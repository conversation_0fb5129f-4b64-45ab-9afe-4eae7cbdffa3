/**
 * FileUploader
 * Copyright (c) 2017 Innostudio.de
 * Website: http://innostudio.de/fileuploader/
 * Version: 1.0.0.4 (21-Mar-2017)
 * Requires: jQuery v1.7.1 or later
 * License: http://innostudio.de/fileuploader/documentation/#license
 */
!function($) {
    "use strict";
    $.fn.fileuploader = function(q) {
        return this.each(function(t, r) {
            var s = $(r)
              , p = null
              , o = null
              , l = null
              , sl = []
              , n = $.extend(!0, {}, $.fn.fileuploader.defaults, q)
              , f = {
                init: function() {
                    return s.closest(".fileuploader").length || s.wrap('<div class="fileuploader"></div>'),
                    p = s.closest(".fileuploader"),
                    f.set("attrOpts"),
                    f.isSupported() ? (!n.beforeRender || !$.isFunction(n.beforeRender) || !1 !== n.beforeRender(p, s)) && (f.redesign(),
                    n.files && f.files.append(n.files),
                    f.rendered = !0,
                    n.afterRender && $.isFunction(n.afterRender) && n.afterRender(l, p, o, s),
                    void (f.disabled || f.bindUnbindEvents(!0))) : (n.onSupportError && $.isFunction(n.onSupportError) && n.onSupportError(p, s),
                    !1)
                },
                bindUnbindEvents: function(a) {
                    a && f.bindUnbindEvents(!1),
                    s[a ? "on" : "off"](f._assets.getAllEvents(), f.onEvent),
                    n.changeInput && o !== s && o[a ? "on" : "off"]("click", f.clickHandler),
                    n.dragDrop && n.dragDrop.container.length && (n.dragDrop.container[a ? "on" : "off"]("drag dragstart dragend dragover dragenter dragleave drop", function(a) {
                        a.preventDefault()
                    }),
                    n.dragDrop.container[a ? "on" : "off"]("drop", f.dragDrop.onDrop),
                    n.dragDrop.container[a ? "on" : "off"]("dragover", f.dragDrop.onDragEnter),
                    n.dragDrop.container[a ? "on" : "off"]("dragleave", f.dragDrop.onDragLeave)),
                    f.isUploadMode() && n.clipboardPaste && $(window)[a ? "on" : "off"]("paste", f.clipboard.paste),
                    s.closest("form")[a ? "on" : "off"]("reset", f.reset)
                },
                redesign: function() {
                    if (o = s,
                    n.theme && p.addClass("fileuploader-theme-" + n.theme),
                    n.changeInput) {
                        switch ((typeof n.changeInput).toLowerCase()) {
                        case "boolean":
                            o = $('<div class="fileuploader-input"><div class="fileuploader-input-caption"><span>' + f._assets.textParse(n.captions.feedback) + '</span></div><div class="fileuploader-input-button"><span>' + f._assets.textParse(n.captions.button) + "</span></div></div>");
                            break;
                        case "string":
                            " " != n.changeInput && (o = $(f._assets.textParse(n.changeInput, n)));
                            break;
                        case "object":
                            o = $(n.changeInput);
                            break;
                        case "function":
                            o = $(n.changeInput(s, p, n, f._assets.textParse))
                        }
                        s.after(o),
                        s.css({
                            position: "absolute",
                            "z-index": "-9999",
                            height: "0",
                            width: "0",
                            padding: "0",
                            margin: "0",
                            "line-height": "0",
                            outline: "0",
                            border: "0",
                            opacity: "0"
                        })
                    }
                    n.thumbnails && f.thumbnails.create(),
                    n.dragDrop && (n.dragDrop = "object" != typeof n.dragDrop ? {
                        container: null
                    } : n.dragDrop,
                    n.dragDrop.container = n.dragDrop.container ? $(n.dragDrop.container) : o)
                },
                clickHandler: function(a) {
                    if (a.preventDefault(),
                    f.clipboard._timer)
                        return void f.clipboard.clean();
                    s.click()
                },
                onEvent: function(a) {
                    switch (a.type) {
                    case "focus":
                        p && p.addClass("fileuploader-focused");
                        break;
                    case "blur":
                        p && p.removeClass("fileuploader-focused");
                        break;
                    case "change":
                        f.onChange.call(this)
                    }
                    n.listeners && $.isFunction(n.listeners[a.type]) && n.listeners[a.type].call(s, p)
                },
                set: function(a, b) {
                    switch (a) {
                    case "attrOpts":
                        for (var c = ["limit", "maxSize", "fileMaxSize", "extensions", "changeInput", "theme", "addMore", "listInput", "files"], d = 0; d < c.length; d++) {
                            var e = "data-fileuploader-" + c[d];
                            if (f._assets.hasAttr(e))
                                switch (c[d]) {
                                case "changeInput":
                                case "addMore":
                                case "listInput":
                                    n[c[d]] = ["true", "false"].indexOf(s.attr(e)) > -1 ? "true" == s.attr(e) : s.attr(e);
                                    break;
                                case "extensions":
                                    n[c[d]] = s.attr(e).replace(/ /g, "").split(",");
                                    break;
                                case "files":
                                    n[c[d]] = JSON.parse(s.attr(e));
                                    break;
                                default:
                                    n[c[d]] = s.attr(e)
                                }
                            s.removeAttr(e)
                        }
                        null == s.attr("disabled") && null == s.attr("readonly") && 0 !== n.limit || (f.disabled = !0),
                        (!n.limit || n.limit && n.limit >= 2) && (s.attr("multiple", "multiple"),
                        n.inputNameBrackets && "[]" != s.attr("name").slice(-2) && s.attr("name", s.attr("name") + "[]")),
                        !0 === n.listInput && (n.listInput = $('<input type="hidden" name="fileuploader-list-' + s.attr("name").replace("[]", "").split("[").pop().replace("]", "") + '">').insertBefore(s)),
                        "string" == typeof n.listInput && 0 == $(n.listInput).length && (n.listInput = $('<input type="hidden" name="' + n.listInput + '">').insertBefore(s)),
                        f.set("disabled", f.disabled),
                        !n.fileMaxSize && n.maxSize && (n.fileMaxSize = n.maxSize);
                        break;
                    case "disabled":
                        f.disabled = b,
                        p[f.disabled ? "addClass" : "removeClass"]("fileuploader-disabled"),
                        s[f.disabled ? "attr" : "removeAttr"]("disabled", "disabled"),
                        f.rendered && f.bindUnbindEvents(!b);
                        break;
                    case "feedback":
                        b || (b = f._assets.textParse(f._itFl.length > 0 ? n.captions.feedback2 : n.captions.feedback, {
                            length: f._itFl.length
                        })),
                        $(!o.is(":file")) && o.find(".fileuploader-input-caption span").html(b);
                        break;
                    case "input":
                        var g = f._assets.copyAllAttributes($('<input type="file">'), s, !0);
                        f.bindUnbindEvents(!1),
                        s.after(s = g).remove(),
                        f.bindUnbindEvents(!0);
                        break;
                    case "prevInput":
                        sl.length > 0 && (f.bindUnbindEvents(!1),
                        sl[b].remove(),
                        sl.splice(b, 1),
                        s = sl[sl.length - 1],
                        f.bindUnbindEvents(!0));
                        break;
                    case "nextInput":
                        var g = f._assets.copyAllAttributes($('<input type="file">'), s);
                        f.bindUnbindEvents(!1),
                        sl.length > 0 && 0 == sl[sl.length - 1].get(0).files.length ? s = sl[sl.length - 1] : (-1 == sl.indexOf(s) && sl.push(s),
                        sl.push(g),
                        s.after(s = g)),
                        f.bindUnbindEvents(!0);
                        break;
                    case "listInput":
                        n.listInput && n.listInput.val(null === b ? f.files.list(!0) : b)
                    }
                },
                onChange: function(a, b) {
                    var c = s.get(0).files;
                    if (b) {
                        if (!b.length)
                            return f.set("input", ""),
                            f.files.clear(),
                            !1;
                        c = b
                    }
                    if (f.clipboard._timer && f.clipboard.clean(),
                    !f.isDefaultMode() || (f.reset(),
                    0 != c.length)) {
                        if (n.beforeSelect && $.isFunction(n.beforeSelect) && 0 == n.beforeSelect(c, l, p, o, s))
                            return !1;
                        for (var d = 0, e = 0; e < c.length; e++) {
                            var g = c[e]
                              , h = f._itFl[f.files.add(g, "choosed")]
                              , i = f.files.check(h, c, 0 == e);
                            if (!0 === i)
                                n.thumbnails && f.thumbnails.item(h),
                                f.isUploadMode() && f.upload.prepare(h),
                                n.onSelect && $.isFunction(n.onSelect) && n.onSelect(h, l, p, o, s),
                                d++;
                            else if (f.files.remove(h, !0),
                            i[2] || (f.isDefaultMode() && (f.set("input", ""),
                            f.reset(),
                            i[3] = !0),
                            i[1] && n.dialogs.alert(i[1])),
                            i[3])
                                break
                        }
                        f.isUploadMode() && d > 0 && f.set("input", ""),
                        f.set("feedback", null),
                        f.isAddMoreMode() && d > 0 && f.set("nextInput"),
                        f.set("listInput", null),
                        n.afterSelect && $.isFunction(n.afterSelect) && n.afterSelect(l, p, o, s)
                    }
                },
                thumbnails: {
                    create: function() {
                        null != n.thumbnails.beforeShow && $.isFunction(n.thumbnails.beforeShow) && n.thumbnails.beforeShow(p, o, s);
                        var a = $(f._assets.textParse(n.thumbnails.box)).appendTo(n.thumbnails.boxAppendTo ? n.thumbnails.boxAppendTo : p);
                        l = a.is(n.thumbnails._selectors.list) ? a : a.find(n.thumbnails._selectors.list),
                        f.isUploadMode() && n.thumbnails._selectors.start && l.on("click", n.thumbnails._selectors.start, function(a) {
                            if (a.preventDefault(),
                            f.locked)
                                return !1;
                            var b = $(this).closest(n.thumbnails._selectors.item)
                              , c = f.files.find(b);
                            c && f.upload.send(c, !0)
                        }),
                        f.isUploadMode() && n.thumbnails._selectors.retry && l.on("click", n.thumbnails._selectors.retry, function(a) {
                            if (a.preventDefault(),
                            f.locked)
                                return !1;
                            var b = $(this).closest(n.thumbnails._selectors.item)
                              , c = f.files.find(b);
                            c && f.upload.retry(c)
                        }),
                        n.thumbnails._selectors.remove && l.on("click", n.thumbnails._selectors.remove, function(a) {
                            if (a.preventDefault(),
                            f.locked)
                                return !1;
                            var b = $(this).closest(n.thumbnails._selectors.item)
                              , c = f.files.find(b)
                              , d = function(a) {
                                f.files.remove(c)
                            };
                            c && (c.upload && "successful" != c.upload.status ? f.upload.cancel(c) : n.thumbnails.removeConfirmation ? n.dialogs.confirm(f._assets.textParse(n.captions.removeConfirmation, c), d) : d())
                        })
                    },
                    clear: function() {
                        l && l.html("")
                    },
                    item: function(a) {
                        a.icon = f.thumbnails.generateFileIcon(a.format, a.extension),
                        a.image = '<div class="fileuploader-item-image fileuploader-loading"></div>',
                        a.progressBar = f.isUploadMode() ? '<div class="fileuploader-progressbar"><div class="bar"></div></div>' : "",
                        a.html = $(f._assets.textParse(a.appended && n.thumbnails.item2 ? n.thumbnails.item2 : n.thumbnails.item, a, !0)),
                        a.progressBar = a.html.find(".fileuploader-progressbar"),
                        a.html.addClass("file-type-" + (a.format ? a.format : "no") + " file-ext-" + (a.extension ? a.extension : "no")),
                        a.html[n.thumbnails.itemPrepend ? "prependTo" : "appendTo"](l),
                        a.renderImage = function() {
                            f.thumbnails.renderImage(a, !0)
                        }
                        ,
                        f.thumbnails.renderImage(a),
                        null != n.thumbnails.onItemShow && $.isFunction(n.thumbnails.onItemShow) && n.thumbnails.onItemShow(a, l, p, o, s)
                    },
                    generateFileIcon: function(a, b) {
                        var c = '<div style="${style}" class="fileuploader-item-icon${class}"><i>' + (b || "") + "</i></div>"
                          , d = f._assets.textToColor(b);
                        if (d) {
                            f._assets.isBrightColor(d) && (c = c.replace("${class}", " is-bright-color")),
                            c = c.replace("${style}", "background-color: " + d)
                        }
                        return c.replace("${style}", "").replace("${class}", "")
                    },
                    renderImage: function(a, b) {
                        var c = a.html.find(".fileuploader-item-image")
                          , d = function(b) {
                            var d = $(b);
                            d.is("img") && d.on("load error", function(b) {
                                "error" == b.type && e(),
                                g(),
                                null != n.thumbnails.onImageLoaded && $.isFunction(n.thumbnails.onImageLoaded) && n.thumbnails.onImageLoaded(a, l, p, o, s)
                            }),
                            d.is("canvas") && null != n.thumbnails.onImageLoaded && $.isFunction(n.thumbnails.onImageLoaded) && n.thumbnails.onImageLoaded(a, l, p, o, s),
                            c.removeClass("fileupload-no-thumbnail fileuploader-loading").html(d)
                        }
                          , e = function() {
                            c.addClass("fileupload-no-thumbnail"),
                            c.removeClass("fileuploader-loading").html(a.icon)
                        }
                          , g = function() {
                            var b = 0;
                            if (a && f._pfrL.indexOf(a) > -1)
                                for (f._pfrL.splice(f._pfrL.indexOf(a), 1); b < f._pfrL.length; ) {
                                    if (f._itFl.indexOf(f._pfrL[b]) > -1) {
                                        f.thumbnails.renderImage(f._pfrL[b], !0);
                                        break
                                    }
                                    f._pfrL.splice(b, 1),
                                    b++
                                }
                        };
                        if (!c.length)
                            return void g();
                        if (a.image = c,
                        "image" == a.format && f.isFileReaderSupported() && (a.appended || n.thumbnails.startImageRenderer || b)) {
                            if (n.thumbnails.synchronImages && (-1 != f._pfrL.indexOf(a) || b || f._pfrL.push(a),
                            f._pfrL.length > 1 && !b))
                                return;
                            var h = new FileReader
                              , i = function(a) {
                                if (n.thumbnails.canvasImage) {
                                    var b = document.createElement("canvas")
                                      , h = b.getContext("2d")
                                      , i = new Image;
                                    i.onload = function() {
                                        var a = n.thumbnails.canvasImage.height ? n.thumbnails.canvasImage.height : c.height()
                                          , j = n.thumbnails.canvasImage.width ? n.thumbnails.canvasImage.width : c.width()
                                          , k = i.height / a
                                          , l = i.width / j
                                          , m = k < l ? k : l
                                          , o = i.height / m
                                          , p = i.width / m
                                          , q = Math.ceil(Math.log(i.width / p) / Math.log(2));
                                        if (b.height = a,
                                        b.width = j,
                                        i.width < b.width || i.height < b.height || q <= 1) {
                                            var r = i.width < b.width ? b.width / 2 - i.width / 2 : i.width > b.width ? -(i.width - b.width) / 2 : 0
                                              , s = i.height < b.height ? b.height / 2 - i.height / 2 : 0;
                                            h.drawImage(i, r, s, i.width, i.height)
                                        } else {
                                            var t = document.createElement("canvas")
                                              , u = t.getContext("2d");
                                            t.width = .5 * i.width,
                                            t.height = .5 * i.height,
                                            u.fillStyle = "#fff",
                                            u.fillRect(0, 0, t.width, t.height),
                                            u.drawImage(i, 0, 0, t.width, t.height),
                                            u.drawImage(t, 0, 0, .5 * t.width, .5 * t.height),
                                            h.drawImage(t, p > b.width ? p - b.width : 0, 0, .5 * t.width, .5 * t.height, 0, 0, p, o)
                                        }
                                        i = null,
                                        f._assets.isBlankCanvas(b) ? e() : d(b),
                                        g()
                                    }
                                    ,
                                    i.onerror = function(a) {
                                        e(),
                                        g()
                                    }
                                    ,
                                    i.src = a.target.result
                                } else
                                    d('<img src="' + a.target.result + '" draggable="false">')
                            };
                            return void ("string" == typeof a.file ? i({
                                target: {
                                    result: a.file
                                }
                            }) : (h.onload = i,
                            h.readAsDataURL(a.file)))
                        }
                        e()
                    }
                },
                upload: {
                    prepare: function(a, b) {
                        a.upload = {
                            url: n.upload.url,
                            data: n.upload.data || {},
                            formData: new FormData,
                            type: n.upload.type || "POST",
                            enctype: n.upload.enctype || "multipart/form-data",
                            cache: !1,
                            contentType: !1,
                            processData: !1,
                            status: null,
                            send: function() {
                                f.upload.send(a, !0)
                            },
                            cancel: function() {
                                f.upload.cancel(a)
                            },
                            retry: function() {
                                f.upload.retry(a)
                            }
                        },
                        a.upload.formData.append(s.attr("name"), a.file, !!a.name && a.name),
                        (n.upload.start || b) && f.upload.send(a, b)
                    },
                    send: function(a, b) {
                        if (a.upload) {
                            var c = function(b) {
                                a.html.removeClass("upload-pending upload-loading upload-cancelled upload-failed upload-success").addClass("upload-" + (b || a.upload.status))
                            }
                              , d = function() {
                                var b = 0;
                                if (f._pfuL.length > 0)
                                    for (f._pfuL.indexOf(a) > -1 && f._pfuL.splice(f._pfuL.indexOf(a), 1); b < f._pfuL.length; ) {
                                        if (f._itFl.indexOf(f._pfuL[b]) > -1 && f._pfuL[b].upload && !f._pfuL[b].upload.$ajax) {
                                            f.upload.send(f._pfuL[b], !0);
                                            break
                                        }
                                        f._pfuL.splice(b, 1),
                                        b++
                                    }
                            };
                            if (n.upload.synchron)
                                if (a.upload.status = "pending",
                                a.html && c(),
                                b)
                                    f._pfuL.indexOf(a) > -1 && f._pfuL.splice(f._pfuL.indexOf(a), 1);
                                else if (-1 == f._pfuL.indexOf(a) && f._pfuL.push(a),
                                f._pfuL.length > 1)
                                    return;
                            if (n.upload.beforeSend && $.isFunction(n.upload.beforeSend) && !1 === n.upload.beforeSend(a, l, p, o, s))
                                return c(),
                                void d();
                            if (p.addClass("fileuploader-is-uploading"),
                            a.upload.$ajax && a.upload.$ajax.abort(),
                            delete a.upload.$ajax,
                            delete a.upload.send,
                            a.upload.status = "loading",
                            a.html && (n.thumbnails._selectors.start && a.html.find(n.thumbnails._selectors.start).remove(),
                            c()),
                            a.upload.data)
                                for (var e in a.upload.data)
                                    a.upload.formData.append(e, a.upload.data[e]);
                            a.upload.data = a.upload.formData,
                            a.upload.xhr = function() {
                                var b = $.ajaxSettings.xhr()
                                  , c = new Date;
                                return b.upload && b.upload.addEventListener("progress", function(b) {
                                    f.upload.progressHandling(b, a, c)
                                }, !1),
                                b
                            }
                            ,
                            a.upload.complete = function(a, b) {
                                d();
                                var c = !0;
                                $.each(f._itFl, function(a, b) {
                                    b.upload && b.upload.$ajax && (c = !1)
                                }),
                                c && (p.removeClass("fileuploader-is-uploading"),
                                null != n.upload.onComplete && "function" == typeof n.upload.onComplete && n.upload.onComplete(l, p, o, s, a, b))
                            }
                            ,
                            a.upload.success = function(b, d, e) {
                                a.uploaded = !0,
                                delete a.upload,
                                a.upload = {
                                    status: "successful"
                                },
                                a.html && c(),
                                f.set("listInput", null),
                                null != n.upload.onSuccess && $.isFunction(n.upload.onSuccess) && n.upload.onSuccess(b, a, l, p, o, s, d, e)
                            }
                            ,
                            a.upload.error = function(b, d, e) {
                                a.uploaded = !1,
                                a.upload.status = "cancelled" == a.upload.status ? a.upload.status : "failed",
                                delete a.upload.$ajax,
                                a.html && c(),
                                null != n.upload.onError && $.isFunction(n.upload.onError) && n.upload.onError(a, l, p, o, s, b, d, e)
                            }
                            ,
                            a.upload.$ajax = $.ajax(a.upload)
                        }
                    },
                    cancel: function(a) {
                        a && a.upload && (a.upload.status = "cancelled",
                        a.upload.$ajax && a.upload.$ajax.abort(),
                        delete a.upload.$ajax,
                        f.files.remove(a))
                    },
                    retry: function(a) {
                        a && a.upload && (a.html && n.thumbnails._selectors.retry && a.html.find(n.thumbnails._selectors.retry).remove(),
                        f.upload.prepare(a, !0))
                    },
                    progressHandling: function(a, b, c) {
                        if (a.lengthComputable) {
                            var d = a.loaded
                              , e = a.total
                              , g = Math.round(100 * d / e)
                              , h = ((new Date).getTime() - c.getTime()) / 1e3
                              , i = h ? d / h : 0
                              , j = e - d
                              , k = h ? j / i : null
                              , m = {
                                loaded: d,
                                loadedInFormat: f._assets.bytesToText(d),
                                total: e,
                                totalInFormat: f._assets.bytesToText(e),
                                percentage: g,
                                secondsElapsed: h,
                                secondsElapsedInFormat: f._assets.secondsToText(h, !0),
                                bytesPerSecond: i,
                                bytesPerSecondInFormat: f._assets.bytesToText(i) + "/s",
                                remainingBytes: j,
                                remainingBytesInFormat: f._assets.bytesToText(j),
                                secondsRemaining: k,
                                secondsRemainingInFormat: f._assets.secondsToText(k, !0)
                            };
                            n.upload.onProgress && $.isFunction(n.upload.onProgress) && n.upload.onProgress(m, b, l, p, o, s)
                        }
                    }
                },
                dragDrop: {
                    onDragEnter: function(a) {
                        clearTimeout(f.dragDrop._timer),
                        n.dragDrop.container.addClass("fileuploader-dragging"),
                        f.set("feedback", f._assets.textParse(n.captions.drop)),
                        null != n.dragDrop.onDragEnter && $.isFunction(n.dragDrop.onDragEnter) && n.dragDrop.onDragEnter(a, l, p, o, s)
                    },
                    onDragLeave: function(a) {
                        clearTimeout(f.dragDrop._timer),
                        f.dragDrop._timer = setTimeout(function(a) {
                            if (!f.dragDrop._dragLeaveCheck(a))
                                return !1;
                            n.dragDrop.container.removeClass("fileuploader-dragging"),
                            f.set("feedback", null),
                            null != n.dragDrop.onDragLeave && $.isFunction(n.dragDrop.onDragLeave) && n.dragDrop.onDragLeave(a, l, p, o, s)
                        }, 100, a)
                    },
                    onDrop: function(a) {
                        clearTimeout(f.dragDrop._timer),
                        n.dragDrop.container.removeClass("fileuploader-dragging"),
                        f.set("feedback", null),
                        a && a.originalEvent && a.originalEvent.dataTransfer && a.originalEvent.dataTransfer.files && a.originalEvent.dataTransfer.files.length && (f.isUploadMode() ? f.onChange(a, a.originalEvent.dataTransfer.files) : s.prop("files", a.originalEvent.dataTransfer.files)),
                        null != n.dragDrop.onDrop && $.isFunction(n.dragDrop.onDrop) && n.dragDrop.onDrop(a, l, p, o, s)
                    },
                    _dragLeaveCheck: function(a) {
                        var c, b = $(a.currentTarget);
                        return !(!b.is(n.dragDrop.container) && (c = n.dragDrop.container.find(b),
                        c.length))
                    }
                },
                clipboard: {
                    paste: function(a) {
                        if (f._assets.isIntoView(o) && a.originalEvent.clipboardData && a.originalEvent.clipboardData.items && a.originalEvent.clipboardData.items.length) {
                            var b = a.originalEvent.clipboardData.items;
                            f.clipboard.clean();
                            for (var c = 0; c < b.length; c++)
                                if (-1 !== b[c].type.indexOf("image") || -1 !== b[c].type.indexOf("text/uri-list")) {
                                    var d = b[c].getAsFile()
                                      , e = new Date
                                      , g = function(a) {
                                        return a < 10 && (a = "0" + a),
                                        a
                                    }
                                      , h = n.clipboardPaste > 1 ? n.clipboardPaste : 2e3;
                                    d && (d.name = "Clipboard " + e.getFullYear() + "-" + g(e.getMonth() + 1) + "-" + g(e.getDate()) + " " + g(e.getHours()) + "-" + g(e.getMinutes()) + "-" + g(e.getSeconds()),
                                    d.name += -1 != d.type.indexOf("/") ? "." + d.type.split("/")[1].toString().toLowerCase() : ".png",
                                    f.set("feedback", f._assets.textParse(n.captions.paste, {
                                        ms: h / 1e3
                                    })),
                                    f.clipboard._timer = setTimeout(function() {
                                        f.set("feedback", null),
                                        f.onChange(a, [d])
                                    }, h - 2))
                                }
                        }
                    },
                    clean: function() {
                        f.clipboard._timer && (clearTimeout(f.clipboard._timer),
                        delete f.clipboard._timer,
                        f.set("feedback", null))
                    }
                },
                files: {
                    add: function(a, b) {
                        var l, m, c = a.name, d = a.size, e = f._assets.bytesToText(d), g = a.type, h = g ? g.split("/", 1).toString().toLowerCase() : "", i = -1 != c.indexOf(".") ? c.split(".").pop().toLowerCase() : "", j = c.substr(0, c.length - (-1 != c.indexOf(".") ? i.length + 1 : i.length)), k = a.data, a = a.file || a;
                        return f._itFl.push({
                            name: c,
                            title: j,
                            size: d,
                            size2: e,
                            type: g,
                            format: h,
                            extension: i,
                            data: k,
                            file: a,
                            input: "choosed" == b ? s : null,
                            html: null,
                            upload: null,
                            choosed: "choosed" == b,
                            appended: "appended" == b,
                            uploaded: "uploaded" == b
                        }),
                        l = f._itFl.length - 1,
                        m = f._itFl[l],
                        m.remove = function() {
                            f.files.remove(m)
                        }
                        ,
                        l
                    },
                    list: function(a, b, c) {
                        var d = [];
                        return $.each(f._itFl, function(c, e) {
                            if (!e.upload || e.uploaded) {
                                var g = e;
                                (a || b) && (g = (g.choosed ? "0:/" : "") + (b && null !== f.files.getItemAttr(e, b) ? f.files.getItemAttr(e, b) : e["string" == typeof e.file ? "file" : "name"])),
                                d.push(g)
                            }
                        }),
                        d = n.onListInput && $.isFunction(n.onListInput) && !c ? n.onListInput(f._itFl, n.listInput, l, p, o, s) : d,
                        a ? JSON.stringify(d) : d
                    },
                    check: function(a, b, c) {
                        var d = ["warning", null, !1, !1];
                        if (null != n.limit && c && b.length + f._itFl.length - 1 > n.limit)
                            return d[1] = f._assets.textParse(n.captions.errors.filesLimit),
                            d[3] = !0,
                            d;
                        if (null != n.maxSize && c) {
                            var e = 0;
                            if ($.each(f._itFl, function(a, b) {
                                e += b.size
                            }),
                            e -= a.size,
                            $.each(b, function(a, b) {
                                e += b.size
                            }),
                            e > Math.round(1e6 * n.maxSize))
                                return d[1] = f._assets.textParse(n.captions.errors.filesSizeAll),
                                d[3] = !0,
                                d
                        }
                        if (null != n.onFilesCheck && $.isFunction(n.onFilesCheck) && c) {
                            if (!1 === n.onFilesCheck(b, n, l, p, o, s))
                                return d[3] = !0,
                                d
                        }
                        if (null != n.extensions && -1 == $.inArray(a.extension, n.extensions) && -1 == $.inArray(a.type, n.extensions))
                            return d[1] = f._assets.textParse(n.captions.errors.filesType, a),
                            d;
                        if (null != n.fileMaxSize && a.size > 1e6 * n.fileMaxSize)
                            return d[1] = f._assets.textParse(n.captions.errors.fileSize, a),
                            d;
                        if (4096 == a.size && "" == a.type)
                            return d[1] = f._assets.textParse(n.captions.errors.folderUpload, a),
                            d;
                        var e = !1;
                        return $.each(f._itFl, function(b, c) {
                            if (c != a && 1 == c.choosed && c.file && c.file.name == a.name)
                                return e = !0,
                                c.file.size != a.size || c.file.type != a.type || a.file.lastModified && c.file.lastModified && c.file.lastModified != a.file.lastModified ? (d[1] = f._assets.textParse(n.captions.errors.fileName, a),
                                d[2] = !1) : d[2] = !0,
                                !1
                        }),
                        !e || d
                    },
                    append: function(a) {
                        if (a = $.isArray(a) ? a : [a],
                        a.length) {
                            for (var b, c = 0; c < a.length; c++)
                                b = f._itFl[f.files.add(a[c], "appended")],
                                n.thumbnails && f.thumbnails.item(b);
                            return f.set("feedback", null),
                            f.set("listInput", null),
                            n.afterSelect && $.isFunction(n.afterSelect) && n.afterSelect(l, p, o, s),
                            1 != a.length || b
                        }
                    },
                    find: function(a) {
                        var b = null;
                        return $.each(f._itFl, function(c, d) {
                            if (d.html && d.html.is(a))
                                return b = d,
                                !1
                        }),
                        b
                    },
                    remove: function(a, b) {
                        if (b || !n.onRemove || !$.isFunction(n.onRemove) || !1 !== n.onRemove(a, l, p, o, s)) {
                            if (a.html && (n.thumbnails.onItemRemove && $.isFunction(n.thumbnails.onItemRemove) && !b ? n.thumbnails.onItemRemove(a.html, l, p, o, s) : a.html.remove()),
                            a.upload && a.upload.$ajax && a.upload.cancel && a.upload.cancel(),
                            a.input) {
                                var c = !0;
                                $.each(f._itFl, function(d, e) {
                                    if (a != e && (a.input == e.input || b && a.input.get(0).files.length > 1))
                                        return c = !1,
                                        !1
                                }),
                                c && (f.isAddMoreMode() && sl.length > 1 ? (sl.splice(sl.indexOf(a.input), 1),
                                a.input.remove(),
                                f.set("nextInput")) : f.set("input", ""))
                            }
                            f._pfrL.indexOf(a) > -1 && f._pfrL.splice(f._pfrL.indexOf(a), 1),
                            f._pfuL.indexOf(a) > -1 && f._pfuL.splice(f._pfuL.indexOf(a), 1),
                            f._itFl.indexOf(a) > -1 && f._itFl.splice(f._itFl.indexOf(a), 1),
                            a = null,
                            0 == f._itFl.length && f.reset(),
                            f.set("feedback", null),
                            f.set("listInput", null)
                        }
                    },
                    getItemAttr: function(a, b) {
                        var c = null;
                        return a && (void 0 !== a[b] ? c = a[b] : a.data && void 0 !== a.data[b] && (c = a.data[b])),
                        c
                    },
                    clear: function(a) {
                        for (var b = 0; b < f._itFl.length; ) {
                            var c = f._itFl[b];
                            a || !c.appended ? (c.html && c.html && f._itFl[b].html.remove(),
                            c.upload && c.upload.$ajax && f.upload.cancel(c),
                            f._itFl.splice(b, 1)) : b++
                        }
                        f.set("feedback", null),
                        f.set("listInput", null),
                        0 == f._itFl.length && n.onEmpty && $.isFunction(n.onEmpty) && n.onEmpty(l, p, o, s)
                    }
                },
                reset: function(a) {
                    a && (f.clipboard._timer && f.clipboard.clean(),
                    $.each(sl, function(a, b) {
                        a < sl.length && b.remove()
                    }),
                    sl = [],
                    f.set("input", "")),
                    f._itRl = [],
                    f._pfuL = [],
                    f._pfrL = [],
                    f.files.clear(a)
                },
                destroy: function() {
                    f.reset(!0),
                    f.bindUnbindEvents(!1),
                    s.removeAttr("style"),
                    s.insertBefore(".fileuploader"),
                    s.prop("FileUploader", null),
                    p.remove(),
                    p = o = l = null
                },
                _assets: {
                    bytesToText: function(a) {
                        if (0 == a)
                            return "0 Byte";
                        var b = 1e3
                          , c = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]
                          , d = Math.floor(Math.log(a) / Math.log(b));
                        return (a / Math.pow(b, d)).toPrecision(3) + " " + c[d]
                    },
                    secondsToText: function(a, b) {
                        a = parseInt(Math.round(a), 10);
                        var c = Math.floor(a / 3600)
                          , d = Math.floor((a - 3600 * c) / 60)
                          , a = a - 3600 * c - 60 * d
                          , e = "";
                        return (c > 0 || !b) && (e += (c < 10 ? "0" : "") + c + (b ? "h " : ":")),
                        (d > 0 || !b) && (e += (d < 10 && !b ? "0" : "") + d + (b ? "m " : ":")),
                        e += (a < 10 && !b ? "0" : "") + a + (b ? "s" : "")
                    },
                    hasAttr: function(a, b) {
                        var b = b || s
                          , c = b.attr(a);
                        return !(!c || void 0 === c)
                    },
                    copyAllAttributes: function(a, b) {
                        return $.each(b.get(0).attributes, function() {
                            "required" != this.name && "type" != this.name && a.attr(this.name, this.value)
                        }),
                        b.get(0).FileUploader && (a.get(0).FileUploader = b.get(0).FileUploader),
                        a
                    },
                    getAllEvents: function(a) {
                        var a = a || s
                          , b = [];
                        a = a.get ? a.get(0) : a;
                        for (var c in a)
                            0 === c.indexOf("on") && b.push(c.slice(2));
                        return -1 == b.indexOf("change") && b.push("change"),
                        b.join(" ")
                    },
                    isIntoView: function(a) {
                        var b = $(window).scrollTop()
                          , c = b + window.innerHeight
                          , d = a.offset().top
                          , e = d + a.outerHeight();
                        return b < d && c > e
                    },
                    isBlankCanvas: function(a) {
                        var b = document.createElement("canvas")
                          , c = !1;
                        return b.width = a.width,
                        b.height = a.height,
                        c = a.toDataURL() == b.toDataURL(),
                        b = null,
                        c
                    },
                    textParse: function(text, opts, noOptions) {
                        switch (opts = noOptions ? opts || {} : $.extend({}, {
                            limit: n.limit,
                            maxSize: n.maxSize,
                            fileMaxSize: n.fileMaxSize,
                            extensions: n.extensions ? n.extensions.join(", ") : null
                        }, opts),
                        typeof text) {
                        case "string":
                            text = text.replace(/\$\{(.*?)\}/g, function(match, a) {
                                var a = a.replace(/ /g, "")
                                  , r = void 0 !== opts[a] && null != opts[a] ? opts[a] : "";
                                if (a.indexOf(".") > -1 || a.indexOf("[]") > -1) {
                                    var x = a.substr(0, a.indexOf(".") > -1 ? a.indexOf(".") : a.indexOf("[") > -1 ? a.indexOf("[") : a.length)
                                      , y = a.substring(x.length);
                                    if (opts[x])
                                        try {
                                            r = eval('opts["' + x + '"]' + y)
                                        } catch (a) {
                                            r = ""
                                        }
                                }
                                return (r = $.isFunction(r) ? f._assets.textParse(r) : r) || ""
                            });
                            break;
                        case "function":
                            text = text(opts, l, p, o, s)
                        }
                        return opts = null,
                        text
                    },
                    textToColor: function(a) {
                        if (!a || 0 == a.length)
                            return !1;
                        for (var b = 0, c = 0; b < a.length; c = a.charCodeAt(b++) + ((c << 5) - c))
                            ;
                        for (var b = 0, d = "#"; b < 3; d += ("00" + (c >> 2 * b++ & 255).toString(16)).slice(-2))
                            ;
                        return d
                    },
                    isBrightColor: function(a) {
                        var b = function(a) {
                            var b;
                            return a && a.constructor == Array && 3 == a.length ? a : (b = /rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/.exec(a)) ? [parseInt(b[1]), parseInt(b[2]), parseInt(b[3])] : (b = /rgb\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*\)/.exec(a)) ? [2.55 * parseFloat(b[1]), 2.55 * parseFloat(b[2]), 2.55 * parseFloat(b[3])] : (b = /#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(a)) ? [parseInt(b[1], 16), parseInt(b[2], 16), parseInt(b[3], 16)] : (b = /#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])/.exec(a)) ? [parseInt(b[1] + b[1], 16), parseInt(b[2] + b[2], 16), parseInt(b[3] + b[3], 16)] : "undefined" != typeof colors ? colors[$.trim(a).toLowerCase()] : null
                        };
                        return function(a) {
                            var c = b(a);
                            return c ? .2126 * c[0] + .7152 * c[1] + .0722 * c[2] : null
                        }(a) > 194
                    }
                },
                isSupported: function() {
                    return s && s.get(0).files
                },
                isFileReaderSupported: function() {
                    return window.File && window.FileList && window.FileReader
                },
                isDefaultMode: function() {
                    return !n.upload && !n.addMore
                },
                isAddMoreMode: function() {
                    return !n.upload && n.addMore
                },
                isUploadMode: function() {
                    return n.upload
                },
                _itFl: [],
                _pfuL: [],
                _pfrL: [],
                disabled: !1,
                locked: !1,
                rendered: !1
            };
            return n.enableApi && s.prop("FileUploader", {
                open: function() {
                    s.trigger("click")
                },
                getOptions: function() {
                    return n
                },
                getParentEl: function() {
                    return p
                },
                getInputEl: function() {
                    return s
                },
                getNewInputEl: function() {
                    return o
                },
                getListEl: function() {
                    return l
                },
                getListInputEl: function() {
                    return n.listInput
                },
                getFiles: function() {
                    return f._itFl
                },
                getChoosedFiles: function() {
                    return f._itFl.filter(function(a) {
                        return a.choosed
                    })
                },
                getAppendedFiles: function() {
                    return f._itFl.filter(function(a) {
                        return a.appended
                    })
                },
                getUploadedFiles: function() {
                    return f._itFl.filter(function(a) {
                        return a.uploaded
                    })
                },
                getFileList: function(a, b) {
                    return f.files.list(a, b, !0)
                },
                setOption: function(a, b) {
                    return n[a] = b,
                    !0
                },
                findFile: function(a) {
                    return f.files.find(a)
                },
                append: function(a) {
                    return f.files.append(a)
                },
                remove: function(a) {
                    return a = a.jquery ? f.files.find(a) : a,
                    f._itFl.indexOf(a) > -1 && (f.files.remove(a),
                    !0)
                },
                reset: function() {
                    return f.reset(!0),
                    !0
                },
                disable: function(a) {
                    return f.set("disabled", !0),
                    a && (f.locked = !0),
                    !0
                },
                enable: function() {
                    return f.set("disabled", !1),
                    f.locked = !1,
                    !0
                },
                destroy: function() {
                    return f.destroy(),
                    !0
                },
                isEmpty: function() {
                    return 0 == f._itFl.length
                },
                isDisabled: function() {
                    return f.disabled
                },
                isRendered: function() {
                    return f.rendered
                },
                assets: f._assets,
                getPluginMode: function() {
                    return f.isDefaultMode() ? "default" : f.isAddMoreMode() ? "addMore" : f.isUploadMode() ? "upload" : void 0
                }
            }),
            f.init(),
            this
        })
    }
    ,
    window.$.fileuploader = {
        getInstance: function(a) {
            return (a.prop ? a : $(a)).prop("FileUploader")
        }
    },
    $.fn.fileuploader.defaults = {
        limit: null,
        maxSize: null,
        fileMaxSize: null,
        extensions: null,
        changeInput: !0,
        inputNameBrackets: !0,
        theme: "default",
        thumbnails: {
            box: '<div class="fileuploader-items"><ul class="fileuploader-items-list"></ul></div>',
            boxAppendTo: null,
            item: '<li class="fileuploader-item"><div class="columns"><div class="column-thumbnail">${image}</div><div class="column-title"><div title="${name}">${name}</div><span>${size2}</span></div><div class="column-actions"><a class="fileuploader-action fileuploader-action-remove" title="Remove"><i></i></a></div></div><div class="progress-bar2">${progressBar}<span></span></div></li>',
            item2: '<li class="fileuploader-item"><div class="columns"><a href="${file}" target="_blank"><div class="column-thumbnail">${image}</div><div class="column-title"><div title="${name}">${name}</div><span>${size2}</span></div></a><div class="column-actions"><a href="${file}" class="fileuploader-action fileuploader-action-download" title="Download" download><i></i></a><a class="fileuploader-action fileuploader-action-remove" title="Remove"><i></i></a></div></div></li>',
            itemPrepend: !1,
            removeConfirmation: !0,
            startImageRenderer: !0,
            synchronImages: !0,
            canvasImage: !0,
            _selectors: {
                list: ".fileuploader-items-list",
                item: ".fileuploader-item",
                start: ".fileuploader-action-start",
                retry: ".fileuploader-action-retry",
                remove: ".fileuploader-action-remove"
            },
            beforeShow: null,
            onItemShow: null,
            onItemRemove: function(a) {
                a.children().animate({
                    opacity: 0
                }, 200, function() {
                    setTimeout(function() {
                        a.slideUp(200, function() {
                            a.remove()
                        })
                    }, 100)
                })
            },
            onImageLoaded: null
        },
        files: null,
        upload: null,
        dragDrop: !0,
        addMore: !1,
        clipboardPaste: !0,
        listInput: !0,
        enableApi: !1,
        listeners: null,
        onSupportError: null,
        beforeRender: null,
        afterRender: null,
        beforeSelect: null,
        onFilesCheck: null,
        onSelect: null,
        afterSelect: null,
        onListInput: null,
        onRemove: null,
        onEmpty: null,
        dialogs: {
            alert: function(a) {
                return alert(a)
            },
            confirm: function(a, b) {
                confirm(a) && b()
            }
        },
        captions: {
            button: function(a) {
                //return "Choose"
                return ST_FILEUPLOADERCHOOSE
            },
            feedback: function(a) {
                //return "Choose " + (1 == a.limit ? "file" : "files") + " to upload"
                return ST_FILEUPLOADERSELECT
            },
            feedback2: function(a) {
                //return a.length + " " + (a.length > 1 ? " files were" : " file was") + " chosen"
                return ST_FILEUPLOADERYOUHAVE + " " + a.length + " " + (a.length > 1 ? ST_FILEUPLOADERFILES : ST_FILEUPLOADERFILE)
            },
            drop: "Drop the files here to Upload",
            paste: '<div class="fileuploader-pending-loader"><div class="left-half" style="animation-duration: ${ms}s"></div><div class="spinner" style="animation-duration: ${ms}s"></div><div class="right-half" style="animation-duration: ${ms}s"></div></div> Pasting a file, click here to cancel.',
            removeConfirmation: ST_FILEUPLOADERDELETEALERT,
            errors: {
                filesLimit: "Only ${limit} files are allowed to be uploaded.",
                filesType: "Only ${extensions} files are allowed to be uploaded.",
                fileSize: "${name} is too large! Please choose a file up to ${fileMaxSize}MB.",
                filesSizeAll: "Files that you choosed are too large! Please upload files up to ${maxSize} MB.",
                fileName: "File with the name ${name} is already selected.",
                folderUpload: "You are not allowed to upload folders."
            }
        }
    }
}(jQuery);
