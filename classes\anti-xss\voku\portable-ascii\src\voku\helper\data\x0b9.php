<?php

return [
    'ruk',    // 0x00
    'rut',    // 0x01
    'rup',    // 0x02
    'ruh',    // 0x03
    'rweo',    // 0x04
    'rweog',    // 0x05
    'rweogg',    // 0x06
    'rweogs',    // 0x07
    'rweon',    // 0x08
    'rweonj',    // 0x09
    'rweonh',    // 0x0a
    'rweod',    // 0x0b
    'rweol',    // 0x0c
    'rweolg',    // 0x0d
    'rweolm',    // 0x0e
    'rweolb',    // 0x0f
    'rweols',    // 0x10
    'rweolt',    // 0x11
    'rweolp',    // 0x12
    'rweolh',    // 0x13
    'rweom',    // 0x14
    'rweob',    // 0x15
    'rweobs',    // 0x16
    'rweos',    // 0x17
    'rweoss',    // 0x18
    'rweong',    // 0x19
    'rweoj',    // 0x1a
    'rweoc',    // 0x1b
    'rweok',    // 0x1c
    'rweot',    // 0x1d
    'rweop',    // 0x1e
    'rweoh',    // 0x1f
    'rwe',    // 0x20
    'rweg',    // 0x21
    'rwegg',    // 0x22
    'rwegs',    // 0x23
    'rwen',    // 0x24
    'rwenj',    // 0x25
    'rwenh',    // 0x26
    'rwed',    // 0x27
    'rwel',    // 0x28
    'rwelg',    // 0x29
    'rwelm',    // 0x2a
    'rwelb',    // 0x2b
    'rwels',    // 0x2c
    'rwelt',    // 0x2d
    'rwelp',    // 0x2e
    'rwelh',    // 0x2f
    'rwem',    // 0x30
    'rweb',    // 0x31
    'rwebs',    // 0x32
    'rwes',    // 0x33
    'rwess',    // 0x34
    'rweng',    // 0x35
    'rwej',    // 0x36
    'rwec',    // 0x37
    'rwek',    // 0x38
    'rwet',    // 0x39
    'rwep',    // 0x3a
    'rweh',    // 0x3b
    'rwi',    // 0x3c
    'rwig',    // 0x3d
    'rwigg',    // 0x3e
    'rwigs',    // 0x3f
    'rwin',    // 0x40
    'rwinj',    // 0x41
    'rwinh',    // 0x42
    'rwid',    // 0x43
    'rwil',    // 0x44
    'rwilg',    // 0x45
    'rwilm',    // 0x46
    'rwilb',    // 0x47
    'rwils',    // 0x48
    'rwilt',    // 0x49
    'rwilp',    // 0x4a
    'rwilh',    // 0x4b
    'rwim',    // 0x4c
    'rwib',    // 0x4d
    'rwibs',    // 0x4e
    'rwis',    // 0x4f
    'rwiss',    // 0x50
    'rwing',    // 0x51
    'rwij',    // 0x52
    'rwic',    // 0x53
    'rwik',    // 0x54
    'rwit',    // 0x55
    'rwip',    // 0x56
    'rwih',    // 0x57
    'ryu',    // 0x58
    'ryug',    // 0x59
    'ryugg',    // 0x5a
    'ryugs',    // 0x5b
    'ryun',    // 0x5c
    'ryunj',    // 0x5d
    'ryunh',    // 0x5e
    'ryud',    // 0x5f
    'ryul',    // 0x60
    'ryulg',    // 0x61
    'ryulm',    // 0x62
    'ryulb',    // 0x63
    'ryuls',    // 0x64
    'ryult',    // 0x65
    'ryulp',    // 0x66
    'ryulh',    // 0x67
    'ryum',    // 0x68
    'ryub',    // 0x69
    'ryubs',    // 0x6a
    'ryus',    // 0x6b
    'ryuss',    // 0x6c
    'ryung',    // 0x6d
    'ryuj',    // 0x6e
    'ryuc',    // 0x6f
    'ryuk',    // 0x70
    'ryut',    // 0x71
    'ryup',    // 0x72
    'ryuh',    // 0x73
    'reu',    // 0x74
    'reug',    // 0x75
    'reugg',    // 0x76
    'reugs',    // 0x77
    'reun',    // 0x78
    'reunj',    // 0x79
    'reunh',    // 0x7a
    'reud',    // 0x7b
    'reul',    // 0x7c
    'reulg',    // 0x7d
    'reulm',    // 0x7e
    'reulb',    // 0x7f
    'reuls',    // 0x80
    'reult',    // 0x81
    'reulp',    // 0x82
    'reulh',    // 0x83
    'reum',    // 0x84
    'reub',    // 0x85
    'reubs',    // 0x86
    'reus',    // 0x87
    'reuss',    // 0x88
    'reung',    // 0x89
    'reuj',    // 0x8a
    'reuc',    // 0x8b
    'reuk',    // 0x8c
    'reut',    // 0x8d
    'reup',    // 0x8e
    'reuh',    // 0x8f
    'ryi',    // 0x90
    'ryig',    // 0x91
    'ryigg',    // 0x92
    'ryigs',    // 0x93
    'ryin',    // 0x94
    'ryinj',    // 0x95
    'ryinh',    // 0x96
    'ryid',    // 0x97
    'ryil',    // 0x98
    'ryilg',    // 0x99
    'ryilm',    // 0x9a
    'ryilb',    // 0x9b
    'ryils',    // 0x9c
    'ryilt',    // 0x9d
    'ryilp',    // 0x9e
    'ryilh',    // 0x9f
    'ryim',    // 0xa0
    'ryib',    // 0xa1
    'ryibs',    // 0xa2
    'ryis',    // 0xa3
    'ryiss',    // 0xa4
    'rying',    // 0xa5
    'ryij',    // 0xa6
    'ryic',    // 0xa7
    'ryik',    // 0xa8
    'ryit',    // 0xa9
    'ryip',    // 0xaa
    'ryih',    // 0xab
    'ri',    // 0xac
    'rig',    // 0xad
    'rigg',    // 0xae
    'rigs',    // 0xaf
    'rin',    // 0xb0
    'rinj',    // 0xb1
    'rinh',    // 0xb2
    'rid',    // 0xb3
    'ril',    // 0xb4
    'rilg',    // 0xb5
    'rilm',    // 0xb6
    'rilb',    // 0xb7
    'rils',    // 0xb8
    'rilt',    // 0xb9
    'rilp',    // 0xba
    'rilh',    // 0xbb
    'rim',    // 0xbc
    'rib',    // 0xbd
    'ribs',    // 0xbe
    'ris',    // 0xbf
    'riss',    // 0xc0
    'ring',    // 0xc1
    'rij',    // 0xc2
    'ric',    // 0xc3
    'rik',    // 0xc4
    'rit',    // 0xc5
    'rip',    // 0xc6
    'rih',    // 0xc7
    'ma',    // 0xc8
    'mag',    // 0xc9
    'magg',    // 0xca
    'mags',    // 0xcb
    'man',    // 0xcc
    'manj',    // 0xcd
    'manh',    // 0xce
    'mad',    // 0xcf
    'mal',    // 0xd0
    'malg',    // 0xd1
    'malm',    // 0xd2
    'malb',    // 0xd3
    'mals',    // 0xd4
    'malt',    // 0xd5
    'malp',    // 0xd6
    'malh',    // 0xd7
    'mam',    // 0xd8
    'mab',    // 0xd9
    'mabs',    // 0xda
    'mas',    // 0xdb
    'mass',    // 0xdc
    'mang',    // 0xdd
    'maj',    // 0xde
    'mac',    // 0xdf
    'mak',    // 0xe0
    'mat',    // 0xe1
    'map',    // 0xe2
    'mah',    // 0xe3
    'mae',    // 0xe4
    'maeg',    // 0xe5
    'maegg',    // 0xe6
    'maegs',    // 0xe7
    'maen',    // 0xe8
    'maenj',    // 0xe9
    'maenh',    // 0xea
    'maed',    // 0xeb
    'mael',    // 0xec
    'maelg',    // 0xed
    'maelm',    // 0xee
    'maelb',    // 0xef
    'maels',    // 0xf0
    'maelt',    // 0xf1
    'maelp',    // 0xf2
    'maelh',    // 0xf3
    'maem',    // 0xf4
    'maeb',    // 0xf5
    'maebs',    // 0xf6
    'maes',    // 0xf7
    'maess',    // 0xf8
    'maeng',    // 0xf9
    'maej',    // 0xfa
    'maec',    // 0xfb
    'maek',    // 0xfc
    'maet',    // 0xfd
    'maep',    // 0xfe
    'maeh',    // 0xff
];
