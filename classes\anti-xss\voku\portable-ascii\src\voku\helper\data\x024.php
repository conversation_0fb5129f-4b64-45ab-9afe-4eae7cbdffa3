<?php

return [
    '',    // 0x00
    '',    // 0x01
    '',    // 0x02
    '',    // 0x03
    '',    // 0x04
    '',    // 0x05
    '',    // 0x06
    '',    // 0x07
    '',    // 0x08
    '',    // 0x09
    '',    // 0x0a
    '',    // 0x0b
    '',    // 0x0c
    '',    // 0x0d
    '',    // 0x0e
    '',    // 0x0f
    '',    // 0x10
    '',    // 0x11
    '',    // 0x12
    '',    // 0x13
    '',    // 0x14
    '',    // 0x15
    '',    // 0x16
    '',    // 0x17
    '',    // 0x18
    '',    // 0x19
    '',    // 0x1a
    '',    // 0x1b
    '',    // 0x1c
    '',    // 0x1d
    '',    // 0x1e
    '',    // 0x1f
    '',    // 0x20
    '',    // 0x21
    '',    // 0x22
    '',    // 0x23
    '',    // 0x24
    '',    // 0x25
    '',    // 0x26
    '[?]',    // 0x27
    '[?]',    // 0x28
    '[?]',    // 0x29
    '[?]',    // 0x2a
    '[?]',    // 0x2b
    '[?]',    // 0x2c
    '[?]',    // 0x2d
    '[?]',    // 0x2e
    '[?]',    // 0x2f
    '[?]',    // 0x30
    '[?]',    // 0x31
    '[?]',    // 0x32
    '[?]',    // 0x33
    '[?]',    // 0x34
    '[?]',    // 0x35
    '[?]',    // 0x36
    '[?]',    // 0x37
    '[?]',    // 0x38
    '[?]',    // 0x39
    '[?]',    // 0x3a
    '[?]',    // 0x3b
    '[?]',    // 0x3c
    '[?]',    // 0x3d
    '[?]',    // 0x3e
    '[?]',    // 0x3f
    '',    // 0x40
    '',    // 0x41
    '',    // 0x42
    '',    // 0x43
    '',    // 0x44
    '',    // 0x45
    '',    // 0x46
    '',    // 0x47
    '',    // 0x48
    '',    // 0x49
    '',    // 0x4a
    '[?]',    // 0x4b
    '[?]',    // 0x4c
    '[?]',    // 0x4d
    '[?]',    // 0x4e
    '[?]',    // 0x4f
    '[?]',    // 0x50
    '[?]',    // 0x51
    '[?]',    // 0x52
    '[?]',    // 0x53
    '[?]',    // 0x54
    '[?]',    // 0x55
    '[?]',    // 0x56
    '[?]',    // 0x57
    '[?]',    // 0x58
    '[?]',    // 0x59
    '[?]',    // 0x5a
    '[?]',    // 0x5b
    '[?]',    // 0x5c
    '[?]',    // 0x5d
    '[?]',    // 0x5e
    '[?]',    // 0x5f
    '1',    // 0x60
    '2',    // 0x61
    '3',    // 0x62
    '4',    // 0x63
    '5',    // 0x64
    '6',    // 0x65
    '7',    // 0x66
    '8',    // 0x67
    '9',    // 0x68
    '10',    // 0x69
    '11',    // 0x6a
    '12',    // 0x6b
    '13',    // 0x6c
    '14',    // 0x6d
    '15',    // 0x6e
    '16',    // 0x6f
    '17',    // 0x70
    '18',    // 0x71
    '19',    // 0x72
    '20',    // 0x73
    '(1)',    // 0x74
    '(2)',    // 0x75
    '(3)',    // 0x76
    '(4)',    // 0x77
    '(5)',    // 0x78
    '(6)',    // 0x79
    '(7)',    // 0x7a
    '(8)',    // 0x7b
    '(9)',    // 0x7c
    '(10)',    // 0x7d
    '(11)',    // 0x7e
    '(12)',    // 0x7f
    '(13)',    // 0x80
    '(14)',    // 0x81
    '(15)',    // 0x82
    '(16)',    // 0x83
    '(17)',    // 0x84
    '(18)',    // 0x85
    '(19)',    // 0x86
    '(20)',    // 0x87
    '1.',    // 0x88
    '2.',    // 0x89
    '3.',    // 0x8a
    '4.',    // 0x8b
    '5.',    // 0x8c
    '6.',    // 0x8d
    '7.',    // 0x8e
    '8.',    // 0x8f
    '9.',    // 0x90
    '10.',    // 0x91
    '11.',    // 0x92
    '12.',    // 0x93
    '13.',    // 0x94
    '14.',    // 0x95
    '15.',    // 0x96
    '16.',    // 0x97
    '17.',    // 0x98
    '18.',    // 0x99
    '19.',    // 0x9a
    '20.',    // 0x9b
    '(a)',    // 0x9c
    '(b)',    // 0x9d
    '(c)',    // 0x9e
    '(d)',    // 0x9f
    '(e)',    // 0xa0
    '(f)',    // 0xa1
    '(g)',    // 0xa2
    '(h)',    // 0xa3
    '(i)',    // 0xa4
    '(j)',    // 0xa5
    '(k)',    // 0xa6
    '(l)',    // 0xa7
    '(m)',    // 0xa8
    '(n)',    // 0xa9
    '(o)',    // 0xaa
    '(p)',    // 0xab
    '(q)',    // 0xac
    '(r)',    // 0xad
    '(s)',    // 0xae
    '(t)',    // 0xaf
    '(u)',    // 0xb0
    '(v)',    // 0xb1
    '(w)',    // 0xb2
    '(x)',    // 0xb3
    '(y)',    // 0xb4
    '(z)',    // 0xb5
    'A',    // 0xb6
    'B',    // 0xb7
    'C',    // 0xb8
    'D',    // 0xb9
    'E',    // 0xba
    'F',    // 0xbb
    'G',    // 0xbc
    'H',    // 0xbd
    'I',    // 0xbe
    'J',    // 0xbf
    'K',    // 0xc0
    'L',    // 0xc1
    'M',    // 0xc2
    'N',    // 0xc3
    'O',    // 0xc4
    'P',    // 0xc5
    'Q',    // 0xc6
    'R',    // 0xc7
    'S',    // 0xc8
    'T',    // 0xc9
    'U',    // 0xca
    'V',    // 0xcb
    'W',    // 0xcc
    'X',    // 0xcd
    'Y',    // 0xce
    'Z',    // 0xcf
    'a',    // 0xd0
    'b',    // 0xd1
    'c',    // 0xd2
    'd',    // 0xd3
    'e',    // 0xd4
    'f',    // 0xd5
    'g',    // 0xd6
    'h',    // 0xd7
    'i',    // 0xd8
    'j',    // 0xd9
    'k',    // 0xda
    'l',    // 0xdb
    'm',    // 0xdc
    'n',    // 0xdd
    'o',    // 0xde
    'p',    // 0xdf
    'q',    // 0xe0
    'r',    // 0xe1
    's',    // 0xe2
    't',    // 0xe3
    'u',    // 0xe4
    'v',    // 0xe5
    'w',    // 0xe6
    'x',    // 0xe7
    'y',    // 0xe8
    'z',    // 0xe9
    '0',    // 0xea
    '11',    // 0xeb
    '12',    // 0xec
    '13',    // 0xed
    '14',    // 0xee
    '15',    // 0xef
    '16',    // 0xf0
    '17',    // 0xf1
    '18',    // 0xf2
    '19',    // 0xf3
    '20',    // 0xf4
    '1',    // 0xf5
    '2',    // 0xf6
    '3',    // 0xf7
    '4',    // 0xf8
    '5',    // 0xf9
    '6',    // 0xfa
    '7',    // 0xfb
    '8',    // 0xfc
    '9',    // 0xfd
    '10',    // 0xfe
    '0',    // 0xff
];
