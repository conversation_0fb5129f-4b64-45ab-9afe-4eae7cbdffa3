<?php

return [
    '[?]',    // 0x00
    '[?]',    // 0x01
    'N',    // 0x02
    'H',    // 0x03
    '[?]',    // 0x04
    'a',    // 0x05
    'aa',    // 0x06
    'i',    // 0x07
    'ii',    // 0x08
    'u',    // 0x09
    'uu',    // 0x0a
    'R',    // 0x0b
    'L',    // 0x0c
    '[?]',    // 0x0d
    'e',    // 0x0e
    'ee',    // 0x0f
    'ai',    // 0x10
    '[?]',    // 0x11
    'o',    // 0x12
    'oo',    // 0x13
    'au',    // 0x14
    'k',    // 0x15
    'kh',    // 0x16
    'g',    // 0x17
    'gh',    // 0x18
    'ng',    // 0x19
    'c',    // 0x1a
    'ch',    // 0x1b
    'j',    // 0x1c
    'jh',    // 0x1d
    'ny',    // 0x1e
    'tt',    // 0x1f
    'tth',    // 0x20
    'dd',    // 0x21
    'ddh',    // 0x22
    'nn',    // 0x23
    't',    // 0x24
    'th',    // 0x25
    'd',    // 0x26
    'dh',    // 0x27
    'n',    // 0x28
    '[?]',    // 0x29
    'p',    // 0x2a
    'ph',    // 0x2b
    'b',    // 0x2c
    'bh',    // 0x2d
    'm',    // 0x2e
    'y',    // 0x2f
    'r',    // 0x30
    'rr',    // 0x31
    'l',    // 0x32
    'll',    // 0x33
    'lll',    // 0x34
    'v',    // 0x35
    'sh',    // 0x36
    'ss',    // 0x37
    's',    // 0x38
    'h',    // 0x39
    '[?]',    // 0x3a
    '[?]',    // 0x3b
    '[?]',    // 0x3c
    '[?]',    // 0x3d
    'aa',    // 0x3e
    'i',    // 0x3f
    'ii',    // 0x40
    'u',    // 0x41
    'uu',    // 0x42
    'R',    // 0x43
    '[?]',    // 0x44
    '[?]',    // 0x45
    'e',    // 0x46
    'ee',    // 0x47
    'ai',    // 0x48
    '',    // 0x49
    'o',    // 0x4a
    'oo',    // 0x4b
    'au',    // 0x4c
    '',    // 0x4d
    '[?]',    // 0x4e
    '[?]',    // 0x4f
    '[?]',    // 0x50
    '[?]',    // 0x51
    '[?]',    // 0x52
    '[?]',    // 0x53
    '[?]',    // 0x54
    '[?]',    // 0x55
    '[?]',    // 0x56
    '+',    // 0x57
    '[?]',    // 0x58
    '[?]',    // 0x59
    '[?]',    // 0x5a
    '[?]',    // 0x5b
    '[?]',    // 0x5c
    '[?]',    // 0x5d
    '[?]',    // 0x5e
    '[?]',    // 0x5f
    'RR',    // 0x60
    'LL',    // 0x61
    '[?]',    // 0x62
    '[?]',    // 0x63
    '[?]',    // 0x64
    '[?]',    // 0x65
    '0',    // 0x66
    '1',    // 0x67
    '2',    // 0x68
    '3',    // 0x69
    '4',    // 0x6a
    '5',    // 0x6b
    '6',    // 0x6c
    '7',    // 0x6d
    '8',    // 0x6e
    '9',    // 0x6f
    '[?]',    // 0x70
    '[?]',    // 0x71
    '[?]',    // 0x72
    '[?]',    // 0x73
    '[?]',    // 0x74
    '[?]',    // 0x75
    '[?]',    // 0x76
    '[?]',    // 0x77
    '[?]',    // 0x78
    '[?]',    // 0x79
    '[?]',    // 0x7a
    '[?]',    // 0x7b
    '[?]',    // 0x7c
    '[?]',    // 0x7d
    '[?]',    // 0x7e
    '[?]',    // 0x7f
    '[?]',    // 0x80
    '[?]',    // 0x81
    'N',    // 0x82
    'H',    // 0x83
    '[?]',    // 0x84
    'a',    // 0x85
    'aa',    // 0x86
    'ae',    // 0x87
    'aae',    // 0x88
    'i',    // 0x89
    'ii',    // 0x8a
    'u',    // 0x8b
    'uu',    // 0x8c
    'R',    // 0x8d
    'RR',    // 0x8e
    'L',    // 0x8f
    'LL',    // 0x90
    'e',    // 0x91
    'ee',    // 0x92
    'ai',    // 0x93
    'o',    // 0x94
    'oo',    // 0x95
    'au',    // 0x96
    '[?]',    // 0x97
    '[?]',    // 0x98
    '[?]',    // 0x99
    'k',    // 0x9a
    'kh',    // 0x9b
    'g',    // 0x9c
    'gh',    // 0x9d
    'ng',    // 0x9e
    'nng',    // 0x9f
    'c',    // 0xa0
    'ch',    // 0xa1
    'j',    // 0xa2
    'jh',    // 0xa3
    'ny',    // 0xa4
    'jny',    // 0xa5
    'nyj',    // 0xa6
    'tt',    // 0xa7
    'tth',    // 0xa8
    'dd',    // 0xa9
    'ddh',    // 0xaa
    'nn',    // 0xab
    'nndd',    // 0xac
    't',    // 0xad
    'th',    // 0xae
    'd',    // 0xaf
    'dh',    // 0xb0
    'n',    // 0xb1
    '[?]',    // 0xb2
    'nd',    // 0xb3
    'p',    // 0xb4
    'ph',    // 0xb5
    'b',    // 0xb6
    'bh',    // 0xb7
    'm',    // 0xb8
    'mb',    // 0xb9
    'y',    // 0xba
    'r',    // 0xbb
    '[?]',    // 0xbc
    'l',    // 0xbd
    '[?]',    // 0xbe
    '[?]',    // 0xbf
    'v',    // 0xc0
    'sh',    // 0xc1
    'ss',    // 0xc2
    's',    // 0xc3
    'h',    // 0xc4
    'll',    // 0xc5
    'f',    // 0xc6
    '[?]',    // 0xc7
    '[?]',    // 0xc8
    '[?]',    // 0xc9
    '',    // 0xca
    '[?]',    // 0xcb
    '[?]',    // 0xcc
    '[?]',    // 0xcd
    '[?]',    // 0xce
    'aa',    // 0xcf
    'ae',    // 0xd0
    'aae',    // 0xd1
    'i',    // 0xd2
    'ii',    // 0xd3
    'u',    // 0xd4
    '[?]',    // 0xd5
    'uu',    // 0xd6
    '[?]',    // 0xd7
    'R',    // 0xd8
    'e',    // 0xd9
    'ee',    // 0xda
    'ai',    // 0xdb
    'o',    // 0xdc
    'oo',    // 0xdd
    'au',    // 0xde
    'L',    // 0xdf
    '[?]',    // 0xe0
    '[?]',    // 0xe1
    '[?]',    // 0xe2
    '[?]',    // 0xe3
    '[?]',    // 0xe4
    '[?]',    // 0xe5
    '[?]',    // 0xe6
    '[?]',    // 0xe7
    '[?]',    // 0xe8
    '[?]',    // 0xe9
    '[?]',    // 0xea
    '[?]',    // 0xeb
    '[?]',    // 0xec
    '[?]',    // 0xed
    '[?]',    // 0xee
    '[?]',    // 0xef
    '[?]',    // 0xf0
    '[?]',    // 0xf1
    'RR',    // 0xf2
    'LL',    // 0xf3
    ' . ',    // 0xf4
    '[?]',    // 0xf5
    '[?]',    // 0xf6
    '[?]',    // 0xf7
    '[?]',    // 0xf8
    '[?]',    // 0xf9
    '[?]',    // 0xfa
    '[?]',    // 0xfb
    '[?]',    // 0xfc
    '[?]',    // 0xfd
    '[?]',    // 0xfe
];
