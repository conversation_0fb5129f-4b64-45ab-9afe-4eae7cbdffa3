<?php

return [
    'dit',    // 0x00
    'dix',    // 0x01
    'di',    // 0x02
    'dip',    // 0x03
    'diex',    // 0x04
    'die',    // 0x05
    'diep',    // 0x06
    'dat',    // 0x07
    'dax',    // 0x08
    'da',    // 0x09
    'dap',    // 0x0a
    'duox',    // 0x0b
    'duo',    // 0x0c
    'dot',    // 0x0d
    'dox',    // 0x0e
    'do',    // 0x0f
    'dop',    // 0x10
    'dex',    // 0x11
    'de',    // 0x12
    'dep',    // 0x13
    'dut',    // 0x14
    'dux',    // 0x15
    'du',    // 0x16
    'dup',    // 0x17
    'durx',    // 0x18
    'dur',    // 0x19
    'tit',    // 0x1a
    'tix',    // 0x1b
    'ti',    // 0x1c
    'tip',    // 0x1d
    'tiex',    // 0x1e
    'tie',    // 0x1f
    'tiep',    // 0x20
    'tat',    // 0x21
    'tax',    // 0x22
    'ta',    // 0x23
    'tap',    // 0x24
    'tuot',    // 0x25
    'tuox',    // 0x26
    'tuo',    // 0x27
    'tuop',    // 0x28
    'tot',    // 0x29
    'tox',    // 0x2a
    'to',    // 0x2b
    'top',    // 0x2c
    'tex',    // 0x2d
    'te',    // 0x2e
    'tep',    // 0x2f
    'tut',    // 0x30
    'tux',    // 0x31
    'tu',    // 0x32
    'tup',    // 0x33
    'turx',    // 0x34
    'tur',    // 0x35
    'ddit',    // 0x36
    'ddix',    // 0x37
    'ddi',    // 0x38
    'ddip',    // 0x39
    'ddiex',    // 0x3a
    'ddie',    // 0x3b
    'ddiep',    // 0x3c
    'ddat',    // 0x3d
    'ddax',    // 0x3e
    'dda',    // 0x3f
    'ddap',    // 0x40
    'dduox',    // 0x41
    'dduo',    // 0x42
    'dduop',    // 0x43
    'ddot',    // 0x44
    'ddox',    // 0x45
    'ddo',    // 0x46
    'ddop',    // 0x47
    'ddex',    // 0x48
    'dde',    // 0x49
    'ddep',    // 0x4a
    'ddut',    // 0x4b
    'ddux',    // 0x4c
    'ddu',    // 0x4d
    'ddup',    // 0x4e
    'ddurx',    // 0x4f
    'ddur',    // 0x50
    'ndit',    // 0x51
    'ndix',    // 0x52
    'ndi',    // 0x53
    'ndip',    // 0x54
    'ndiex',    // 0x55
    'ndie',    // 0x56
    'ndat',    // 0x57
    'ndax',    // 0x58
    'nda',    // 0x59
    'ndap',    // 0x5a
    'ndot',    // 0x5b
    'ndox',    // 0x5c
    'ndo',    // 0x5d
    'ndop',    // 0x5e
    'ndex',    // 0x5f
    'nde',    // 0x60
    'ndep',    // 0x61
    'ndut',    // 0x62
    'ndux',    // 0x63
    'ndu',    // 0x64
    'ndup',    // 0x65
    'ndurx',    // 0x66
    'ndur',    // 0x67
    'hnit',    // 0x68
    'hnix',    // 0x69
    'hni',    // 0x6a
    'hnip',    // 0x6b
    'hniet',    // 0x6c
    'hniex',    // 0x6d
    'hnie',    // 0x6e
    'hniep',    // 0x6f
    'hnat',    // 0x70
    'hnax',    // 0x71
    'hna',    // 0x72
    'hnap',    // 0x73
    'hnuox',    // 0x74
    'hnuo',    // 0x75
    'hnot',    // 0x76
    'hnox',    // 0x77
    'hnop',    // 0x78
    'hnex',    // 0x79
    'hne',    // 0x7a
    'hnep',    // 0x7b
    'hnut',    // 0x7c
    'nit',    // 0x7d
    'nix',    // 0x7e
    'ni',    // 0x7f
    'nip',    // 0x80
    'niex',    // 0x81
    'nie',    // 0x82
    'niep',    // 0x83
    'nax',    // 0x84
    'na',    // 0x85
    'nap',    // 0x86
    'nuox',    // 0x87
    'nuo',    // 0x88
    'nuop',    // 0x89
    'not',    // 0x8a
    'nox',    // 0x8b
    'no',    // 0x8c
    'nop',    // 0x8d
    'nex',    // 0x8e
    'ne',    // 0x8f
    'nep',    // 0x90
    'nut',    // 0x91
    'nux',    // 0x92
    'nu',    // 0x93
    'nup',    // 0x94
    'nurx',    // 0x95
    'nur',    // 0x96
    'hlit',    // 0x97
    'hlix',    // 0x98
    'hli',    // 0x99
    'hlip',    // 0x9a
    'hliex',    // 0x9b
    'hlie',    // 0x9c
    'hliep',    // 0x9d
    'hlat',    // 0x9e
    'hlax',    // 0x9f
    'hla',    // 0xa0
    'hlap',    // 0xa1
    'hluox',    // 0xa2
    'hluo',    // 0xa3
    'hluop',    // 0xa4
    'hlox',    // 0xa5
    'hlo',    // 0xa6
    'hlop',    // 0xa7
    'hlex',    // 0xa8
    'hle',    // 0xa9
    'hlep',    // 0xaa
    'hlut',    // 0xab
    'hlux',    // 0xac
    'hlu',    // 0xad
    'hlup',    // 0xae
    'hlurx',    // 0xaf
    'hlur',    // 0xb0
    'hlyt',    // 0xb1
    'hlyx',    // 0xb2
    'hly',    // 0xb3
    'hlyp',    // 0xb4
    'hlyrx',    // 0xb5
    'hlyr',    // 0xb6
    'lit',    // 0xb7
    'lix',    // 0xb8
    'li',    // 0xb9
    'lip',    // 0xba
    'liet',    // 0xbb
    'liex',    // 0xbc
    'lie',    // 0xbd
    'liep',    // 0xbe
    'lat',    // 0xbf
    'lax',    // 0xc0
    'la',    // 0xc1
    'lap',    // 0xc2
    'luot',    // 0xc3
    'luox',    // 0xc4
    'luo',    // 0xc5
    'luop',    // 0xc6
    'lot',    // 0xc7
    'lox',    // 0xc8
    'lo',    // 0xc9
    'lop',    // 0xca
    'lex',    // 0xcb
    'le',    // 0xcc
    'lep',    // 0xcd
    'lut',    // 0xce
    'lux',    // 0xcf
    'lu',    // 0xd0
    'lup',    // 0xd1
    'lurx',    // 0xd2
    'lur',    // 0xd3
    'lyt',    // 0xd4
    'lyx',    // 0xd5
    'ly',    // 0xd6
    'lyp',    // 0xd7
    'lyrx',    // 0xd8
    'lyr',    // 0xd9
    'git',    // 0xda
    'gix',    // 0xdb
    'gi',    // 0xdc
    'gip',    // 0xdd
    'giet',    // 0xde
    'giex',    // 0xdf
    'gie',    // 0xe0
    'giep',    // 0xe1
    'gat',    // 0xe2
    'gax',    // 0xe3
    'ga',    // 0xe4
    'gap',    // 0xe5
    'guot',    // 0xe6
    'guox',    // 0xe7
    'guo',    // 0xe8
    'guop',    // 0xe9
    'got',    // 0xea
    'gox',    // 0xeb
    'go',    // 0xec
    'gop',    // 0xed
    'get',    // 0xee
    'gex',    // 0xef
    'ge',    // 0xf0
    'gep',    // 0xf1
    'gut',    // 0xf2
    'gux',    // 0xf3
    'gu',    // 0xf4
    'gup',    // 0xf5
    'gurx',    // 0xf6
    'gur',    // 0xf7
    'kit',    // 0xf8
    'kix',    // 0xf9
    'ki',    // 0xfa
    'kip',    // 0xfb
    'kiex',    // 0xfc
    'kie',    // 0xfd
    'kiep',    // 0xfe
    'kat',    // 0xff
];
