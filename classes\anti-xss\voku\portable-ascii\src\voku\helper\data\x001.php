<?php

return [
    'A',    // 0x00
    'a',    // 0x01
    'A',    // 0x02
    'a',    // 0x03
    'A',    // 0x04
    'a',    // 0x05
    'C',    // 0x06
    'c',    // 0x07
    'C',    // 0x08
    'c',    // 0x09
    'C',    // 0x0a
    'c',    // 0x0b
    'C',    // 0x0c
    'c',    // 0x0d
    'D',    // 0x0e
    'd',    // 0x0f
    'D',    // 0x10
    'd',    // 0x11
    'E',    // 0x12
    'e',    // 0x13
    'E',    // 0x14
    'e',    // 0x15
    'E',    // 0x16
    'e',    // 0x17
    'E',    // 0x18
    'e',    // 0x19
    'E',    // 0x1a
    'e',    // 0x1b
    'G',    // 0x1c
    'g',    // 0x1d
    'G',    // 0x1e
    'g',    // 0x1f
    'G',    // 0x20
    'g',    // 0x21
    'G',    // 0x22
    'g',    // 0x23
    'H',    // 0x24
    'h',    // 0x25
    'H',    // 0x26
    'h',    // 0x27
    'I',    // 0x28
    'i',    // 0x29
    'I',    // 0x2a
    'i',    // 0x2b
    'I',    // 0x2c
    'i',    // 0x2d
    'I',    // 0x2e
    'i',    // 0x2f
    'I',    // 0x30
    'i',    // 0x31
    'IJ',    // 0x32
    'ij',    // 0x33
    'J',    // 0x34
    'j',    // 0x35
    'K',    // 0x36
    'k',    // 0x37
    'k',    // 0x38
    'L',    // 0x39
    'l',    // 0x3a
    'L',    // 0x3b
    'l',    // 0x3c
    'L',    // 0x3d
    'l',    // 0x3e
    'L',    // 0x3f
    'l',    // 0x40
    'L',    // 0x41
    'l',    // 0x42
    'N',    // 0x43
    'n',    // 0x44
    'N',    // 0x45
    'n',    // 0x46
    'N',    // 0x47
    'n',    // 0x48
    '\'n',    // 0x49
    'ng',    // 0x4a
    'NG',    // 0x4b
    'O',    // 0x4c
    'o',    // 0x4d
    'O',    // 0x4e
    'o',    // 0x4f
    'O',    // 0x50
    'o',    // 0x51
    'OE',    // 0x52
    'oe',    // 0x53
    'R',    // 0x54
    'r',    // 0x55
    'R',    // 0x56
    'r',    // 0x57
    'R',    // 0x58
    'r',    // 0x59
    'S',    // 0x5a
    's',    // 0x5b
    'S',    // 0x5c
    's',    // 0x5d
    'S',    // 0x5e
    's',    // 0x5f
    'S',    // 0x60
    's',    // 0x61
    'T',    // 0x62
    't',    // 0x63
    'T',    // 0x64
    't',    // 0x65
    'T',    // 0x66
    't',    // 0x67
    'U',    // 0x68
    'u',    // 0x69
    'U',    // 0x6a
    'u',    // 0x6b
    'U',    // 0x6c
    'u',    // 0x6d
    'U',    // 0x6e
    'u',    // 0x6f
    'U',    // 0x70
    'u',    // 0x71
    'U',    // 0x72
    'u',    // 0x73
    'W',    // 0x74
    'w',    // 0x75
    'Y',    // 0x76
    'y',    // 0x77
    'Y',    // 0x78
    'Z',    // 0x79
    'z',    // 0x7a
    'Z',    // 0x7b
    'z',    // 0x7c
    'Z',    // 0x7d
    'z',    // 0x7e
    's',    // 0x7f
    'b',    // 0x80
    'B',    // 0x81
    'B',    // 0x82
    'b',    // 0x83
    '6',    // 0x84
    '6',    // 0x85
    'O',    // 0x86
    'C',    // 0x87
    'c',    // 0x88
    'D',    // 0x89
    'D',    // 0x8a
    'D',    // 0x8b
    'd',    // 0x8c
    'd',    // 0x8d
    '3',    // 0x8e
    '@',    // 0x8f
    'E',    // 0x90
    'F',    // 0x91
    'f',    // 0x92
    'G',    // 0x93
    'G',    // 0x94
    'hv',    // 0x95
    'I',    // 0x96
    'I',    // 0x97
    'K',    // 0x98
    'k',    // 0x99
    'l',    // 0x9a
    'l',    // 0x9b
    'W',    // 0x9c
    'N',    // 0x9d
    'n',    // 0x9e
    'O',    // 0x9f
    'O',    // 0xa0
    'o',    // 0xa1
    'OI',    // 0xa2
    'oi',    // 0xa3
    'P',    // 0xa4
    'p',    // 0xa5
    'YR',    // 0xa6
    '2',    // 0xa7
    '2',    // 0xa8
    'SH',    // 0xa9
    'sh',    // 0xaa
    't',    // 0xab
    'T',    // 0xac
    't',    // 0xad
    'T',    // 0xae
    'U',    // 0xaf
    'u',    // 0xb0
    'Y',    // 0xb1
    'V',    // 0xb2
    'Y',    // 0xb3
    'y',    // 0xb4
    'Z',    // 0xb5
    'z',    // 0xb6
    'ZH',    // 0xb7
    'ZH',    // 0xb8
    'zh',    // 0xb9
    'zh',    // 0xba
    '2',    // 0xbb
    '5',    // 0xbc
    '5',    // 0xbd
    'ts',    // 0xbe
    'w',    // 0xbf
    '|',    // 0xc0
    '||',    // 0xc1
    '|=',    // 0xc2
    '!',    // 0xc3
    'DZ',    // 0xc4
    'Dz',    // 0xc5
    'dz',    // 0xc6
    'LJ',    // 0xc7
    'Lj',    // 0xc8
    'lj',    // 0xc9
    'NJ',    // 0xca
    'Nj',    // 0xcb
    'nj',    // 0xcc
    'A',    // 0xcd
    'a',    // 0xce
    'I',    // 0xcf
    'i',    // 0xd0
    'O',    // 0xd1
    'o',    // 0xd2
    'U',    // 0xd3
    'u',    // 0xd4
    'U',    // 0xd5
    'u',    // 0xd6
    'U',    // 0xd7
    'u',    // 0xd8
    'U',    // 0xd9
    'u',    // 0xda
    'U',    // 0xdb
    'u',    // 0xdc
    '@',    // 0xdd
    'A',    // 0xde
    'a',    // 0xdf
    'A',    // 0xe0
    'a',    // 0xe1
    'AE',    // 0xe2
    'ae',    // 0xe3
    'G',    // 0xe4
    'g',    // 0xe5
    'G',    // 0xe6
    'g',    // 0xe7
    'K',    // 0xe8
    'k',    // 0xe9
    'O',    // 0xea
    'o',    // 0xeb
    'O',    // 0xec
    'o',    // 0xed
    'ZH',    // 0xee
    'zh',    // 0xef
    'j',    // 0xf0
    'DZ',    // 0xf1
    'Dz',    // 0xf2
    'dz',    // 0xf3
    'G',    // 0xf4
    'g',    // 0xf5
    'HV',    // 0xf6
    'W',    // 0xf7
    'N',    // 0xf8
    'n',    // 0xf9
    'A',    // 0xfa
    'a',    // 0xfb
    'AE',    // 0xfc
    'ae',    // 0xfd
    'O',    // 0xfe
    'o',    // 0xff
];
