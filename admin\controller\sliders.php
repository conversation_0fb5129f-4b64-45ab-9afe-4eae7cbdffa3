<?php

session_start();

require '../../config.php';
require '../admin_config.php';
require '../functions.php';
require '../views/header.view.php';

if (isset($_SESSION['user_email'])){

$connect = connect($database);

$check_access = check_access($connect);

if ($check_access['user_role'] == 1 || $check_access['user_role'] == 2){
    
require '../views/sliders.view.php';
	
}else{
	header('Location:'.SITE_URL);
}

require '../views/footer.view.php';

}else{
	header('Location: ./login.php');	
}


?>