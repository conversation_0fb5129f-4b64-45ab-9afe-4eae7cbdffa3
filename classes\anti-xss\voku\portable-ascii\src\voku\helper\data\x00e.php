<?php

return [
    '[?]',    // 0x00
    'k',    // 0x01
    'kh',    // 0x02
    'kh',    // 0x03
    'kh',    // 0x04
    'kh',    // 0x05
    'kh',    // 0x06
    'ng',    // 0x07
    'cch',    // 0x08
    'ch',    // 0x09
    'ch',    // 0x0a
    'ch',    // 0x0b
    'ch',    // 0x0c
    'y',    // 0x0d
    'd',    // 0x0e
    't',    // 0x0f
    'th',    // 0x10
    'th',    // 0x11
    'th',    // 0x12
    'n',    // 0x13
    'd',    // 0x14
    't',    // 0x15
    'th',    // 0x16
    'th',    // 0x17
    'th',    // 0x18
    'n',    // 0x19
    'b',    // 0x1a
    'p',    // 0x1b
    'ph',    // 0x1c
    'f',    // 0x1d
    'ph',    // 0x1e
    'f',    // 0x1f
    'ph',    // 0x20
    'm',    // 0x21
    'y',    // 0x22
    'r',    // 0x23
    'R',    // 0x24
    'l',    // 0x25
    'L',    // 0x26
    'w',    // 0x27
    's',    // 0x28
    's',    // 0x29
    's',    // 0x2a
    'h',    // 0x2b
    'l',    // 0x2c
    '`',    // 0x2d
    'h',    // 0x2e
    '~',    // 0x2f
    'a',    // 0x30
    'a',    // 0x31
    'aa',    // 0x32
    'am',    // 0x33
    'i',    // 0x34
    'ii',    // 0x35
    'ue',    // 0x36
    'uue',    // 0x37
    'u',    // 0x38
    'uu',    // 0x39
    '\'',    // 0x3a
    '[?]',    // 0x3b
    '[?]',    // 0x3c
    '[?]',    // 0x3d
    '[?]',    // 0x3e
    'Bh.',    // 0x3f
    'e',    // 0x40
    'ae',    // 0x41
    'o',    // 0x42
    'ai',    // 0x43
    'ai',    // 0x44
    'ao',    // 0x45
    '+',    // 0x46
    '',    // 0x47
    '',    // 0x48
    '',    // 0x49
    '',    // 0x4a
    '',    // 0x4b
    '',    // 0x4c
    'M',    // 0x4d
    '',    // 0x4e
    ' * ',    // 0x4f
    '0',    // 0x50
    '1',    // 0x51
    '2',    // 0x52
    '3',    // 0x53
    '4',    // 0x54
    '5',    // 0x55
    '6',    // 0x56
    '7',    // 0x57
    '8',    // 0x58
    '9',    // 0x59
    ' // ',    // 0x5a
    ' /// ',    // 0x5b
    '[?]',    // 0x5c
    '[?]',    // 0x5d
    '[?]',    // 0x5e
    '[?]',    // 0x5f
    '[?]',    // 0x60
    '[?]',    // 0x61
    '[?]',    // 0x62
    '[?]',    // 0x63
    '[?]',    // 0x64
    '[?]',    // 0x65
    '[?]',    // 0x66
    '[?]',    // 0x67
    '[?]',    // 0x68
    '[?]',    // 0x69
    '[?]',    // 0x6a
    '[?]',    // 0x6b
    '[?]',    // 0x6c
    '[?]',    // 0x6d
    '[?]',    // 0x6e
    '[?]',    // 0x6f
    '[?]',    // 0x70
    '[?]',    // 0x71
    '[?]',    // 0x72
    '[?]',    // 0x73
    '[?]',    // 0x74
    '[?]',    // 0x75
    '[?]',    // 0x76
    '[?]',    // 0x77
    '[?]',    // 0x78
    '[?]',    // 0x79
    '[?]',    // 0x7a
    '[?]',    // 0x7b
    '[?]',    // 0x7c
    '[?]',    // 0x7d
    '[?]',    // 0x7e
    '[?]',    // 0x7f
    '[?]',    // 0x80
    'k',    // 0x81
    'kh',    // 0x82
    '[?]',    // 0x83
    'kh',    // 0x84
    '[?]',    // 0x85
    '[?]',    // 0x86
    'ng',    // 0x87
    'ch',    // 0x88
    '[?]',    // 0x89
    's',    // 0x8a
    '[?]',    // 0x8b
    '[?]',    // 0x8c
    'ny',    // 0x8d
    '[?]',    // 0x8e
    '[?]',    // 0x8f
    '[?]',    // 0x90
    '[?]',    // 0x91
    '[?]',    // 0x92
    '[?]',    // 0x93
    'd',    // 0x94
    'h',    // 0x95
    'th',    // 0x96
    'th',    // 0x97
    '[?]',    // 0x98
    'n',    // 0x99
    'b',    // 0x9a
    'p',    // 0x9b
    'ph',    // 0x9c
    'f',    // 0x9d
    'ph',    // 0x9e
    'f',    // 0x9f
    '[?]',    // 0xa0
    'm',    // 0xa1
    'y',    // 0xa2
    'r',    // 0xa3
    '[?]',    // 0xa4
    'l',    // 0xa5
    '[?]',    // 0xa6
    'w',    // 0xa7
    '[?]',    // 0xa8
    '[?]',    // 0xa9
    's',    // 0xaa
    'h',    // 0xab
    '[?]',    // 0xac
    '`',    // 0xad
    '',    // 0xae
    '~',    // 0xaf
    'a',    // 0xb0
    '',    // 0xb1
    'aa',    // 0xb2
    'am',    // 0xb3
    'i',    // 0xb4
    'ii',    // 0xb5
    'y',    // 0xb6
    'yy',    // 0xb7
    'u',    // 0xb8
    'uu',    // 0xb9
    '[?]',    // 0xba
    'o',    // 0xbb
    'l',    // 0xbc
    'ny',    // 0xbd
    '[?]',    // 0xbe
    '[?]',    // 0xbf
    'e',    // 0xc0
    'ei',    // 0xc1
    'o',    // 0xc2
    'ay',    // 0xc3
    'ai',    // 0xc4
    '[?]',    // 0xc5
    '+',    // 0xc6
    '[?]',    // 0xc7
    '',    // 0xc8
    '',    // 0xc9
    '',    // 0xca
    '',    // 0xcb
    '',    // 0xcc
    'M',    // 0xcd
    '[?]',    // 0xce
    '[?]',    // 0xcf
    '0',    // 0xd0
    '1',    // 0xd1
    '2',    // 0xd2
    '3',    // 0xd3
    '4',    // 0xd4
    '5',    // 0xd5
    '6',    // 0xd6
    '7',    // 0xd7
    '8',    // 0xd8
    '9',    // 0xd9
    '[?]',    // 0xda
    '[?]',    // 0xdb
    'hn',    // 0xdc
    'hm',    // 0xdd
    '[?]',    // 0xde
    '[?]',    // 0xdf
    '[?]',    // 0xe0
    '[?]',    // 0xe1
    '[?]',    // 0xe2
    '[?]',    // 0xe3
    '[?]',    // 0xe4
    '[?]',    // 0xe5
    '[?]',    // 0xe6
    '[?]',    // 0xe7
    '[?]',    // 0xe8
    '[?]',    // 0xe9
    '[?]',    // 0xea
    '[?]',    // 0xeb
    '[?]',    // 0xec
    '[?]',    // 0xed
    '[?]',    // 0xee
    '[?]',    // 0xef
    '[?]',    // 0xf0
    '[?]',    // 0xf1
    '[?]',    // 0xf2
    '[?]',    // 0xf3
    '[?]',    // 0xf4
    '[?]',    // 0xf5
    '[?]',    // 0xf6
    '[?]',    // 0xf7
    '[?]',    // 0xf8
    '[?]',    // 0xf9
    '[?]',    // 0xfa
    '[?]',    // 0xfb
    '[?]',    // 0xfc
    '[?]',    // 0xfd
    '[?]',    // 0xfe
];
