<?php

return [
    ' a/c ',    // 0x00
    ' a/s ',    // 0x01
    'C',    // 0x02
    '',    // 0x03
    '',    // 0x04
    ' c/o ',    // 0x05
    ' c/u ',    // 0x06
    '',    // 0x07
    '',    // 0x08
    '',    // 0x09
    'g',    // 0x0a
    'H',    // 0x0b
    'H',    // 0x0c
    'H',    // 0x0d
    'h',    // 0x0e
    '',    // 0x0f
    'I',    // 0x10
    'I',    // 0x11
    'L',    // 0x12
    'l',    // 0x13
    '',    // 0x14
    'N',    // 0x15
    'No. ',    // 0x16
    '',    // 0x17
    '',    // 0x18
    'P',    // 0x19
    'Q',    // 0x1a
    'R',    // 0x1b
    'R',    // 0x1c
    'R',    // 0x1d
    '',    // 0x1e
    '',    // 0x1f
    '(sm)',    // 0x20
    'TEL',    // 0x21
    '(tm)',    // 0x22
    '',    // 0x23
    'Z',    // 0x24
    '',    // 0x25
    '',    // 0x26
    '',    // 0x27
    'Z',    // 0x28
    '',    // 0x29
    'K',    // 0x2a
    'A',    // 0x2b
    'B',    // 0x2c
    'C',    // 0x2d
    'e',    // 0x2e
    'e',    // 0x2f
    'E',    // 0x30
    'F',    // 0x31
    'F',    // 0x32
    'M',    // 0x33
    'o',    // 0x34
    '',    // 0x35
    '',    // 0x36
    '',    // 0x37
    '',    // 0x38
    'i',    // 0x39
    '',    // 0x3a
    'FAX',    // 0x3b
    '',    // 0x3c
    '',    // 0x3d
    '',    // 0x3e
    '',    // 0x3f
    '[?]',    // 0x40
    '[?]',    // 0x41
    '[?]',    // 0x42
    '[?]',    // 0x43
    '[?]',    // 0x44
    'D',    // 0x45
    'd',    // 0x46
    'e',    // 0x47
    'i',    // 0x48
    'j',    // 0x49
    '[?]',    // 0x4a
    '[?]',    // 0x4b
    '[?]',    // 0x4c
    '[?]',    // 0x4d
    'F',    // 0x4e
    '[?]',    // 0x4f
    ' 1/7 ',    // 0x50
    ' 1/9 ',    // 0x51
    ' 1/10 ',    // 0x52
    ' 1/3 ',    // 0x53
    ' 2/3 ',    // 0x54
    ' 1/5 ',    // 0x55
    ' 2/5 ',    // 0x56
    ' 3/5 ',    // 0x57
    ' 4/5 ',    // 0x58
    ' 1/6 ',    // 0x59
    ' 5/6 ',    // 0x5a
    ' 1/8 ',    // 0x5b
    ' 3/8 ',    // 0x5c
    ' 5/8 ',    // 0x5d
    ' 7/8 ',    // 0x5e
    ' 1/',    // 0x5f
    'I',    // 0x60
    'II',    // 0x61
    'III',    // 0x62
    'IV',    // 0x63
    'V',    // 0x64
    'VI',    // 0x65
    'VII',    // 0x66
    'VIII',    // 0x67
    'IX',    // 0x68
    'X',    // 0x69
    'XI',    // 0x6a
    'XII',    // 0x6b
    'L',    // 0x6c
    'C',    // 0x6d
    'D',    // 0x6e
    'M',    // 0x6f
    'i',    // 0x70
    'ii',    // 0x71
    'iii',    // 0x72
    'iv',    // 0x73
    'v',    // 0x74
    'vi',    // 0x75
    'vii',    // 0x76
    'viii',    // 0x77
    'ix',    // 0x78
    'x',    // 0x79
    'xi',    // 0x7a
    'xii',    // 0x7b
    'l',    // 0x7c
    'c',    // 0x7d
    'd',    // 0x7e
    'm',    // 0x7f
    '(D',    // 0x80
    'D)',    // 0x81
    '((|))',    // 0x82
    ')',    // 0x83
    '[?]',    // 0x84
    '[?]',    // 0x85
    '[?]',    // 0x86
    '[?]',    // 0x87
    '[?]',    // 0x88
    ' 0/3 ',    // 0x89
    '[?]',    // 0x8a
    '[?]',    // 0x8b
    '[?]',    // 0x8c
    '[?]',    // 0x8d
    '[?]',    // 0x8e
    '[?]',    // 0x8f
    '-',    // 0x90
    '|',    // 0x91
    '-',    // 0x92
    '|',    // 0x93
    '-',    // 0x94
    '|',    // 0x95
    '\\',    // 0x96
    '/',    // 0x97
    '\\',    // 0x98
    '/',    // 0x99
    '-',    // 0x9a
    '-',    // 0x9b
    '~',    // 0x9c
    '~',    // 0x9d
    '-',    // 0x9e
    '|',    // 0x9f
    '-',    // 0xa0
    '|',    // 0xa1
    '-',    // 0xa2
    '-',    // 0xa3
    '-',    // 0xa4
    '|',    // 0xa5
    '-',    // 0xa6
    '|',    // 0xa7
    '|',    // 0xa8
    '-',    // 0xa9
    '-',    // 0xaa
    '-',    // 0xab
    '-',    // 0xac
    '-',    // 0xad
    '-',    // 0xae
    '|',    // 0xaf
    '|',    // 0xb0
    '|',    // 0xb1
    '|',    // 0xb2
    '|',    // 0xb3
    '|',    // 0xb4
    '|',    // 0xb5
    '^',    // 0xb6
    'V',    // 0xb7
    '\\',    // 0xb8
    '=',    // 0xb9
    'V',    // 0xba
    '^',    // 0xbb
    '-',    // 0xbc
    '-',    // 0xbd
    '|',    // 0xbe
    '|',    // 0xbf
    '-',    // 0xc0
    '-',    // 0xc1
    '|',    // 0xc2
    '|',    // 0xc3
    '=',    // 0xc4
    '|',    // 0xc5
    '=',    // 0xc6
    '=',    // 0xc7
    '|',    // 0xc8
    '=',    // 0xc9
    '|',    // 0xca
    '=',    // 0xcb
    '=',    // 0xcc
    '=',    // 0xcd
    '=',    // 0xce
    '=',    // 0xcf
    '=',    // 0xd0
    '|',    // 0xd1
    '=',    // 0xd2
    '|',    // 0xd3
    '=',    // 0xd4
    '|',    // 0xd5
    '\\',    // 0xd6
    '/',    // 0xd7
    '\\',    // 0xd8
    '/',    // 0xd9
    '=',    // 0xda
    '=',    // 0xdb
    '~',    // 0xdc
    '~',    // 0xdd
    '|',    // 0xde
    '|',    // 0xdf
    '-',    // 0xe0
    '|',    // 0xe1
    '-',    // 0xe2
    '|',    // 0xe3
    '-',    // 0xe4
    '-',    // 0xe5
    '-',    // 0xe6
    '|',    // 0xe7
    '-',    // 0xe8
    '|',    // 0xe9
    '|',    // 0xea
    '|',    // 0xeb
    '|',    // 0xec
    '|',    // 0xed
    '|',    // 0xee
    '|',    // 0xef
    '-',    // 0xf0
    '\\',    // 0xf1
    '\\',    // 0xf2
    '|',    // 0xf3
    '[?]',    // 0xf4
    '[?]',    // 0xf5
    '[?]',    // 0xf6
    '[?]',    // 0xf7
    '[?]',    // 0xf8
    '[?]',    // 0xf9
    '[?]',    // 0xfa
    '[?]',    // 0xfb
    '[?]',    // 0xfc
    '[?]',    // 0xfd
    '[?]',    // 0xfe
];
