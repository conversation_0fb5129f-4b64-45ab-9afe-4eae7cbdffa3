<?php

// code source:  https://github.com/devgeniem/wp-sanitize-accented-uploads/blob/master/plugin.php#L152
// table source: http://www.i18nqa.com/debug/utf8-debug.html

return [
    // 3 char errors
    'â€š' => '‚',
    'â€ž' => '„',
    'â€¦' => '…',
    'â€¡' => '‡',
    'â€°' => '‰',
    'â€¹' => '‹',
    'â€˜' => '‘',
    'â€™' => '’',
    'â€œ' => '“',
    'â€¢' => '•',
    'â€“' => '–',
    'â€”' => '—',
    'â„¢' => '™',
    'â€º' => '›',
    'â‚¬' => '€',
    // 2 char errors
    "\xc2\x80" => "\xe2\x82\xac", // EURO SIGN
    "\xc2\x82" => "\xe2\x80\x9a", // SINGLE LOW-9 QUOTATION MARK
    "\xc2\x83" => "\xc6\x92", // LATIN SMALL LETTER F WITH HOOK
    "\xc2\x84" => "\xe2\x80\x9e", // DOUBLE LOW-9 QUOTATION MARK
    "\xc2\x85" => "\xe2\x80\xa6", // HORIZONTAL ELLIPSIS
    "\xc2\x86" => "\xe2\x80\xa0", // DAGGER
    "\xc2\x87" => "\xe2\x80\xa1", // DOUBLE DAGGER
    "\xc2\x88" => "\xcb\x86", // MODIFIER LETTER CIRCUMFLEX ACCENT
    "\xc2\x89" => "\xe2\x80\xb0", // PER MILLE SIGN
    "\xc2\x8a" => "\xc5\xa0", // LATIN CAPITAL LETTER S WITH CARON
    "\xc2\x8b" => "\xe2\x80\xb9", // SINGLE LEFT-POINTING ANGLE QUOTE
    "\xc2\x8c" => "\xc5\x92", // LATIN CAPITAL LIGATURE OE
    "\xc2\x8e" => "\xc5\xbd", // LATIN CAPITAL LETTER Z WITH CARON
    "\xc2\x91" => "\xe2\x80\x98", // LEFT SINGLE QUOTATION MARK
    "\xc2\x92" => "\xe2\x80\x99", // RIGHT SINGLE QUOTATION MARK
    "\xc2\x93" => "\xe2\x80\x9c", // LEFT DOUBLE QUOTATION MARK
    "\xc2\x94" => "\xe2\x80\x9d", // RIGHT DOUBLE QUOTATION MARK
    "\xc2\x95" => "\xe2\x80\xa2", // BULLET
    "\xc2\x96" => "\xe2\x80\x93", // EN DASH
    "\xc2\x97" => "\xe2\x80\x94", // EM DASH
    "\xc2\x98" => "\xcb\x9c", // SMALL TILDE
    'Ã‚'       => 'Â',
    'Æ’'       => 'ƒ',
    'Ãƒ'       => 'Ã',
    'Ã„'       => 'Ä',
    'Ã…'       => 'Å',
    //'â€'       => '†', // duplicate key
    'Ã†' => 'Æ',
    'Ã‡' => 'Ç',
    'Ë†' => 'ˆ',
    'Ãˆ' => 'È',
    'Ã‰' => 'É',
    'ÃŠ' => 'Ê',
    'Ã‹' => 'Ë',
    'Å’' => 'Œ',
    'ÃŒ' => 'Ì',
    'Å½' => 'Ž',
    'ÃŽ' => 'Î',
    'Ã‘' => 'Ñ',
    'Ã’' => 'Ò',
    'Ã“' => 'Ó',
    'â€' => '”',
    'Ã”' => 'Ô',
    'Ã•' => 'Õ',
    'Ã–' => 'Ö',
    'Ã—' => '×',
    'Ëœ' => '˜',
    'Ã˜' => 'Ø',
    'Ã™' => 'Ù',
    'Å¡' => 'š',
    'Ãš' => 'Ú',
    'Ã›' => 'Û',
    'Å“' => 'œ',
    'Ãœ' => 'Ü',
    'Å¾' => 'ž',
    'Ãž' => 'Þ',
    'Å¸' => 'Ÿ',
    'ÃŸ' => 'ß',
    'Â¡' => '¡',
    'Ã¡' => 'á',
    'Â¢' => '¢',
    'Ã¢' => 'â',
    'Â£' => '£',
    'Ã£' => 'ã',
    'Â¤' => '¤',
    'Ã¤' => 'ä',
    'Â¥' => '¥',
    'Ã¥' => 'å',
    'Â¦' => '¦',
    'Ã¦' => 'æ',
    'Â§' => '§',
    'Ã§' => 'ç',
    'Â¨' => '¨',
    'Ã¨' => 'è',
    'Â©' => '©',
    'Ã©' => 'é',
    'Âª' => 'ª',
    'Ãª' => 'ê',
    'Â«' => '«',
    'Ã«' => 'ë',
    'Â¬' => '¬',
    'Ã¬' => 'ì',
    'Â®' => '®',
    'Ã®' => 'î',
    'Â¯' => '¯',
    'Ã¯' => 'ï',
    'Â°' => '°',
    'Ã°' => 'ð',
    'Â±' => '±',
    'Ã±' => 'ñ',
    'Â²' => '²',
    'Ã²' => 'ò',
    'Â³' => '³',
    'Ã³' => 'ó',
    'Â´' => '´',
    'Ã´' => 'ô',
    'Âµ' => 'µ',
    'Ãµ' => 'õ',
    'Â¶' => '¶',
    'Ã¶' => 'ö',
    'Â·' => '·',
    'Ã·' => '÷',
    'Â¸' => '¸',
    'Ã¸' => 'ø',
    'Â¹' => '¹',
    'Ã¹' => 'ù',
    'Âº' => 'º',
    'Ãº' => 'ú',
    'Â»' => '»',
    'Ã»' => 'û',
    'Â¼' => '¼',
    'Ã¼' => 'ü',
    'Â½' => '½',
    'Ã½' => 'ý',
    'Â¾' => '¾',
    'Ã¾' => 'þ',
    'Â¿' => '¿',
    'Ã¿' => 'ÿ',
    'Ã€' => 'À',
    // 1 char errors last (don't use them, because of false-positives)
    //'Ã'        => 'Á',
    //'Å'        => 'Š',
    //'Ã'        => 'Í',
    //'Ã'        => 'Ï',
    //'Ã'        => 'Ð',
    //'Ã'        => 'Ý',
    //'Ã'        => 'à',
    //'Ã'        => 'í',
];
