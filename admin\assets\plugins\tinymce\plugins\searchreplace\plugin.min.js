/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.1 (2020-05-27)
 */
!function(c){"use strict";var e,t,n,r,d=function(e){var t=e;return{get:function(){return t},set:function(e){t=e}}},o=tinymce.util.Tools.resolve("tinymce.PluginManager"),h=function(){return(h=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},i=function(e){return function(){return e}},a=i(!1),m=i(!0),u=i("[!-#%-*,-\\/:;?@\\[-\\]_{}\xa1\xab\xb7\xbb\xbf;\xb7\u055a-\u055f\u0589\u058a\u05be\u05c0\u05c3\u05c6\u05f3\u05f4\u0609\u060a\u060c\u060d\u061b\u061e\u061f\u066a-\u066d\u06d4\u0700-\u070d\u07f7-\u07f9\u0830-\u083e\u085e\u0964\u0965\u0970\u0df4\u0e4f\u0e5a\u0e5b\u0f04-\u0f12\u0f3a-\u0f3d\u0f85\u0fd0-\u0fd4\u0fd9\u0fda\u104a-\u104f\u10fb\u1361-\u1368\u1400\u166d\u166e\u169b\u169c\u16eb-\u16ed\u1735\u1736\u17d4-\u17d6\u17d8-\u17da\u1800-\u180a\u1944\u1945\u1a1e\u1a1f\u1aa0-\u1aa6\u1aa8-\u1aad\u1b5a-\u1b60\u1bfc-\u1bff\u1c3b-\u1c3f\u1c7e\u1c7f\u1cd3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205e\u207d\u207e\u208d\u208e\u3008\u3009\u2768-\u2775\u27c5\u27c6\u27e6-\u27ef\u2983-\u2998\u29d8-\u29db\u29fc\u29fd\u2cf9-\u2cfc\u2cfe\u2cff\u2d70\u2e00-\u2e2e\u2e30\u2e31\u3001-\u3003\u3008-\u3011\u3014-\u301f\u3030\u303d\u30a0\u30fb\ua4fe\ua4ff\ua60d-\ua60f\ua673\ua67e\ua6f2-\ua6f7\ua874-\ua877\ua8ce\ua8cf\ua8f8-\ua8fa\ua92e\ua92f\ua95f\ua9c1-\ua9cd\ua9de\ua9df\uaa5c-\uaa5f\uaade\uaadf\uabeb\ufd3e\ufd3f\ufe10-\ufe19\ufe30-\ufe52\ufe54-\ufe61\ufe63\ufe68\ufe6a\ufe6b\uff01-\uff03\uff05-\uff0a\uff0c-\uff0f\uff1a\uff1b\uff1f\uff20\uff3b-\uff3d\uff3f\uff5b\uff5d\uff5f-\uff65]"),l=function(){return f},f=(e=function(e){return e.isNone()},{fold:function(e,t){return e()},is:a,isSome:a,isNone:m,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:i(null),getOrUndefined:i(undefined),or:n,orThunk:t,map:l,each:function(){},bind:l,exists:a,forall:m,filter:l,equals:e,equals_:e,toArray:function(){return[]},toString:i("none()")}),s=function(n){var e=i(n),t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:m,isNone:a,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return s(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?o:f},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(a,function(e){return t(n,e)})}};return o},g={some:s,none:l,from:function(e){return null===e||e===undefined?f:s(e)}},p=u,v=tinymce.util.Tools.resolve("tinymce.util.Tools"),y=function(r){return function(e){return n=typeof(t=e),(null===t?"null":"object"==n&&(Array.prototype.isPrototypeOf(t)||t.constructor&&"Array"===t.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(t)||t.constructor&&"String"===t.constructor.name)?"string":n)===r;var t,n}},x=function(t){return function(e){return typeof e===t}},w=y("string"),b=y("array"),O=x("boolean"),C=x("number"),T=Array.prototype.slice,N=Array.prototype.push,E=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o)}return r},k=function(e,t){for(var n=0,r=e.length;n<r;n++){t(e[n],n)}},S=function(e,t){for(var n=e.length-1;0<=n;n--){t(e[n],n)}},A=function(e,t){return function(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!b(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);N.apply(t,e[n])}return t}(E(e,t))},D=Object.hasOwnProperty,M=function(e,t){return D.call(e,t)},B=("undefined"!=typeof c.window?c.window:Function("return this;")(),r=3,function(e){return e.dom().nodeType===r}),I=function(e,t,n){!function(e,t,n){if(!(w(n)||O(n)||C(n)))throw c.console.error("Invalid call to Attr.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")}(e.dom(),t,n)},P=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:i(e)}},F={fromHtml:function(e,t){var n=(t||c.document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw c.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return P(n.childNodes[0])},fromTag:function(e,t){var n=(t||c.document).createElement(e);return P(n)},fromText:function(e,t){var n=(t||c.document).createTextNode(e);return P(n)},fromDom:P,fromPoint:function(e,t,n){var r=e.dom();return g.from(r.elementFromPoint(t,n)).map(P)}},R=function(e,t){return{element:i(e),offset:i(t)}},W=function(e,t){var n=E(e.dom().childNodes,F.fromDom);return 0<n.length&&t<n.length?R(n[t],0):R(e,t)},j=function(t,n){var e;(e=t,g.from(e.dom().parentNode).map(F.fromDom)).each(function(e){e.dom().insertBefore(n.dom(),t.dom())})},q=function(e,t){var n;j(e,t),n=e,t.dom().appendChild(n.dom())};var V=function we(n,r){var t=function(e){return n(e)?g.from(e.dom().nodeValue):g.none()};return{get:function(e){if(!n(e))throw new Error("Can only get "+r+" value of a "+r+" node");return t(e).getOr("")},getOption:t,set:function(e,t){if(!n(e))throw new Error("Can only set raw "+r+" value of a "+r+" node");e.dom().nodeValue=t}}}(B,"text"),_=function(e){return V.get(e)},H=function(e,t){return n=t,i=(r=e)===undefined?c.document:r.dom(),1!==(o=i).nodeType&&9!==o.nodeType||0===o.childElementCount?[]:E(i.querySelectorAll(n),F.fromDom);var n,r,o,i},L=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),U=function(e,t){return e.isBlock(t)||M(e.schema.getShortEndedElements(),t.nodeName)},$=function(e,t){return"false"===e.getContentEditable(t)},z=function(e,t){return!e.isBlock(t)&&M(e.schema.getWhiteSpaceElements(),t.nodeName)},G=function(){return{sOffset:0,fOffset:0,elements:[]}},K=function(e,t){return W(F.fromDom(e),t)},J=function(e,t,n,r,o,i){void 0===i&&(i=!0);for(var a=i?t(!1):n;a;){var c=$(e,a);if(c||z(e,a)){if(c?r.cef(a):r.boundary(a))break;a=t(!0)}else{if(U(e,a)){if(r.boundary(a))break}else 3===a.nodeType&&r.text(a);if(a===o)break;a=t(!1)}}},Q=function(e,t,n,r,o){if(!(U(i=e,a=n)||$(i,a)||z(i,a)||(u=a,"true"===(c=i).getContentEditable(u)&&"false"===c.getContentEditableParent(u.parentNode)))){var i,a,c,u,l=e.getParent(r,e.isBlock),f=new L(n,l),s=o?f.next:f.prev;J(e,s,n,{boundary:m,cef:m,text:function(e){o?t.fOffset+=e.length:t.sOffset+=e.length,t.elements.push(F.fromDom(e))}})}},X=function(e,t,n,r,o,i){void 0===i&&(i=!0);var a=new L(n,t),c=[],u=G();Q(e,u,n,t,!1);var l=function(){return 0<u.elements.length&&(c.push(u),u=G()),!1};return J(e,a.next,n,{boundary:l,cef:function(e){return l(),o&&c.push.apply(c,o.cef(e)),!1},text:function(e){u.elements.push(F.fromDom(e)),o&&o.text(e,u)}},r,i),r&&Q(e,u,r,t,!0),l(),c},Y=function(i,e){var n=K(e.startContainer,e.startOffset),r=n.element().dom(),o=K(e.endContainer,e.endOffset),a=o.element().dom();return X(i,e.commonAncestorContainer,r,a,{text:function(e,t){e===a?t.fOffset+=e.length-o.offset():e===r&&(t.sOffset+=n.offset())},cef:function(e){var t,n,r,o=A(H(F.fromDom(e),"*[contenteditable=true]"),function(e){var t=e.dom();return X(i,t,t)});return t=o,n=function(e,t){return n=e.elements[0].dom(),r=t.elements[0].dom(),o=n,i=r,a=c.Node.DOCUMENT_POSITION_PRECEDING,0!=(o.compareDocumentPosition(i)&a)?1:-1;var n,r,o,i,a},(r=T.call(t,0)).sort(n),r}},!1)},Z=function(e,t){return t.collapsed?[]:Y(e,t)},ee=function(e,t){var n=e.createRng();return n.selectNode(t),Z(e,n)},te=function(e,a){var t,n;return function(e,t){if(0===e.length)return[];for(var n=t(e[0]),r=[],o=[],i=0,a=e.length;i<a;i++){var c=e[i],u=t(c);u!==n&&(r.push(o),o=[]),n=u,o.push(c)}return 0!==o.length&&r.push(o),r}((t=function(e,n){var t=_(n),r=e.last,o=r+t.length,i=A(a,function(e,t){return e.start<o&&e.finish>r?[{element:n,start:Math.max(r,e.start)-r,finish:Math.min(o,e.finish)-r,matchId:t}]:[]});return{results:e.results.concat(i),last:o}},n={results:[],last:0},k(e,function(e){n=t(n,e)}),n.results),function(e){return e.matchId})},ne=function(o,e){return A(e,function(e){var t=e.elements,n=E(t,_).join(""),r=function(e,t,n,r){void 0===n&&(n=0),void 0===r&&(r=e.length);var o=t.regex;o.lastIndex=n;for(var i,a=[];i=o.exec(e);){var c=i[t.matchIndex],u=i.index+i[0].indexOf(c),l=u+c.length;if(r<l)break;a.push({start:u,finish:l}),o.lastIndex=l}return a}(n,o,e.sOffset,n.length-e.fOffset);return te(t,r)})},re=function(e,i){S(e,function(e,o){S(e,function(e){var t=F.fromDom(i.cloneNode(!1));I(t,"data-mce-index",o);var n=e.element.dom();if(n.length===e.finish&&0===e.start)q(e.element,t);else{n.length!==e.finish&&n.splitText(e.finish);var r=n.splitText(e.start);q(F.fromDom(r),t)}})})},oe=function(e,t,n,r){var o,i=n.getBookmark(),a=e.select("td[data-mce-selected],th[data-mce-selected]"),c=0<a.length?(o=e,A(a,function(e){return ee(o,e)})):Z(e,n.getRng()),u=ne(t,c);return re(u,r),n.moveToBookmark(i),u.length},ie=function(e){var t=e.getAttribute("data-mce-index");return"number"==typeof t?""+t:t},ae=function(e,t,n,r){var o,i,a,c,u,l,f;return(i=e.dom.create("span",{"data-mce-bogus":1})).className="mce-match-marker",o=e.getBody(),pe(e,t,!1),r?oe(e.dom,n,e.selection,i):(a=e.dom,c=n,u=i,l=ee(a,o),f=ne(c,l),re(f,u),f.length)},ce=function(e){var t=e.parentNode;e.firstChild&&t.insertBefore(e.firstChild,e),e.parentNode.removeChild(e)},ue=function(e,t){var n,r=[];if((n=v.toArray(e.getBody().getElementsByTagName("span"))).length)for(var o=0;o<n.length;o++){var i=ie(n[o]);null!==i&&i.length&&i===t.toString()&&r.push(n[o])}return r},le=function(e,t,n){var r=t.get(),o=r.index,i=e.dom;(n=!1!==n)?o+1===r.count?o=0:o++:o-1==-1?o=r.count-1:o--,i.removeClass(ue(e,r.index),"mce-match-marker-selected");var a=ue(e,o);return a.length?(i.addClass(ue(e,o),"mce-match-marker-selected"),e.selection.scrollIntoView(a[0]),o):-1},fe=function(e,t){var n=t.parentNode;e.remove(t),e.isEmpty(n)&&e.remove(n)},se=function(e,t,n,r,o,i){var a,c,u=(a=o,c="("+n.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&").replace(/\s/g,"[^\\S\\r\\n]")+")",a?"(?:^|\\s|"+p()+")"+c+"(?=$|\\s|"+p()+")":c),l={regex:new RegExp(u,r?"g":"gi"),matchIndex:1},f=ae(e,t,l,i);if(f){var s=le(e,t,!0);t.set({index:s,count:f,text:n,matchCase:r,wholeWord:o,inSelection:i})}return f},de=function(e,t){var n=le(e,t,!0);t.set(h(h({},t.get()),{index:n}))},me=function(e,t){var n=le(e,t,!1);t.set(h(h({},t.get()),{index:n}))},he=function(e){var t=ie(e);return null!==t&&0<t.length},ge=function(e,t,n,r,o){var i,a,c,u,l,f=t.get(),s=f.index,d=s;for(r=!1!==r,c=e.getBody(),a=v.grep(v.toArray(c.getElementsByTagName("span")),he),i=0;i<a.length;i++){var m=ie(a[i]);if(u=l=parseInt(m,10),o||u===f.index){for(n.length?(a[i].firstChild.nodeValue=n,ce(a[i])):fe(e.dom,a[i]);a[++i];){if((u=parseInt(ie(a[i]),10))!==l){i--;break}fe(e.dom,a[i])}r&&d--}else s<l&&a[i].setAttribute("data-mce-index",String(l-1))}return t.set(h(h({},f),{count:o?0:f.count-1,index:d})),(r?de:me)(e,t),!o&&0<t.get().count},pe=function(e,t,n){var r,o,i,a,c=t.get();for(o=v.toArray(e.getBody().getElementsByTagName("span")),r=0;r<o.length;r++){var u=ie(o[r]);null!==u&&u.length&&(u===c.index.toString()&&(i=i||o[r].firstChild,a=o[r].firstChild),ce(o[r]))}if(t.set(h(h({},c),{index:-1,count:0,text:""})),i&&a){var l=e.dom.createRng();return l.setStart(i,0),l.setEnd(a,a.data.length),!1!==n&&e.selection.setRng(l),l}},ve=tinymce.util.Tools.resolve("tinymce.Env"),ye=function(i,a){var t,e=(t=d(g.none()),{clear:function(){t.set(g.none())},set:function(e){t.set(g.some(e))},isSet:function(){return t.get().isSome()},on:function(e){t.get().each(e)}});i.undoManager.add();var n=v.trim(i.selection.getContent({format:"text"}));function c(e){(1<a.get().count?e.enable:e.disable)("next"),(1<a.get().count?e.enable:e.disable)("prev")}var u=function(e,t){var n=t?e.disable:e.enable;k(["replace","replaceall","prev","next"],n)};var l=function(e,t){ve.browser.isSafari()&&ve.deviceType.isTouch()&&("find"===t||"replace"===t||"replaceall"===t)&&e.focus(t)},f=function(e){pe(i,a,!1),u(e,!0),c(e)},s=function(e){var t=e.getData(),n=a.get();if(t.findtext.length){if(n.text===t.findtext&&n.matchCase===t.matchcase&&n.wholeWord===t.wholewords)de(i,a);else{var r=se(i,a,t.findtext,t.matchcase,t.wholewords,t.inselection);r<=0&&!function o(e){i.windowManager.alert("Could not find the specified string.",function(){e.focus("findtext")})}(e),u(e,0===r)}c(e)}else f(e)},r=a.get(),o={title:"Find and Replace",size:"normal",body:{type:"panel",items:[{type:"bar",items:[{type:"input",name:"findtext",placeholder:"Find",maximized:!0,inputMode:"search"},{type:"button",name:"prev",text:"Previous",icon:"action-prev",disabled:!0,borderless:!0},{type:"button",name:"next",text:"Next",icon:"action-next",disabled:!0,borderless:!0}]},{type:"input",name:"replacetext",placeholder:"Replace with",inputMode:"search"}]},buttons:[{type:"menu",name:"options",icon:"preferences",tooltip:"Preferences",align:"start",items:[{type:"togglemenuitem",name:"matchcase",text:"Match case"},{type:"togglemenuitem",name:"wholewords",text:"Find whole words only"},{type:"togglemenuitem",name:"inselection",text:"Find in selection"}]},{type:"custom",name:"find",text:"Find",primary:!0},{type:"custom",name:"replace",text:"Replace",disabled:!0},{type:"custom",name:"replaceall",text:"Replace All",disabled:!0}],initialData:{findtext:n,replacetext:"",wholewords:r.wholeWord,matchcase:r.matchCase,inselection:r.inSelection},onChange:function(e,t){"findtext"===t.name&&0<a.get().count&&f(e)},onAction:function(e,t){var n,r,o=e.getData();switch(t.name){case"find":s(e);break;case"replace":(ge(i,a,o.replacetext)?c:f)(e);break;case"replaceall":ge(i,a,o.replacetext,!0,!0),f(e);break;case"prev":me(i,a),c(e);break;case"next":de(i,a),c(e);break;case"matchcase":case"wholewords":case"inselection":n=e.getData(),r=a.get(),a.set(h(h({},r),{matchCase:n.matchcase,wholeWord:n.wholewords,inSelection:n.inselection})),f(e)}l(e,t.name)},onSubmit:function(e){s(e),l(e,"find")},onClose:function(){i.focus(),pe(i,a),i.undoManager.add()}};e.set(i.windowManager.open(o,{inline:"toolbar"}))},xe=function(e,t){return function(){ye(e,t)}};!function be(){o.add("searchreplace",function(e){var t,n,r,o,i,a,c=d({index:-1,count:0,text:"",matchCase:!1,wholeWord:!1,inSelection:!1});return n=c,(t=e).addCommand("SearchReplace",function(){ye(t,n)}),o=c,(r=e).ui.registry.addMenuItem("searchreplace",{text:"Find and replace...",shortcut:"Meta+F",onAction:xe(r,o),icon:"search"}),r.ui.registry.addButton("searchreplace",{tooltip:"Find and replace",onAction:xe(r,o),icon:"search"}),r.shortcuts.add("Meta+F","",xe(r,o)),i=e,a=c,{done:function(e){return pe(i,a,e)},find:function(e,t,n,r){return void 0===r&&(r=!1),se(i,a,e,t,n,r)},next:function(){return de(i,a)},prev:function(){return me(i,a)},replace:function(e,t,n){return ge(i,a,e,t,n)}}})}()}(window);