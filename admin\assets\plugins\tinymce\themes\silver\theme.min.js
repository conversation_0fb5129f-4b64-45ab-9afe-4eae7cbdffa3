/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.1 (2020-05-27)
 */
!function(nt){"use strict";var Z=function(){},d=function(e,o){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e(o.apply(null,t))}},at=function(t){return function(){return t}},ct=function(t){return t};function g(o){for(var r=[],t=1;t<arguments.length;t++)r[t-1]=arguments[t];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=r.concat(t);return o.apply(null,e)}}var x=function(n){return function(t){return!n(t)}},u=function(t){return function(){throw new Error(t)}},c=at(!1),i=at(!0),t=tinymce.util.Tools.resolve("tinymce.ThemeManager"),et=function(){return(et=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t}).apply(this,arguments)};function y(t,n){var e={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&n.indexOf(o)<0&&(e[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(t);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(e[o[r]]=t[o[r]])}return e}function b(){for(var t=0,n=0,e=arguments.length;n<e;n++)t+=arguments[n].length;var o=Array(t),r=0;for(n=0;n<e;n++)for(var i=arguments[n],u=0,a=i.length;u<a;u++,r++)o[r]=i[u];return o}var n,e,o,r,a,s,l=function(){return f},f=(n=function(t){return t.isNone()},{fold:function(t,n){return t()},is:c,isSome:c,isNone:i,getOr:o=function(t){return t},getOrThunk:e=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:at(null),getOrUndefined:at(undefined),or:o,orThunk:e,map:l,each:Z,bind:l,exists:c,forall:i,filter:l,equals:n,equals_:n,toArray:function(){return[]},toString:at("none()")}),m=function(e){var t=at(e),n=function(){return r},o=function(t){return t(e)},r={fold:function(t,n){return n(e)},is:function(t){return e===t},isSome:i,isNone:c,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:n,orThunk:n,map:function(t){return m(t(e))},each:function(t){t(e)},bind:o,exists:o,forall:o,filter:function(t){return t(e)?r:f},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(t){return t.is(e)},equals_:function(t,n){return t.fold(c,function(t){return n(e,t)})}};return r},st={some:m,none:l,from:function(t){return null===t||t===undefined?f:m(t)}},p=function(e){return{is:function(t){return e===t},isValue:i,isError:c,getOr:at(e),getOrThunk:at(e),getOrDie:at(e),or:function(t){return p(e)},orThunk:function(t){return p(e)},fold:function(t,n){return n(e)},map:function(t){return p(t(e))},mapError:function(t){return p(e)},each:function(t){t(e)},bind:function(t){return t(e)},exists:function(t){return t(e)},forall:function(t){return t(e)},toOption:function(){return st.some(e)}}},h=function(e){return{is:c,isValue:c,isError:i,getOr:ct,getOrThunk:function(t){return t()},getOrDie:function(){return u(String(e))()},or:function(t){return t},orThunk:function(t){return t()},fold:function(t,n){return t(e)},map:function(t){return h(e)},mapError:function(t){return h(t(e))},each:Z,bind:function(t){return h(e)},exists:c,forall:i,toOption:st.none}},ot={value:p,error:h,fromOption:function(t,n){return t.fold(function(){return h(n)},p)}},v=function(o){return function(t){return e=typeof(n=t),(null===n?"null":"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e)===o;var n,e}},w=function(n){return function(t){return typeof t===n}},S=v("string"),k=v("object"),C=v("array"),O=w("boolean"),_=(r=undefined,function(t){return r===t}),T=w("function"),rt=w("number"),E=function(t,n){if(C(t)){for(var e=0,o=t.length;e<o;++e)if(!n(t[e]))return!1;return!0}return!1},B=Array.prototype.slice,D=Array.prototype.indexOf,M=Array.prototype.push,A=function(t,n){return D.call(t,n)},F=function(t,n){return-1<A(t,n)},I=function(t,n){for(var e=0,o=t.length;e<o;e++){if(n(t[e],e))return!0}return!1},R=function(t,n){for(var e=[],o=0;o<t.length;o+=n){var r=B.call(t,o,o+n);e.push(r)}return e},V=function(t,n){for(var e=t.length,o=new Array(e),r=0;r<e;r++){var i=t[r];o[r]=n(i,r)}return o},it=function(t,n){for(var e=0,o=t.length;e<o;e++){n(t[e],e)}},H=function(t,n){for(var e=[],o=[],r=0,i=t.length;r<i;r++){var u=t[r];(n(u,r)?e:o).push(u)}return{pass:e,fail:o}},P=function(t,n){for(var e=[],o=0,r=t.length;o<r;o++){var i=t[o];n(i,o)&&e.push(i)}return e},z=function(t,n,e){return function(t,n){for(var e=t.length-1;0<=e;e--){n(t[e],e)}}(t,function(t){e=n(e,t)}),e},N=function(t,n,e){return it(t,function(t){e=n(e,t)}),e},L=function(t,n){return function(t,n,e){for(var o=0,r=t.length;o<r;o++){var i=t[o];if(n(i,o))return st.some(i);if(e(i,o))break}return st.none()}(t,n,c)},j=function(t,n){for(var e=0,o=t.length;e<o;e++){if(n(t[e],e))return st.some(e)}return st.none()},ut=function(t){for(var n=[],e=0,o=t.length;e<o;++e){if(!C(t[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+t);M.apply(n,t[e])}return n},U=function(t,n){return ut(V(t,n))},W=function(t,n){for(var e=0,o=t.length;e<o;++e){if(!0!==n(t[e],e))return!1}return!0},G=function(t){var n=B.call(t,0);return n.reverse(),n},X=function(t,n){return P(t,function(t){return!F(n,t)})},Y=function(t){return[t]},q=function(t,n){var e=B.call(t,0);return e.sort(n),e},K=function(t){return 0===t.length?st.none():st.some(t[0])},J=function(t){return 0===t.length?st.none():st.some(t[t.length-1])},$=T(Array.from)?Array.from:function(t){return B.call(t)},Q=function(t,n){for(var e=0;e<t.length;e++){var o=n(t[e],e);if(o.isSome())return o}return st.none()},lt=Object.keys,tt=Object.hasOwnProperty,ft=function(t,n){for(var e=lt(t),o=0,r=e.length;o<r;o++){var i=e[o];n(t[i],i)}},dt=function(t,e){return mt(t,function(t,n){return{k:n,v:e(t,n)}})},mt=function(t,o){var r={};return ft(t,function(t,n){var e=o(t,n);r[e.k]=e.v}),r},gt=function(t,n){var e,o,r,i,u={};return e=n,i=u,o=function(t,n){i[n]=t},r=Z,ft(t,function(t,n){(e(t,n)?o:r)(t,n)}),u},pt=function(t,e){var o=[];return ft(t,function(t,n){o.push(e(t,n))}),o},ht=function(t,n){for(var e=lt(t),o=0,r=e.length;o<r;o++){var i=e[o],u=t[i];if(n(u,i,t))return st.some(u)}return st.none()},vt=function(t){return pt(t,function(t){return t})},bt=function(t,n){return yt(t,n)?st.from(t[n]):st.none()},yt=function(t,n){return tt.call(t,n)},xt=function(t,n){return yt(t,n)&&t[n]!==undefined&&null!==t[n]},wt=function(u){if(!C(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],e={};return it(u,function(t,o){var n=lt(t);if(1!==n.length)throw new Error("one and only one name per case");var r=n[0],i=t[r];if(e[r]!==undefined)throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!C(i))throw new Error("case arguments must be an array");a.push(r),e[r]=function(){var t=arguments.length;if(t!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+t);for(var e=new Array(t),n=0;n<e.length;n++)e[n]=arguments[n];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[o].apply(null,e)},match:function(t){var n=lt(t);if(a.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+n.join(","));if(!W(a,function(t){return F(n,t)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+a.join(", "));return t[r].apply(null,e)},log:function(t){nt.console.log(t,{constructors:a,constructor:r,params:e})}}}}),e},St=Object.prototype.hasOwnProperty,kt=function(u){return function(){for(var t=new Array(arguments.length),n=0;n<t.length;n++)t[n]=arguments[n];if(0===t.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<t.length;o++){var r=t[o];for(var i in r)St.call(r,i)&&(e[i]=u(e[i],r[i]))}return e}},Ct=kt(function(t,n){return k(t)&&k(n)?Ct(t,n):n}),Ot=kt(function(t,n){return n}),_t=function(e){var o,r=!1;return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return r||(r=!0,o=e.apply(null,t)),o}};(s=a=a||{})[s.Error=0]="Error",s[s.Value=1]="Value";var Tt,Et,Bt=function(t,n,e){return t.stype===a.Error?n(t.serror):e(t.svalue)},Dt=function(t){return{stype:a.Value,svalue:t}},Mt=function(t){return{stype:a.Error,serror:t}},At=function(t){return t.fold(Mt,Dt)},Ft=function(t){return Bt(t,ot.error,ot.value)},It=Dt,Rt=function(t){var n=[],e=[];return it(t,function(t){Bt(t,function(t){return e.push(t)},function(t){return n.push(t)})}),{values:n,errors:e}},Vt=Mt,Ht=function(t,n){return t.stype===a.Value?n(t.svalue):t},Pt=function(t,n){return t.stype===a.Error?n(t.serror):t},zt=function(t,n){return t.stype===a.Value?{stype:a.Value,svalue:n(t.svalue)}:t},Nt=function(t,n){return t.stype===a.Error?{stype:a.Error,serror:n(t.serror)}:t},Lt=wt([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),jt=function(t){return Lt.defaultedThunk(at(t))},Ut=Lt.strict,Wt=Lt.asOption,Gt=Lt.defaultedThunk,Xt=(Lt.asDefaultedOptionThunk,Lt.mergeWithThunk),Yt=function(t,n){var e;return(e={})[t]=n,e},qt=(wt([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(t,n){return e=n,o={},ft(t,function(t,n){F(e,n)||(o[n]=t)}),o;var e,o}),Kt=function(t,n){return Yt(t,n)},Jt=function(t){return n={},it(t,function(t){n[t.key]=t.value}),n;var n},$t=function(t,n){var e,o,r,i,u,a=(e=[],o=[],it(t,function(t){t.fold(function(t){e.push(t)},function(t){o.push(t)})}),{errors:e,values:o});return 0<a.errors.length?(u=a.errors,ot.error(ut(u))):(i=n,0===(r=a.values).length?ot.value(i):ot.value(Ct(i,Ot.apply(undefined,r))))},Qt=function(t){return d(Vt,ut)(t)},Zt=function(t,n){var e,o,r=Rt(t);return 0<r.errors.length?Qt(r.errors):(e=r.values,o=n,0<e.length?It(Ct(o,Ot.apply(undefined,e))):It(o))},tn=function(t){var n=Rt(t);return 0<n.errors.length?Qt(n.errors):It(n.values)},nn=function(t){return k(t)&&100<lt(t).length?" removed due to size":JSON.stringify(t,null,2)},en=function(t,n){return Vt([{path:t,getErrorInfo:n}])},on=wt([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),rn=function(e,o,r){return bt(o,r).fold(function(){return t=r,n=o,en(e,function(){return'Could not find valid *strict* value for "'+t+'" in '+nn(n)});var t,n},It)},un=function(t,n,e){var o=bt(t,n).fold(function(){return e(t)},ct);return It(o)},an=function(a,c,t,s){return t.fold(function(r,e,t,o){var i=function(t){var n=o.extract(a.concat([r]),s,t);return zt(n,function(t){return Yt(e,s(t))})},u=function(t){return t.fold(function(){var t=Yt(e,s(st.none()));return It(t)},function(t){var n=o.extract(a.concat([r]),s,t);return zt(n,function(t){return Yt(e,s(st.some(t)))})})};return t.fold(function(){return Ht(rn(a,c,r),i)},function(t){return Ht(un(c,r,t),i)},function(){return Ht(It(bt(c,r)),u)},function(t){return Ht((e=t,o=bt(n=c,r).map(function(t){return!0===t?e(n):t}),It(o)),u);var n,e,o},function(t){var n=t(c),e=zt(un(c,r,at({})),function(t){return Ct(n,t)});return Ht(e,i)})},function(t,n){var e=n(c);return It(Yt(t,s(e)))})},cn=function(o){return{extract:function(e,t,n){return Pt(o(n,t),function(t){return n=t,en(e,function(){return n});var n})},toString:function(){return"val"}}},sn=function(t){var u=ln(t),a=z(t,function(n,t){return t.fold(function(t){return Ct(n,Kt(t,!0))},at(n))},{});return{extract:function(t,n,e){var o,r=O(e)?[]:lt(gt(e,function(t){return t!==undefined&&null!==t})),i=P(r,function(t){return!xt(a,t)});return 0===i.length?u.extract(t,n,e):(o=i,en(t,function(){return"There are unsupported fields: ["+o.join(", ")+"] specified"}))},toString:u.toString}},ln=function(a){return{extract:function(t,n,e){return o=t,r=e,i=n,u=V(a,function(t){return an(o,r,t,i)}),Zt(u,{});var o,r,i,u},toString:function(){return"obj{\n"+V(a,function(t){return t.fold(function(t,n,e,o){return t+" -> "+o.toString()},function(t,n){return"state("+t+")"})}).join("\n")+"}"}}},fn=function(r){return{extract:function(e,o,t){var n=V(t,function(t,n){return r.extract(e.concat(["["+n+"]"]),o,t)});return tn(n)},toString:function(){return"array("+r.toString()+")"}}},dn=function(a,c){return{extract:function(e,o,r){var t,n,i=lt(r),u=(t=e,n=i,fn(cn(a)).extract(t,ct,n));return Ht(u,function(t){var n=V(t,function(t){return on.field(t,t,Ut(),c)});return ln(n).extract(e,o,r)})},toString:function(){return"setOf("+c.toString()+")"}}},mn=at(cn(It)),gn=d(fn,ln),pn=on.state,hn=on.field,vn=function(e,n,o,r,i){return bt(r,i).fold(function(){return t=r,n=i,en(e,function(){return'The chosen schema: "'+n+'" did not exist in branches: '+nn(t)});var t,n},function(t){return t.extract(e.concat(["branch: "+i]),n,o)})},bn=function(r,i){return{extract:function(n,e,o){return bt(o,r).fold(function(){return t=r,en(n,function(){return'Choice schema did not contain choice key: "'+t+'"'});var t},function(t){return vn(n,e,o,i,t)})},toString:function(){return"chooseOn("+r+"). Possible values: "+lt(i)}}},yn=cn(It),xn=function(t){return gn(t)},wn=function(o){return{extract:function(t,n,e){return o().extract(t,n,e)},toString:function(){return o().toString()}}},Sn=function(n){return cn(function(t){return n(t).fold(Vt,It)})},kn=function(n,t){return dn(function(t){return At(n(t))},t)},Cn=function(t,n,e){return Ft((o=t,r=ct,i=e,u=n.extract([o],r,i),Nt(u,function(t){return{input:i,errors:t}})));var o,r,i,u},On=function(t){return t.fold(function(t){throw new Error(Tn(t))},ct)},_n=function(t,n,e){return On(Cn(t,n,e))},Tn=function(t){return"Errors: \n"+(n=t.errors,e=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n,V(e,function(t){return"Failed path: ("+t.path.join(" > ")+")\n"+t.getErrorInfo()}).join("\n"))+"\n\nInput object: "+nn(t.input);var n,e},En=function(t,n){return bn(t,n)},Bn=function(t,n){return bn(t,dt(n,ln))},Dn=at(yn),Mn=function(e,o){return cn(function(t){var n=typeof t;return e(t)?It(t):Vt("Expected type: "+o+" but got: "+n)})},An=Mn(rt,"number"),Fn=Mn(S,"string"),In=Mn(O,"boolean"),Rn=Mn(T,"function"),Vn=function(n){var t=function(t,n){for(var e=t.next();!e.done;){if(!n(e.value))return!1;e=t.next()}return!0};if(Object(n)!==n)return!0;switch({}.toString.call(n).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(n).every(function(t){return Vn(n[t])});case"Map":return t(n.keys(),Vn)&&t(n.values(),Vn);case"Set":return t(n.keys(),Vn);default:return!1}},Hn=cn(function(t){return Vn(t)?It(t):Vt("Expected value to be acceptable for sending via postMessage")}),Pn=function(n){return Sn(function(t){return F(n,t)?ot.value(t):ot.error('Unsupported value: "'+t+'", choose one of "'+n.join(", ")+'".')})},zn=function(t){return hn(t,t,Ut(),mn())},Nn=function(t,n){return hn(t,t,Ut(),n)},Ln=function(t){return Nn(t,Fn)},jn=function(t,n){return hn(t,t,Ut(),Pn(n))},Un=function(t){return Nn(t,Rn)},Wn=function(t,n){return hn(t,t,Ut(),ln(n))},Gn=function(t,n){return hn(t,t,Ut(),gn(n))},Xn=function(t,n){return hn(t,t,Ut(),fn(n))},Yn=function(t){return hn(t,t,Wt(),mn())},qn=function(t,n){return hn(t,t,Wt(),n)},Kn=function(t){return qn(t,An)},Jn=function(t){return qn(t,Fn)},$n=function(t){return qn(t,Rn)},Qn=function(t,n){return qn(t,ln(n))},Zn=function(t,n){return hn(t,t,jt(n),mn())},te=function(t,n,e){return hn(t,t,jt(n),e)},ne=function(t,n){return te(t,n,An)},ee=function(t,n){return te(t,n,Fn)},oe=function(t,n,e){return te(t,n,Pn(e))},re=function(t,n){return te(t,n,In)},ie=function(t,n){return te(t,n,Rn)},ue=function(t,n,e){return te(t,n,ln(e))},ae=function(t,n){return pn(t,n)},ce=function(t){var n=t;return{get:function(){return n},set:function(t){n=t}}},se=function(t){if(null===t||t===undefined)throw new Error("Node cannot be null or undefined");return{dom:at(t)}},le={fromHtml:function(t,n){var e=(n||nt.document).createElement("div");if(e.innerHTML=t,!e.hasChildNodes()||1<e.childNodes.length)throw nt.console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return se(e.childNodes[0])},fromTag:function(t,n){var e=(n||nt.document).createElement(t);return se(e)},fromText:function(t,n){var e=(n||nt.document).createTextNode(t);return se(e)},fromDom:se,fromPoint:function(t,n,e){var o=t.dom();return st.from(o.elementFromPoint(n,e)).map(se)}},fe=function(t,n){var e=function(t,n){for(var e=0;e<t.length;e++){var o=t[e];if(o.test(n))return o}return undefined}(t,n);if(!e)return{major:0,minor:0};var o=function(t){return Number(n.replace(e,"$"+t))};return me(o(1),o(2))},de=function(){return me(0,0)},me=function(t,n){return{major:t,minor:n}},ge={nu:me,detect:function(t,n){var e=String(n).toLowerCase();return 0===t.length?de():fe(t,e)},unknown:de},pe="Firefox",he=function(t){var n=t.current,e=t.version,o=function(t){return function(){return n===t}};return{current:n,version:e,isEdge:o("Edge"),isChrome:o("Chrome"),isIE:o("IE"),isOpera:o("Opera"),isFirefox:o(pe),isSafari:o("Safari")}},ve={unknown:function(){return he({current:undefined,version:ge.unknown()})},nu:he,edge:at("Edge"),chrome:at("Chrome"),ie:at("IE"),opera:at("Opera"),firefox:at(pe),safari:at("Safari")},be="Windows",ye="Android",xe="Solaris",we="FreeBSD",Se="ChromeOS",ke=function(t){var n=t.current,e=t.version,o=function(t){return function(){return n===t}};return{current:n,version:e,isWindows:o(be),isiOS:o("iOS"),isAndroid:o(ye),isOSX:o("OSX"),isLinux:o("Linux"),isSolaris:o(xe),isFreeBSD:o(we),isChromeOS:o(Se)}},Ce={unknown:function(){return ke({current:undefined,version:ge.unknown()})},nu:ke,windows:at(be),ios:at("iOS"),android:at(ye),linux:at("Linux"),osx:at("OSX"),solaris:at(xe),freebsd:at(we),chromeos:at(Se)},Oe=function(t,n){var e=String(n).toLowerCase();return L(t,function(t){return t.search(e)})},_e=function(t,e){return Oe(t,e).map(function(t){var n=ge.detect(t.versionRegexes,e);return{current:t.name,version:n}})},Te=function(t,e){return Oe(t,e).map(function(t){var n=ge.detect(t.versionRegexes,e);return{current:t.name,version:n}})},Ee=function(t,n){return-1!==t.indexOf(n)},Be=(Tt=/^\s+|\s+$/g,function(t){return t.replace(Tt,"")}),De=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Me=function(n){return function(t){return Ee(t,n)}},Ae=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(t){return Ee(t,"edge/")&&Ee(t,"chrome")&&Ee(t,"safari")&&Ee(t,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,De],search:function(t){return Ee(t,"chrome")&&!Ee(t,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(t){return Ee(t,"msie")||Ee(t,"trident")}},{name:"Opera",versionRegexes:[De,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Me("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Me("firefox")},{name:"Safari",versionRegexes:[De,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(t){return(Ee(t,"safari")||Ee(t,"mobile/"))&&Ee(t,"applewebkit")}}],Fe=[{name:"Windows",search:Me("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(t){return Ee(t,"iphone")||Ee(t,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Me("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Me("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Me("linux"),versionRegexes:[]},{name:"Solaris",search:Me("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Me("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Me("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Ie={browsers:at(Ae),oses:at(Fe)},Re=function(t,n){var e,o,r,i,u,a,c,s,l,f,d,m,g=Ie.browsers(),p=Ie.oses(),h=_e(g,t).fold(ve.unknown,ve.nu),v=Te(p,t).fold(Ce.unknown,Ce.nu);return{browser:h,os:v,deviceType:(o=h,r=t,i=n,u=(e=v).isiOS()&&!0===/ipad/i.test(r),a=e.isiOS()&&!u,c=e.isiOS()||e.isAndroid(),s=c||i("(pointer:coarse)"),l=u||!a&&c&&i("(min-device-width:768px)"),f=a||c&&!l,d=o.isSafari()&&e.isiOS()&&!1===/safari/i.test(r),m=!f&&!l&&!d,{isiPad:at(u),isiPhone:at(a),isTablet:at(l),isPhone:at(f),isTouch:at(s),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:at(d),isDesktop:at(m)})}},Ve=function(t){return nt.window.matchMedia(t).matches},He=_t(function(){return Re(nt.navigator.userAgent,Ve)}),Pe=function(){return He()},ze=function(t,n){var e=t.dom();if(1!==e.nodeType)return!1;var o=e;if(o.matches!==undefined)return o.matches(n);if(o.msMatchesSelector!==undefined)return o.msMatchesSelector(n);if(o.webkitMatchesSelector!==undefined)return o.webkitMatchesSelector(n);if(o.mozMatchesSelector!==undefined)return o.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")},Ne=function(t){return 1!==t.nodeType&&9!==t.nodeType||0===t.childElementCount},Le=function(t,n){return t.dom()===n.dom()},je=function(t,n){return e=t.dom(),o=n.dom(),r=e,i=o,u=nt.Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(r.compareDocumentPosition(i)&u);var e,o,r,i,u},Ue=function(t,n){return Pe().browser.isIE()?je(t,n):(e=n,o=t.dom(),r=e.dom(),o!==r&&o.contains(r));var e,o,r},We=function(t){return T(t)?t:at(!1)},Ge=function(t,n,e){for(var o=t.dom(),r=We(e);o.parentNode;){o=o.parentNode;var i=le.fromDom(o),u=n(i);if(u.isSome())return u;if(r(i))break}return st.none()},Xe=function(t,n,e){var o=n(t),r=We(e);return o.orThunk(function(){return r(t)?st.none():Ge(t,n,r)})},Ye=function(t,n){return Le(t.element(),n.event().target())},qe=function(t){if(!xt(t,"can")&&!xt(t,"abort")&&!xt(t,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(t,null,2)+" does not have can, abort, or run!");return _n("Extracting event.handler",sn([Zn("can",at(!0)),Zn("abort",at(!1)),Zn("run",Z)]),t)},Ke=function(e){var n,o,r,i,t=(o=function(t){return t.can},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return N(n,function(t,n){return t&&o(n).apply(undefined,e)},!0)}),u=(r=n=e,i=function(t){return t.abort},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return N(r,function(t,n){return t||i(n).apply(undefined,e)},!1)});return qe({can:t,abort:u,run:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];it(e,function(t){t.run.apply(undefined,n)})}})},Je=at("touchstart"),$e=at("touchmove"),Qe=at("touchend"),Ze=at("touchcancel"),to=at("mousedown"),no=at("mousemove"),eo=at("mouseout"),oo=at("mouseup"),ro=at("mouseover"),io=at("focusin"),uo=at("focusout"),ao=at("keydown"),co=at("keyup"),so=at("input"),lo=at("change"),fo=at("click"),mo=at("transitionend"),go=at("selectstart"),po={tap:at("alloy.tap")},ho=at("alloy.focus"),vo=at("alloy.blur.post"),bo=at("alloy.paste.post"),yo=at("alloy.receive"),xo=at("alloy.execute"),wo=at("alloy.focus.item"),So=po.tap,ko=at("alloy.longpress"),Co=at("alloy.sandbox.close"),Oo=at("alloy.typeahead.cancel"),_o=at("alloy.system.init"),To=at("alloy.system.touchmove"),Eo=at("alloy.system.touchend"),Bo=at("alloy.system.scroll"),Do=at("alloy.system.resize"),Mo=at("alloy.system.attached"),Ao=at("alloy.system.detached"),Fo=at("alloy.system.dismissRequested"),Io=at("alloy.system.repositionRequested"),Ro=at("alloy.focusmanager.shifted"),Vo=at("alloy.slotcontainer.visibility"),Ho=at("alloy.change.tab"),Po=at("alloy.dismiss.tab"),zo=at("alloy.highlight"),No=at("alloy.dehighlight"),Lo=function(t,n){Go(t,t.element(),n,{})},jo=function(t,n,e){Go(t,t.element(),n,e)},Uo=function(t){Lo(t,xo())},Wo=function(t,n,e){Go(t,n,e,{})},Go=function(t,n,e,o){var r=et({target:n},o);t.getSystem().triggerEvent(e,n,dt(r,at))},Xo=function(t,n,e,o){t.getSystem().triggerEvent(e,n,o.event())},Yo=function(t){return Jt(t)},qo=function(t,n){return{key:t,value:qe({abort:n})}},Ko=function(t){return{key:t,value:qe({run:function(t,n){n.event().prevent()}})}},Jo=function(t,n){return{key:t,value:qe({run:n})}},$o=function(t,e,o){return{key:t,value:qe({run:function(t,n){e.apply(undefined,[t,n].concat(o))}})}},Qo=function(t){return function(e){return{key:t,value:qe({run:function(t,n){Ye(t,n)&&e(t,n)}})}}},Zo=function(t,n,e){var o,r,i=n.partUids[e];return r=i,Jo(o=t,function(t,n){t.getSystem().getByUid(r).each(function(t){Xo(t,t.element(),o,n)})})},tr=function(t,r){return Jo(t,function(n,t){var e=t.event(),o=n.getSystem().getByDom(e.target()).fold(function(){return Xe(e.target(),function(t){return n.getSystem().getByDom(t).toOption()},at(!1)).getOr(n)},function(t){return t});r(n,o,t)})},nr=function(t){return Jo(t,function(t,n){n.cut()})},er=function(t,n){return Qo(t)(n)},or=Qo(Mo()),rr=Qo(Ao()),ir=Qo(_o()),ur=(Et=xo(),function(t){return Jo(Et,t)}),ar=function(t){return le.fromDom(t.dom().ownerDocument)},cr=function(t){return le.fromDom(t.dom().ownerDocument.documentElement)},sr=function(t){return le.fromDom(t.dom().ownerDocument.defaultView)},lr=function(t){return st.from(t.dom().parentNode).map(le.fromDom)},fr=function(t){return st.from(t.dom().offsetParent).map(le.fromDom)},dr=function(t){return V(t.dom().childNodes,le.fromDom)},mr=function(t,n){var e=t.dom().childNodes;return st.from(e[n]).map(le.fromDom)},gr=function(n,e){lr(n).each(function(t){t.dom().insertBefore(e.dom(),n.dom())})},pr=function(t,n){var e;(e=t,st.from(e.dom().nextSibling).map(le.fromDom)).fold(function(){lr(t).each(function(t){vr(t,n)})},function(t){gr(t,n)})},hr=function(n,e){mr(n,0).fold(function(){vr(n,e)},function(t){n.dom().insertBefore(e.dom(),t.dom())})},vr=function(t,n){t.dom().appendChild(n.dom())},br=function(n,t){it(t,function(t){vr(n,t)})},yr=function(t){t.dom().textContent="",it(dr(t),function(t){xr(t)})},xr=function(t){var n=t.dom();null!==n.parentNode&&n.parentNode.removeChild(n)},wr=function(t){var n,e=dr(t);0<e.length&&(n=t,it(e,function(t){gr(n,t)})),xr(t)},Sr=function(t){return t.dom().innerHTML},kr=function(t,n){var e,o,r=ar(t).dom(),i=le.fromDom(r.createDocumentFragment()),u=(e=n,(o=(r||nt.document).createElement("div")).innerHTML=e,dr(le.fromDom(o)));br(i,u),yr(t),vr(t,i)},Cr=("undefined"!=typeof nt.window?nt.window:Function("return this;")(),function(t){return t.dom().nodeName.toLowerCase()}),Or=function(n){return function(t){return t.dom().nodeType===n}},_r=Or(1),Tr=Or(3),Er=function(t,n,e){if(!(S(e)||O(e)||rt(e)))throw nt.console.error("Invalid call to Attr.set. Key ",n,":: Value ",e,":: Element ",t),new Error("Attribute value was not simple");t.setAttribute(n,e+"")},Br=function(t,n,e){Er(t.dom(),n,e)},Dr=function(t,n){var e=t.dom().getAttribute(n);return null===e?undefined:e},Mr=function(t,n){return st.from(Dr(t,n))},Ar=function(t,n){var e=t.dom();return!(!e||!e.hasAttribute)&&e.hasAttribute(n)},Fr=function(t,n){t.dom().removeAttribute(n)},Ir=function(t){return n=t,e=!1,le.fromDom(n.dom().cloneNode(e));var n,e},Rr=function(t){var n,e,o,r=Ir(t);return n=r,e=le.fromTag("div"),o=le.fromDom(n.dom().cloneNode(!0)),vr(e,o),Sr(e)},Vr=function(t){return Rr(t)},Hr=Yo([{key:ho(),value:qe({can:function(t,n){var e,o,r=n.event().originator(),i=n.event().target();return o=i,!(Le(e=r,t.element())&&!Le(e,o))||(nt.console.warn(ho()+" did not get interpreted by the desired target. \nOriginator: "+Vr(r)+"\nTarget: "+Vr(i)+"\nCheck the "+ho()+" event handlers"),!1)}})}]),Pr=/* */Object.freeze({__proto__:null,events:Hr}),zr=0,Nr=function(t){var n=(new Date).getTime();return t+"_"+Math.floor(1e9*Math.random())+ ++zr+String(n)},Lr=at("alloy-id-"),jr=at("data-alloy-id"),Ur=Lr(),Wr=jr(),Gr=function(t,n){Object.defineProperty(t.dom(),Wr,{value:n,writable:!0})},Xr=function(t){var n=_r(t)?t.dom()[Wr]:null;return st.from(n)},Yr=function(t){return Nr(t)},qr=ct,Kr=function(n){var t=function(t){return function(){throw new Error("The component must be in a context to send: "+t+(n?"\n"+Vr(n().element())+" is not in context.":""))}};return{debugInfo:at("fake"),triggerEvent:t("triggerEvent"),triggerFocus:t("triggerFocus"),triggerEscape:t("triggerEscape"),build:t("build"),addToWorld:t("addToWorld"),removeFromWorld:t("removeFromWorld"),addToGui:t("addToGui"),removeFromGui:t("removeFromGui"),getByUid:t("getByUid"),getByDom:t("getByDom"),broadcast:t("broadcast"),broadcastOn:t("broadcastOn"),broadcastEvent:t("broadcastEvent"),isConnected:at(!1)}},Jr=Kr(),$r=function(t){return V(t,function(t){return o=n="/*",r=(e=t).length-n.length,""===o||e.length>=o.length&&e.substr(r,r+o.length)===o?t.substring(0,t.length-"/*".length):t;var n,e,o,r})},Qr=function(t,n){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return t.toFunctionAnnotation=function(){return{name:n,parameters:$r(i)}},t},Zr=Nr("alloy-premade"),ti=function(t){return Kt(Zr,t)},ni=function(o){return t=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];return o.apply(void 0,b([t.getApis(),t],n))},n=o.toString(),e=n.indexOf(")")+1,r=n.indexOf("("),i=n.substring(r+1,e-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:$r(i.slice(1))}},t;var t,n,e,r,i},ei={init:function(){return oi({readState:function(){return"No State required"}})}},oi=function(t){return t},ri=function(t,r){var i={};return ft(t,function(t,o){ft(t,function(t,n){var e=bt(i,n).getOr([]);i[n]=e.concat([r(o,t)])})}),i},ii=function(t){return{classes:t.classes!==undefined?t.classes:[],attributes:t.attributes!==undefined?t.attributes:{},styles:t.styles!==undefined?t.styles:{}}},ui=function(t,n){return e=g.apply(undefined,[t.handler].concat(n)),o=t.purpose(),{cHandler:e,purpose:at(o)};var e,o},ai=function(t){return t.cHandler},ci=function(t,n){return{name:at(t),handler:at(n)}},si=function(t,n,e){var o,r,i=et(et({},e),(o=t,r={},it(n,function(t){r[t.name()]=t.handlers(o)}),r));return ri(i,ci)},li=function(t){var n,i=T(n=t)?{can:at(!0),abort:at(!1),run:n}:n;return function(t,n){for(var e=[],o=2;o<arguments.length;o++)e[o-2]=arguments[o];var r=[t,n].concat(e);i.abort.apply(undefined,r)?n.stop():i.can.apply(undefined,r)&&i.run.apply(undefined,r)}},fi=function(t,n,e){var o,r,i=n[e];return i?function(u,a,t,c){try{var n=q(t,function(t,n){var e=t[a](),o=n[a](),r=c.indexOf(e),i=c.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+e+".\nOrder specified: "+JSON.stringify(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(c,null,2));return r<i?-1:i<r?1:0});return ot.value(n)}catch(e){return ot.error([e])}}("Event: "+e,"name",t,i).map(function(t){var n=V(t,function(t){return t.handler()});return Ke(n)}):(o=e,r=t,ot.error(["The event ("+o+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(V(r,function(t){return t.name()}),null,2)]))},di=function(t,i){var n=pt(t,function(o,r){return(1===o.length?ot.value(o[0].handler()):fi(o,i,r)).map(function(t){var n=li(t),e=1<o.length?P(i[r],function(n){return I(o,function(t){return t.name()===n})}).join(" > "):o[0].name();return Kt(r,{handler:n,purpose:at(e)})})});return $t(n,{})},mi=function(t){return Cn("custom.definition",ln([hn("dom","dom",Ut(),ln([zn("tag"),Zn("styles",{}),Zn("classes",[]),Zn("attributes",{}),Yn("value"),Yn("innerHtml")])),zn("components"),zn("uid"),Zn("events",{}),Zn("apis",{}),hn("eventOrder","eventOrder",Lt.mergeWithThunk(at({"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],touchstart:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"],"alloy.receive":["receiving","reflecting","tooltipping"]})),Dn()),Yn("domModification")]),t)},gi=function(t,n){var e=Dr(t,n);return e===undefined||""===e?[]:e.split(" ")},pi=function(t){return t.dom().classList!==undefined},hi=function(t,n){return r=n,i=gi(e=t,o="class").concat([r]),Br(e,o,i.join(" ")),!0;var e,o,r,i},vi=function(t,n){return r=n,0<(i=P(gi(e=t,o="class"),function(t){return t!==r})).length?Br(e,o,i.join(" ")):Fr(e,o),!1;var e,o,r,i},bi=function(t,n){pi(t)?t.dom().classList.add(n):hi(t,n)},yi=function(t){0===(pi(t)?t.dom().classList:gi(t,"class")).length&&Fr(t,"class")},xi=function(t,n){pi(t)?t.dom().classList.remove(n):vi(t,n);yi(t)},wi=function(t,n){return pi(t)&&t.dom().classList.contains(n)},Si=function(n,t){it(t,function(t){bi(n,t)})},ki=function(n,t){it(t,function(t){xi(n,t)})},Ci=function(t){return t.style!==undefined&&T(t.style.getPropertyValue)},Oi=function(t){var n=Tr(t)?t.dom().parentNode:t.dom();return n!==undefined&&null!==n&&n.ownerDocument.body.contains(n)},_i=function(){return Ti(le.fromDom(nt.document))},Ti=function(t){var n=t.dom().body;if(null===n||n===undefined)throw new Error("Body is not available yet");return le.fromDom(n)},Ei=function(t,n,e){if(!S(e))throw nt.console.error("Invalid call to CSS.set. Property ",n,":: Value ",e,":: Element ",t),new Error("CSS value must be a string: "+e);Ci(t)&&t.style.setProperty(n,e)},Bi=function(t,n){Ci(t)&&t.style.removeProperty(n)},Di=function(t,n,e){var o=t.dom();Ei(o,n,e)},Mi=function(t,n){var e=t.dom();ft(n,function(t,n){Ei(e,n,t)})},Ai=function(t,n){var e=t.dom();ft(n,function(t,n){t.fold(function(){Bi(e,n)},function(t){Ei(e,n,t)})})},Fi=function(t,n){var e=t.dom(),o=nt.window.getComputedStyle(e).getPropertyValue(n);return""!==o||Oi(t)?o:Ii(e,n)},Ii=function(t,n){return Ci(t)?t.style.getPropertyValue(n):""},Ri=function(t,n){var e=t.dom(),o=Ii(e,n);return st.from(o).filter(function(t){return 0<t.length})},Vi=function(t,n,e){var o=le.fromTag(t);return Di(o,n,e),Ri(o,n).isSome()},Hi=function(t,n){var e=t.dom();Bi(e,n),Mr(t,"style").map(Be).is("")&&Fr(t,"style")},Pi=function(t){return t.dom().offsetWidth},zi=function(t){return t.dom().value},Ni=function(t,n){if(n===undefined)throw new Error("Value.set was undefined");t.dom().value=n},Li=function(t){var n,e,o,r=le.fromTag(t.tag);n=r,e=t.attributes,o=n.dom(),ft(e,function(t,n){Er(o,n,t)}),Si(r,t.classes),Mi(r,t.styles),t.innerHtml.each(function(t){return kr(r,t)});var i=t.domChildren;return br(r,i),t.value.each(function(t){Ni(r,t)}),t.uid,Gr(r,t.uid),r},ji=function(t,n){return e=t,r=V(o=n,function(t){return Qn(t.name(),[zn("config"),Zn("state",ei)])}),i=Cn("component.behaviours",ln(r),e.behaviours).fold(function(t){throw new Error(Tn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))},function(t){return t}),{list:o,data:dt(i,function(t){var n=t.map(function(t){return{config:t.config,state:t.state.init(t.config)}});return function(){return n}})};var e,o,r,i},Ui=function(t){var n,e,o=(n=bt(t,"behaviours").getOr({}),e=P(lt(n),function(t){return n[t]!==undefined}),V(e,function(t){return n[t].me}));return ji(t,o)},Wi=function(t,n,e){var o,r,i,u=et(et({},(o=t).dom),{uid:o.uid,domChildren:V(o.components,function(t){return t.element()})}),a=t.domModification.fold(function(){return ii({})},ii),c={"alloy.base.modification":a},s=0<n.length?function(n,t,e,o){var r=et({},t);it(e,function(t){r[t.name()]=t.exhibit(n,o)});var i=ri(r,function(t,n){return{name:t,modification:n}}),u=function(t){return z(t,function(t,n){return et(et({},n.modification),t)},{})},a=z(i.classes,function(t,n){return n.modification.concat(t)},[]),c=u(i.attributes),s=u(i.styles);return ii({classes:a,attributes:c,styles:s})}(e,c,n,u):a;return i=s,et(et({},r=u),{attributes:et(et({},r.attributes),i.attributes),styles:et(et({},r.styles),i.styles),classes:r.classes.concat(i.classes)})},Gi=function(t,n,e){var o,r,i,u={"alloy.base.behaviour":t.events};return o=e,r=t.eventOrder,i=si(o,n,u),di(i,r).getOrDie()},Xi=function(t){var n,e,o,r,i,u,a,c,s,l,f,d,m,g=qr(t),p=g.events,h=y(g,["events"]),v=(n=bt(h,"components").getOr([]),V(n,Ji)),b=et(et({},h),{events:et(et({},Pr),p),components:v});return ot.value((o=function(){return m},r=ce(Jr),i=On(mi(e=b)),u=Ui(e),a=u.list,c=u.data,s=Wi(i,a,c),l=Li(s),f=Gi(i,a,c),d=ce(i.components),m={getSystem:r.get,config:function(t){var n=c;return(T(n[t.name()])?n[t.name()]:function(){throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:function(t){return T(c[t.name()])},spec:at(e),readState:function(t){return c[t]().map(function(t){return t.state.readState()}).getOr("not enabled")},getApis:function(){return i.apis},connect:function(t){r.set(t)},disconnect:function(){r.set(Kr(o))},element:at(l),syncComponents:function(){var t=dr(l),n=U(t,function(t){return r.get().getByDom(t).fold(function(){return[]},function(t){return[t]})});d.set(n)},components:d.get,events:at(f)}))},Yi=function(t){var n=le.fromText(t);return qi({element:n})},qi=function(t){var n=_n("external.component",sn([zn("element"),Yn("uid")]),t),e=ce(Kr());n.uid.each(function(t){Gr(n.element,t)});var o={getSystem:e.get,config:st.none,hasConfigured:at(!1),connect:function(t){e.set(t)},disconnect:function(){e.set(Kr(function(){return o}))},getApis:function(){return{}},element:at(n.element),spec:at(t),readState:at("No state"),syncComponents:Z,components:at([]),events:at({})};return ti(o)},Ki=Yr,Ji=function(n){return bt(n,Zr).fold(function(){var t=n.hasOwnProperty("uid")?n:et({uid:Ki("")},n);return Xi(t).getOrDie()},function(t){return t})},$i=ti;function Qi(o,r){var t=function(t){var n=r(t);if(n<=0||null===n){var e=Fi(t,o);return parseFloat(e)||0}return n},i=function(r,t){return N(t,function(t,n){var e=Fi(r,n),o=e===undefined?0:parseInt(e,10);return isNaN(o)?t:t+o},0)};return{set:function(t,n){if(!rt(n)&&!n.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+n);var e=t.dom();Ci(e)&&(e.style[o]=n+"px")},get:t,getOuter:t,aggregate:i,max:function(t,n,e){var o=i(t,e);return o<n?n-o:0}}}var Zi=Qi("height",function(t){var n=t.dom();return Oi(t)?n.getBoundingClientRect().height:n.offsetHeight}),tu=function(t){return Zi.get(t)},nu=function(t){return Zi.getOuter(t)},eu=function(e,o){return{left:at(e),top:at(o),translate:function(t,n){return eu(e+t,o+n)}}},ou=eu,ru=function(t,n){return t!==undefined?t:n!==undefined?n:0},iu=function(t){var n=t.dom().ownerDocument,e=n.body,o=n.defaultView,r=n.documentElement;if(e===t.dom())return ou(e.offsetLeft,e.offsetTop);var i=ru(o.pageYOffset,r.scrollTop),u=ru(o.pageXOffset,r.scrollLeft),a=ru(r.clientTop,e.clientTop),c=ru(r.clientLeft,e.clientLeft);return uu(t).translate(u-c,i-a)},uu=function(t){var n,e=t.dom(),o=e.ownerDocument.body;return o===e?ou(o.offsetLeft,o.offsetTop):Oi(t)?(n=e.getBoundingClientRect(),ou(n.left,n.top)):ou(0,0)},au=Qi("width",function(t){return t.dom().offsetWidth}),cu=function(t){return au.get(t)},su=function(t){return au.getOuter(t)},lu=function(t){var n,e,o,r,i,u,a,c=le.fromDom(t.target),s=function(){return t.stopPropagation()},l=function(){return t.preventDefault()},f=d(l,s);return n=c,e=t.clientX,o=t.clientY,r=s,i=l,u=f,a=t,{target:at(n),x:at(e),y:at(o),stop:r,prevent:i,kill:u,raw:at(a)}},fu=function(t,n,e,o,r){var i,u,a=(i=e,u=o,function(t){i(t)&&u(lu(t))});return t.dom().addEventListener(n,a,r),{unbind:g(du,t,n,a,r)}},du=function(t,n,e,o){t.dom().removeEventListener(n,e,o)},mu=function(t){var n=t!==undefined?t.dom():nt.document,e=n.body.scrollLeft||n.documentElement.scrollLeft,o=n.body.scrollTop||n.documentElement.scrollTop;return ou(e,o)},gu=function(t,n,e){(e!==undefined?e.dom():nt.document).defaultView.scrollTo(t,n)},pu=function(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}},hu=function(t){var n,e,o=t===undefined?nt.window:t,r=o.document,i=mu(le.fromDom(r));return e=(n=o)===undefined?nt.window:n,st.from(e.visualViewport).fold(function(){var t=o.document.documentElement,n=t.clientWidth,e=t.clientHeight;return pu(i.left(),i.top(),n,e)},function(t){return pu(Math.max(t.pageLeft,i.left()),Math.max(t.pageTop,i.top()),t.width,t.height)})},vu=function(o,t){return o.view(t).fold(at([]),function(t){var n=o.owner(t),e=vu(o,n);return[t].concat(e)})},bu=/* */Object.freeze({__proto__:null,view:function(t){return(t.dom()===nt.document?st.none():st.from(t.dom().defaultView.frameElement)).map(le.fromDom)},owner:function(t){return ar(t)}}),yu=function(o){var t,n,e,r,i=le.fromDom(nt.document),u=mu(i);return(t=o,e=(n=bu).owner(t),r=vu(n,e),st.some(r)).fold(g(iu,o),function(t){var n=uu(o),e=z(t,function(t,n){var e=uu(n);return{left:t.left+e.left(),top:t.top+e.top()}},{left:0,top:0});return ou(e.left+n.left()+u.left(),e.top+n.top()+u.top())})},xu=function(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}},wu=function(t){var n=iu(t),e=su(t),o=nu(t);return xu(n.left(),n.top(),e,o)},Su=function(t){var n=yu(t),e=su(t),o=nu(t);return xu(n.left(),n.top(),e,o)},ku=function(){return hu(nt.window)};function Cu(t,n,e,o,r){return t(e,o)?st.some(e):T(r)&&r(e)?st.none():n(e,o,r)}var Ou,_u,Tu=function(t,n,e){for(var o=t.dom(),r=T(e)?e:at(!1);o.parentNode;){o=o.parentNode;var i=le.fromDom(o);if(n(i))return st.some(i);if(r(i))break}return st.none()},Eu=function(t,n,e){return Cu(function(t,n){return n(t)},Tu,t,n,e)},Bu=function(t,n,e){return Eu(t,n,e).isSome()},Du=function(t,n,e){return Tu(t,function(t){return ze(t,n)},e)},Mu=function(t,n){return e=n,r=(o=t)===undefined?nt.document:o.dom(),Ne(r)?st.none():st.from(r.querySelector(e)).map(le.fromDom);var e,o,r},Au=function(t,n,e){return Cu(function(t,n){return ze(t,n)},Du,t,n,e)},Fu=function(){var n=Nr("aria-owns");return{id:n,link:function(t){Br(t,"aria-owns",n)},unlink:function(t){Fr(t,"aria-owns")}}},Iu=function(n,t){return Eu(t,function(t){if(!_r(t))return!1;var n=Dr(t,"id");return n!==undefined&&-1<n.indexOf("aria-owns")}).bind(function(t){var n=Dr(t,"id"),e=ar(t);return Mu(e,'[aria-owns="'+n+'"]')}).exists(function(t){return Ru(n,t)})},Ru=function(n,t){return Bu(t,function(t){return Le(t,n.element())},at(!1))||Iu(n,t)},Vu="unknown";(_u=Ou=Ou||{})[_u.STOP=0]="STOP",_u[_u.NORMAL=1]="NORMAL",_u[_u.LOGGING=2]="LOGGING";var Hu,Pu,zu=ce({}),Nu=function(n,t,e){var o,r,i,u;switch(bt(zu.get(),n).orThunk(function(){var t=lt(zu.get());return Q(t,function(t){return-1<n.indexOf(t)?st.some(zu.get()[t]):st.none()})}).getOr(Ou.NORMAL)){case Ou.NORMAL:return e(Uu());case Ou.LOGGING:var a=(o=n,r=t,i=[],u=(new Date).getTime(),{logEventCut:function(t,n,e){i.push({outcome:"cut",target:n,purpose:e})},logEventStopped:function(t,n,e){i.push({outcome:"stopped",target:n,purpose:e})},logNoParent:function(t,n,e){i.push({outcome:"no-parent",target:n,purpose:e})},logEventNoHandlers:function(t,n){i.push({outcome:"no-handlers-left",target:n})},logEventResponse:function(t,n,e){i.push({outcome:"response",purpose:e,target:n})},write:function(){var t=(new Date).getTime();F(["mousemove","mouseover","mouseout",_o()],o)||nt.console.log(o,{event:o,time:t-u,target:r.dom(),sequence:V(i,function(t){return F(["cut","stopped","response"],t.outcome)?"{"+t.purpose+"} "+t.outcome+" at ("+Vr(t.target)+")":t.outcome})})}}),c=e(a);return a.write(),c;case Ou.STOP:return!0}},Lu=["alloy/data/Fields","alloy/debugging/Debugging"],ju=function(t,n,e){return Nu(t,n,e)},Uu=at({logEventCut:Z,logEventStopped:Z,logNoParent:Z,logEventNoHandlers:Z,logEventResponse:Z,write:Z}),Wu=at([zn("menu"),zn("selectedMenu")]),Gu=at([zn("item"),zn("selectedItem")]),Xu=(at(ln(Gu().concat(Wu()))),at(ln(Gu()))),Yu=Wn("initSize",[zn("numColumns"),zn("numRows")]),qu=function(){return Wn("markers",[zn("backgroundMenu")].concat(Wu()).concat(Gu()))},Ku=function(t){return Wn("markers",V(t,zn))},Ju=function(t,n,e){!function(){var t=new Error;if(t.stack===undefined)return;var n=t.stack.split("\n");L(n,function(n){return 0<n.indexOf("alloy")&&!I(Lu,function(t){return-1<n.indexOf(t)})}).getOr(Vu)}();return hn(n,n,e,Sn(function(e){return ot.value(function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.apply(undefined,t)})}))},$u=function(t){return Ju(0,t,jt(Z))},Qu=function(t){return Ju(0,t,jt(st.none))},Zu=function(t){return Ju(0,t,Ut())},ta=function(t){return Ju(0,t,Ut())},na=function(t,n){return ae(t,at(n))},ea=function(t){return ae(t,ct)},oa=at(Yu),ra=function(t,n,e,o,r,i){return{x:at(t),y:at(n),bubble:at(e),direction:at(o),boundsRestriction:at(r),label:at(i)}},ia=wt([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),ua=ia.southeast,aa=ia.southwest,ca=ia.northeast,sa=ia.northwest,la=ia.south,fa=ia.north,da=ia.east,ma=ia.west,ga=function(n,e){return function(t,n){for(var e={},o=0,r=t.length;o<r;o++){var i=t[o];e[String(i)]=n(i,o)}return e}(["left","right","top","bottom"],function(t){return bt(e,t).map(function(t){return function(t,n){switch(n){case 1:return t.x;case 0:return t.x+t.width;case 2:return t.y;case 3:return t.y+t.height}}(n,t)})})},pa=function(t){return t.x},ha=function(t,n){return t.x+t.width/2-n.width/2},va=function(t,n){return t.x+t.width-n.width},ba=function(t,n){return t.y-n.height},ya=function(t){return t.y+t.height},xa=function(t,n){return t.y+t.height/2-n.height/2},wa=function(t,n,e){return ra(pa(t),ya(t),e.southeast(),ua(),ga(t,{left:1,top:3}),"layout-se")},Sa=function(t,n,e){return ra(va(t,n),ya(t),e.southwest(),aa(),ga(t,{right:0,top:3}),"layout-sw")},ka=function(t,n,e){return ra(pa(t),ba(t,n),e.northeast(),ca(),ga(t,{left:1,bottom:2}),"layout-ne")},Ca=function(t,n,e){return ra(va(t,n),ba(t,n),e.northwest(),sa(),ga(t,{right:0,bottom:2}),"layout-nw")},Oa=function(t,n,e){return ra(ha(t,n),ba(t,n),e.north(),fa(),ga(t,{bottom:2}),"layout-n")},_a=function(t,n,e){return ra(ha(t,n),ya(t),e.south(),la(),ga(t,{top:3}),"layout-s")},Ta=function(t,n,e){return ra((o=t).x+o.width,xa(t,n),e.east(),da(),ga(t,{left:0}),"layout-e");var o},Ea=function(t,n,e){return ra((o=n,t.x-o.width),xa(t,n),e.west(),ma(),ga(t,{right:1}),"layout-w");var o},Ba=function(){return[wa,Sa,ka,Ca,_a,Oa,Ta,Ea]},Da=function(){return[Sa,wa,Ca,ka,_a,Oa,Ta,Ea]},Ma=function(){return[ka,Ca,wa,Sa,Oa,_a]},Aa=function(){return[wa,Sa,ka,Ca,_a,Oa]},Fa=function(){return[Sa,wa,Ca,ka,_a,Oa]},Ia=function(e,o,r){return ir(function(t,n){r(t,e,o)})},Ra=function(t,n,e,o,r,i){var u=sn(t),a=Qn(n,[qn("config",sn(t))]);return Pa(u,a,n,e,o,r,i)},Va=function(r,i,u){var t,n,e,o,a,c;return t=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=[e].concat(t);return e.config({name:at(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(t){var n=Array.prototype.slice.call(o,1);return i.apply(undefined,[e,t.config,t.state].concat(n))})},n=u,e=i.toString(),o=e.indexOf(")")+1,a=e.indexOf("("),c=e.substring(a+1,o-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:n,parameters:$r(c.slice(0,1).concat(c.slice(3)))}},t},Ha=function(t){return{key:t,value:undefined}},Pa=function(e,t,o,r,n,i,u){var a=function(t){return xt(t,o)?t[o]():st.none()},c=dt(n,function(t,n){return Va(o,t,n)}),s=dt(i,function(t,n){return Qr(t,n)}),l=et(et(et({},s),c),{revoke:g(Ha,o),config:function(t){var n=_n(o+"-config",e,t);return{key:o,value:{config:n,me:l,configAsRaw:_t(function(){return _n(o+"-config",e,t)}),initialConfig:t,state:u}}},schema:function(){return t},exhibit:function(t,e){return a(t).bind(function(n){return bt(r,"exhibit").map(function(t){return t(e,n.config,n.state)})}).getOr(ii({}))},name:function(){return o},handlers:function(t){return a(t).map(function(t){return bt(r,"events").getOr(function(){return{}})(t.config,t.state)}).getOr({})}});return l},za=function(t){return Jt(t)},Na=sn([zn("fields"),zn("name"),Zn("active",{}),Zn("apis",{}),Zn("state",ei),Zn("extra",{})]),La=function(t){var n=_n("Creating behaviour: "+t.name,Na,t);return Ra(n.fields,n.name,n.active,n.apis,n.extra,n.state)},ja=sn([zn("branchKey"),zn("branches"),zn("name"),Zn("active",{}),Zn("apis",{}),Zn("state",ei),Zn("extra",{})]),Ua=function(t){var n,e,o,r,i,u,a,c,s=_n("Creating behaviour: "+t.name,ja,t);return n=Bn(s.branchKey,s.branches),e=s.name,o=s.active,r=s.apis,i=s.extra,u=s.state,c=Qn(e,[qn("config",a=n)]),Pa(a,c,e,o,r,i,u)},Wa=at(undefined),Ga=/* */Object.freeze({__proto__:null,events:function(c){return Yo([Jo(yo(),function(r,t){var n,e,i=c.channels,o=lt(i),u=t,a=(n=o,(e=u).universal()?n:P(n,function(t){return F(e.channels(),t)}));it(a,function(t){var n=i[t],e=n.schema,o=_n("channel["+t+"] data\nReceiver: "+Vr(r.element()),e,u.data());n.onReceive(r,o)})})])}}),Xa=[Nn("channels",kn(ot.value,sn([Zu("onReceive"),Zn("schema",Dn())])))],Ya=La({fields:Xa,name:"receiving",active:Ga}),qa=/* */Object.freeze({__proto__:null,exhibit:function(t,n){return ii({classes:[],styles:n.useFixed()?{}:{position:"relative"}})}}),Ka=function(t){return t.dom().focus()},Ja=function(t){var n=t!==undefined?t.dom():nt.document;return st.from(n.activeElement).map(le.fromDom)},$a=function(n){return Ja(ar(n)).filter(function(t){return n.dom().contains(t.dom())})},Qa=function(t,e){var o=ar(e),n=Ja(o).bind(function(n){var r,i,t=function(t){return Le(n,t)};return t(e)?st.some(e):(r=t,(i=function(t){for(var n=0;n<t.childNodes.length;n++){var e=le.fromDom(t.childNodes[n]);if(r(e))return st.some(e);var o=i(t.childNodes[n]);if(o.isSome())return o}return st.none()})(e.dom()))}),r=t(e);return n.each(function(n){Ja(o).filter(function(t){return Le(t,n)}).fold(function(){Ka(n)},Z)}),r},Za=function(t,n,e,o,r){return{position:at(t),left:at(n),top:at(e),right:at(o),bottom:at(r)}},tc=function(t,n){var e=function(t){return t+"px"};Ai(t,{position:st.some(n.position()),left:n.left().map(e),top:n.top().map(e),right:n.right().map(e),bottom:n.bottom().map(e)})},nc=wt([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),ec=function(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=n.x-e,h=n.y-o,v=r-(p+n.width),b=i-(h+n.height),y=st.some(p),x=st.some(h),w=st.some(v),S=st.some(b),k=st.none();return u=n.direction,a=function(){return Za(t,y,x,k,k)},c=function(){return Za(t,k,x,w,k)},s=function(){return Za(t,y,k,k,S)},l=function(){return Za(t,k,k,w,S)},f=function(){return Za(t,y,x,k,k)},d=function(){return Za(t,y,k,k,S)},m=function(){return Za(t,y,x,k,k)},g=function(){return Za(t,k,x,w,k)},u.fold(a,c,s,l,f,d,m,g)},oc=function(t,n){var e=g(yu,n),o=t.fold(e,e,function(){var t=mu();return yu(n).translate(-t.left(),-t.top())}),r=su(n),i=nu(n);return xu(o.left(),o.top(),r,i)},rc=function(t,n,e){var o=ou(n,e);return t.fold(at(o),at(o),function(){var t=mu();return o.translate(-t.left(),-t.top())})},ic=(nc.none,nc.relative),uc=nc.fixed,ac=function(t,n){return e=n,{anchorBox:at(t),origin:at(e)};var e},cc=function(t,n,e,o){var r=t+n;return o<r?e:r<e?o:r},sc=function(t,n,e){return Math.min(Math.max(t,n),e)},lc=wt([{fit:["reposition"]},{nofit:["reposition","deltaW","deltaH"]}]),fc=function(t,n,e,o){var r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k,C,O,_,T,E,B,D,M,A,F,I,R,V,H=t.x(),P=t.y(),z=t.bubble().offset(),N=z.left(),L=z.top(),j=(r=o,i=t.boundsRestriction(),u=z,c=(a=function(n,e){var o="top"===n||"bottom"===n?u.top():u.left();return bt(i,n).bind(ct).bind(function(t){return"left"===n||"top"===n?e<=t?st.some(t):st.none():t<=e?st.some(t):st.none()}).map(function(t){return t+o}).getOr(e)})("left",r.x),s=a("top",r.y),l=a("right",r.right),f=a("bottom",r.bottom),xu(c,s,l-c,f-s)),U=j.y,W=j.bottom,G=j.x,X=j.right,Y=P+L,q=(d=H+N,m=Y,g=n,p=e,v=(h=j).x,b=h.y,y=h.width,x=h.height,S=b<=m,k=(w=v<=d)&&S,C=d+g<=v+y&&m+p<=b+x,O=Math.abs(Math.min(g,w?v+y-d:v-(d+g))),_=Math.abs(Math.min(p,S?b+x-m:b-(m+p))),T=Math.max(h.x,h.right-g),E=Math.max(h.y,h.bottom-p),{originInBounds:k,sizeInBounds:C,limitX:sc(d,h.x,T),limitY:sc(m,h.y,E),deltaW:O,deltaH:_}),K=q.originInBounds,J=q.sizeInBounds,$=q.limitX,Q=q.limitY,Z=q.deltaW,tt=q.deltaH,nt=at(Q+tt-U),et=at(W-Q),ot=(B=t.direction(),M=D=et,A=nt,B.fold(D,D,A,A,D,A,M,M)),rt=at($+Z-G),it=at(X-$),ut={x:$,y:Q,width:Z,height:tt,maxHeight:ot,maxWidth:(F=t.direction(),R=I=it,V=rt,F.fold(I,V,I,V,R,R,I,V)),direction:t.direction(),classes:{on:t.bubble().classesOn(),off:t.bubble().classesOff()},label:t.label(),candidateYforTest:Y};return K&&J?lc.fit(ut):lc.nofit(ut,Z,tt)},dc=function(t,n,e,o){Hi(n,"max-height"),Hi(n,"max-width");var r,i,u,a,c,s,l,f,d,m={width:su(r=n),height:nu(r)};return i=o.preference,u=t,a=m,c=e,s=o.bounds,l=a.width,f=a.height,d=function(t,o,r,i){var n=t(u,a,c);return fc(n,l,f,s).fold(lc.fit,function(t,n,e){return i<e||r<n?lc.nofit(t,n,e):lc.nofit(o,r,i)})},N(i,function(t,n){var e=g(d,n);return t.fold(lc.fit,e)},lc.nofit({x:u.x,y:u.y,width:a.width,height:a.height,maxHeight:a.height,maxWidth:a.width,direction:ua(),classes:{on:[],off:[]},label:"none",candidateYforTest:u.y},-1,-1)).fold(ct,ct)},mc=function(t,n,e){var o,r;tc(t,(o=e.origin,r=n,o.fold(function(){return Za("absolute",st.some(r.x),st.some(r.y),st.none(),st.none())},function(t,n,e,o){return ec("absolute",r,t,n,e,o)},function(t,n,e,o){return ec("fixed",r,t,n,e,o)})))},gc=function(t,n){var e,o,r;e=t,o=Math.floor(n),r=Zi.max(e,o,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]),Di(e,"max-height",r+"px")},pc=at(function(t,n){gc(t,n),Mi(t,{"overflow-x":"hidden","overflow-y":"auto"})}),hc=at(function(t,n){gc(t,n)}),vc=function(t,n,e){return t[n]===undefined?e:t[n]},bc=function(t,n,e,o,r,i){var u,a=vc(i,"maxHeightFunction",pc()),c=vc(i,"maxWidthFunction",Z),s=t.anchorBox(),l=t.origin(),f={bounds:(u=l,r.fold(function(){return u.fold(ku,ku,xu)},function(e){return u.fold(e,e,function(){var t=e(),n=rc(u,t.x,t.y);return xu(n.left(),n.top(),t.width,t.height)})})),origin:l,preference:o,maxHeightFunction:a,maxWidthFunction:c};yc(s,n,e,f)},yc=function(t,n,e,o){var r,i,u,a,c,s,l=dc(t,n,e,o);mc(n,l,o),r=n,i=l.classes,ki(r,i.off),Si(r,i.on),u=n,a=l,(0,o.maxHeightFunction)(u,a.maxHeight),c=n,s=l,(0,o.maxWidthFunction)(c,s.maxWidth)},xc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right"],wc=function(t,n,e){var r=function(t){return bt(e,t).getOr([])},o=function(t,n,e){var o=X(xc,e);return{offset:function(){return ou(t,n)},classesOn:function(){return U(e,r)},classesOff:function(){return U(o,r)}}};return{southeast:function(){return o(-t,n,["top","alignLeft"])},southwest:function(){return o(t,n,["top","alignRight"])},south:function(){return o(-t/2,n,["top","alignCentre"])},northeast:function(){return o(-t,-n,["bottom","alignLeft"])},northwest:function(){return o(t,-n,["bottom","alignRight"])},north:function(){return o(-t/2,-n,["bottom","alignCentre"])},east:function(){return o(t,-n/2,["valignCentre","left"])},west:function(){return o(-t,-n/2,["valignCentre","right"])},innerNorthwest:function(){return o(-t,n,["top","alignRight"])},innerNortheast:function(){return o(t,n,["top","alignLeft"])},innerNorth:function(){return o(-t/2,n,["top","alignCentre"])},innerSouthwest:function(){return o(-t,-n,["bottom","alignRight"])},innerSoutheast:function(){return o(t,-n,["bottom","alignLeft"])},innerSouth:function(){return o(-t/2,-n,["bottom","alignCentre"])},innerWest:function(){return o(t,-n/2,["valignCentre","right"])},innerEast:function(){return o(-t,-n/2,["valignCentre","left"])}}},Sc=function(){return wc(0,0,{})},kc=function(n,e){return function(t){return"rtl"===Cc(t)?e:n}},Cc=function(t){return"rtl"===Fi(t,"direction")?"rtl":"ltr"};(Pu=Hu=Hu||{}).TopToBottom="toptobottom",Pu.BottomToTop="bottomtotop";var Oc="data-alloy-vertical-dir",_c=function(t){return Bu(t,function(t){return _r(t)&&Dr(t,Oc)===Hu.BottomToTop})},Tc=function(){return Qn("layouts",[zn("onLtr"),zn("onRtl"),Yn("onBottomLtr"),Yn("onBottomRtl")])},Ec=function(n,t,e,o,r,i,u){var a=u.map(_c).getOr(!1),c=t.layouts.map(function(t){return t.onLtr(n)}),s=t.layouts.map(function(t){return t.onRtl(n)}),l=a?t.layouts.bind(function(t){return t.onBottomLtr.map(function(t){return t(n)})}).or(c).getOr(r):c.getOr(e),f=a?t.layouts.bind(function(t){return t.onBottomRtl.map(function(t){return t(n)})}).or(s).getOr(i):s.getOr(o);return kc(l,f)(n)},Bc=[zn("hotspot"),Yn("bubble"),Zn("overrides",{}),Tc(),na("placement",function(t,n,e){var o=n.hotspot,r=oc(e,o.element()),i=Ec(t.element(),n,Aa(),Fa(),Ma(),[Ca,ka,Sa,wa,Oa,_a],st.some(n.hotspot.element()));return st.some({anchorBox:r,bubble:n.bubble.getOr(Sc()),overrides:n.overrides,layouts:i,placer:st.none()})})],Dc=[zn("x"),zn("y"),Zn("height",0),Zn("width",0),Zn("bubble",Sc()),Zn("overrides",{}),Tc(),na("placement",function(t,n,e){var o=rc(e,n.x,n.y),r=xu(o.left(),o.top(),n.width,n.height),i=Ec(t.element(),n,Ba(),Da(),Ba(),Da(),st.none());return st.some({anchorBox:r,bubble:n.bubble,overrides:n.overrides,layouts:i,placer:st.none()})})],Mc=function(t,n,e,o){return{start:at(t),soffset:at(n),finish:at(e),foffset:at(o)}},Ac=wt([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Fc=(Ac.before,Ac.on,Ac.after,function(t){return t.fold(ct,ct,ct)}),Ic=wt([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Rc={domRange:Ic.domRange,relative:Ic.relative,exact:Ic.exact,exactFromRange:function(t){return Ic.exact(t.start(),t.soffset(),t.finish(),t.foffset())},getWin:function(t){var n=t.match({domRange:function(t){return le.fromDom(t.startContainer)},relative:function(t,n){return Fc(t)},exact:function(t,n,e,o){return t}});return sr(n)},range:Mc},Vc=function(t,n,e){var o,r,i=t.document.createRange();return o=i,n.fold(function(t){o.setStartBefore(t.dom())},function(t,n){o.setStart(t.dom(),n)},function(t){o.setStartAfter(t.dom())}),r=i,e.fold(function(t){r.setEndBefore(t.dom())},function(t,n){r.setEnd(t.dom(),n)},function(t){r.setEndAfter(t.dom())}),i},Hc=function(t,n,e,o,r){var i=t.document.createRange();return i.setStart(n.dom(),e),i.setEnd(o.dom(),r),i},Pc=function(t){return{left:at(t.left),top:at(t.top),right:at(t.right),bottom:at(t.bottom),width:at(t.width),height:at(t.height)}},zc=wt([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Nc=function(t,n,e){return n(le.fromDom(e.startContainer),e.startOffset,le.fromDom(e.endContainer),e.endOffset)},Lc=function(t,n){var r,e,o,i=(r=t,n.match({domRange:function(t){return{ltr:at(t),rtl:st.none}},relative:function(t,n){return{ltr:_t(function(){return Vc(r,t,n)}),rtl:_t(function(){return st.some(Vc(r,n,t))})}},exact:function(t,n,e,o){return{ltr:_t(function(){return Hc(r,t,n,e,o)}),rtl:_t(function(){return st.some(Hc(r,e,o,t,n))})}}}));return(o=(e=i).ltr()).collapsed?e.rtl().filter(function(t){return!1===t.collapsed}).map(function(t){return zc.rtl(le.fromDom(t.endContainer),t.endOffset,le.fromDom(t.startContainer),t.startOffset)}).getOrThunk(function(){return Nc(0,zc.ltr,o)}):Nc(0,zc.ltr,o)};zc.ltr,zc.rtl;var jc=function PF(e,o){var n=function(t){return e(t)?st.from(t.dom().nodeValue):st.none()};return{get:function(t){if(!e(t))throw new Error("Can only get "+o+" value of a "+o+" node");return n(t).getOr("")},getOption:n,set:function(t,n){if(!e(t))throw new Error("Can only set raw "+o+" value of a "+o+" node");t.dom().nodeValue=n}}}(Tr,"text"),Uc=function(t){return jc.getOption(t)},Wc=["img","br"],Gc=function(t){return Uc(t).filter(function(t){return 0!==t.trim().length||-1<t.indexOf("\xa0")}).isSome()||F(Wc,Cr(t))},Xc=function(t,i){var u=function(t){for(var n=dr(t),e=n.length-1;0<=e;e--){var o=n[e];if(i(o))return st.some(o);var r=u(o);if(r.isSome())return r}return st.none()};return u(t)},Yc=function(t,n){return e=n,r=(o=t)===undefined?nt.document:o.dom(),Ne(r)?[]:V(r.querySelectorAll(e),le.fromDom);var e,o,r},qc=function(t,n,e,o){var r,i,u,a,c,s=(i=n,u=e,a=o,(c=ar(r=t).dom().createRange()).setStart(r.dom(),i),c.setEnd(u.dom(),a),c),l=Le(t,e)&&n===o;return s.collapsed&&!l},Kc=function(t){var n=le.fromDom(t.anchorNode),e=le.fromDom(t.focusNode);return qc(n,t.anchorOffset,e,t.focusOffset)?st.some(Mc(n,t.anchorOffset,e,t.focusOffset)):function(t){if(0<t.rangeCount){var n=t.getRangeAt(0),e=t.getRangeAt(t.rangeCount-1);return st.some(Mc(le.fromDom(n.startContainer),n.startOffset,le.fromDom(e.endContainer),e.endOffset))}return st.none()}(t)},Jc=function(t,n){var i,e,o,r,u=Lc(i=t,n).match({ltr:function(t,n,e,o){var r=i.document.createRange();return r.setStart(t.dom(),n),r.setEnd(e.dom(),o),r},rtl:function(t,n,e,o){var r=i.document.createRange();return r.setStart(e.dom(),o),r.setEnd(t.dom(),n),r}});return o=(e=u).getClientRects(),0<(r=0<o.length?o[0]:e.getBoundingClientRect()).width||0<r.height?st.some(r).map(Pc):st.none()},$c=function(t,n){return{element:t,offset:n}},Qc=function(t,n){var e=dr(t);if(0===e.length)return $c(t,n);if(n<e.length)return $c(e[n],0);var o,r=e[e.length-1],i=Tr(r)?(o=r,jc.get(o).length):dr(r).length;return $c(r,i)},Zc=wt([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),ts=function(t){return t.fold(ct,function(t,n,e){return t.translate(-n,-e)})},ns=function(t){return t.fold(ct,ct)},es=function(t){return N(t,function(t,n){return t.translate(n.left(),n.top())},ou(0,0))},os=function(t){var n=V(t,ns);return es(n)},rs=Zc.screen,is=Zc.absolute,us=function(t,n,e){var o,r,i=ar(t.element()),u=mu(i),a=(o=t,r=sr(e.root).dom(),st.from(r.frameElement).map(le.fromDom).filter(function(t){var n=ar(t),e=ar(o.element());return Le(n,e)}).map(iu).getOr(u));return is(a,u.left(),u.top())},as=function(t,n,e,o){var r=t,i=n,u=e,a=o;t<0&&(r=0,u=e+t),n<0&&(i=0,a=o+n);var c=rs(ou(r,i));return st.some({point:c,width:u,height:a})},cs=function(t,l,f,d,m){return t.map(function(t){var n,e,o,r=[l,t.point],i=(n=function(){return os(r)},e=function(){return os(r)},o=function(){return t=V(r,ts),es(t);var t},d.fold(n,e,o)),u={x:i.left(),y:i.top(),width:t.width,height:t.height},a=(f.showAbove?Ma:Aa)(),c=(f.showAbove,Fa()),s=Ec(m,f,a,c,a,c,st.none());return{anchorBox:u,bubble:f.bubble.getOr(Sc()),overrides:f.overrides,layouts:s,placer:st.none()}})},ss=function(t,n){return Tr(t)?{element:t,offset:n}:Qc(t,n)},ls=function(n,t){return t.getSelection.getOrThunk(function(){return function(){return t=n,st.from(t.getSelection()).filter(function(t){return 0<t.rangeCount}).bind(Kc);var t}})().map(function(t){var n=ss(t.start(),t.soffset()),e=ss(t.finish(),t.foffset());return Rc.range(n.element,n.offset,e.element,e.offset)})},fs=[Yn("getSelection"),zn("root"),Yn("bubble"),Tc(),Zn("overrides",{}),Zn("showAbove",!1),na("placement",function(t,n,e){var o=sr(n.root).dom(),r=us(t,0,n),i=ls(o,n).bind(function(t){return Jc(o,Rc.exactFromRange(t)).orThunk(function(){var n=le.fromText("\ufeff");return gr(t.start(),n),Jc(o,Rc.exact(n,0,n,1)).map(function(t){return xr(n),t})}).bind(function(t){return as(t.left(),t.top(),t.width(),t.height())})}),u=ls(o,n).bind(function(t){return _r(t.start())?st.some(t.start()):lr(t.start())}).getOr(t.element());return cs(i,r,n,e,u)})],ds=[zn("node"),zn("root"),Yn("bubble"),Tc(),Zn("overrides",{}),Zn("showAbove",!1),na("placement",function(r,i,u){var a=us(r,0,i);return i.node.bind(function(t){var n=t.dom().getBoundingClientRect(),e=as(n.left,n.top,n.width,n.height),o=i.node.getOr(r.element());return cs(e,a,i,u,o)})})],ms=function(t){return t.x+t.width},gs=function(t,n){return t.x-n.width},ps=function(t,n){return t.y-n.height+t.height},hs=function(t){return t.y},vs=function(t,n,e){return ra(ms(t),hs(t),e.southeast(),ua(),ga(t,{left:0,top:2}),"link-layout-se")},bs=function(t,n,e){return ra(gs(t,n),hs(t),e.southwest(),aa(),ga(t,{right:1,top:2}),"link-layout-sw")},ys=function(t,n,e){return ra(ms(t),ps(t,n),e.northeast(),ca(),ga(t,{left:0,bottom:3}),"link-layout-ne")},xs=function(t,n,e){return ra(gs(t,n),ps(t,n),e.northwest(),sa(),ga(t,{right:1,bottom:3}),"link-layout-nw")},ws=function(){return[vs,bs,ys,xs]},Ss=function(){return[bs,vs,xs,ys]},ks=[zn("item"),Tc(),Zn("overrides",{}),na("placement",function(t,n,e){var o=oc(e,n.item.element()),r=Ec(t.element(),n,ws(),Ss(),ws(),Ss(),st.none());return st.some({anchorBox:o,bubble:Sc(),overrides:n.overrides,layouts:r,placer:st.none()})})],Cs=Bn("anchor",{selection:fs,node:ds,hotspot:Bc,submenu:ks,makeshift:Dc}),Os=function(t,n,e,o,r){var i=ac(e.anchorBox,n);bc(i,r.element(),e.bubble,e.layouts,o,e.overrides)},_s=function(t,n,e,o,r,i){var u=i.map(wu);return Ts(t,n,e,o,r,u)},Ts=function(c,s,t,n,l,f){var d=_n("positioning anchor.info",Cs,n);Qa(function(){Di(l.element(),"position","fixed");var t=Ri(l.element(),"visibility");Di(l.element(),"visibility","hidden");var n,e,o,r,i=s.useFixed()?(r=nt.document.documentElement,uc(0,0,r.clientWidth,r.clientHeight)):(e=iu((n=c).element()),o=n.element().dom().getBoundingClientRect(),ic(e.left(),e.top(),o.width,o.height)),u=d.placement,a=f.map(at).or(s.getBounds);u(c,d,i).each(function(t){t.placer.getOr(Os)(c,i,t,a,l)}),t.fold(function(){Hi(l.element(),"visibility")},function(t){Di(l.element(),"visibility",t)}),Ri(l.element(),"left").isNone()&&Ri(l.element(),"top").isNone()&&Ri(l.element(),"right").isNone()&&Ri(l.element(),"bottom").isNone()&&Ri(l.element(),"position").is("fixed")&&Hi(l.element(),"position")},l.element())},Es=/* */Object.freeze({__proto__:null,position:function(t,n,e,o,r){_s(t,n,e,o,r,st.none())},positionWithin:_s,positionWithinBounds:Ts,getMode:function(t,n,e){return n.useFixed()?"fixed":"absolute"}}),Bs=[Zn("useFixed",c),Yn("getBounds")],Ds=La({fields:Bs,name:"positioning",active:qa,apis:Es}),Ms=function(t){Lo(t,Ao());var n=t.components();it(n,Ms)},As=function(t){var n=t.components();it(n,As),Lo(t,Mo())},Fs=function(t,n){vr(t.element(),n.element())},Is=function(n,t){var e,o=n.components();it((e=n).components(),function(t){return xr(t.element())}),yr(e.element()),e.syncComponents();var r=X(o,t);it(r,function(t){Ms(t),n.getSystem().removeFromWorld(t)}),it(t,function(t){t.getSystem().isConnected()?Fs(n,t):(n.getSystem().addToWorld(t),Fs(n,t),Oi(n.element())&&As(t)),n.syncComponents()})},Rs=function(t,n){Vs(t,n,vr)},Vs=function(t,n,e){t.getSystem().addToWorld(n),e(t.element(),n.element()),Oi(t.element())&&As(n),t.syncComponents()},Hs=function(t){Ms(t),xr(t.element()),t.getSystem().removeFromWorld(t)},Ps=function(n){var t=lr(n.element()).bind(function(t){return n.getSystem().getByDom(t).toOption()});Hs(n),t.each(function(t){t.syncComponents()})},zs=function(t){var n=t.components();it(n,Hs),yr(t.element()),t.syncComponents()},Ns=function(t,n){Ls(t,n,vr)},Ls=function(t,n,e){e(t,n.element());var o=dr(n.element());it(o,function(t){n.getByDom(t).each(As)})},js=function(n){var t=dr(n.element());it(t,function(t){n.getByDom(t).each(Ms)}),xr(n.element())},Us=function(n,t,e,o){e.get().each(function(t){zs(n)});var r=t.getAttachPoint(n);Rs(r,n);var i=n.getSystem().build(o);return Rs(n,i),e.set(i),i},Ws=function(t,n,e,o){var r=Us(t,n,e,o);return n.onOpen(t,r),r},Gs=function(n,e,o){o.get().each(function(t){zs(n),Ps(n),e.onClose(n,t),o.clear()})},Xs=function(t,n,e){return e.isOpen()},Ys=function(t,n,e){var o,r,i,u,a=n.getAttachPoint(t);Di(t.element(),"position",Ds.getMode(a)),o=t,r="visibility",i=n.cloakVisibilityAttr,u="hidden",Ri(o.element(),r).fold(function(){Fr(o.element(),i)},function(t){Br(o.element(),i,t)}),Di(o.element(),r,u)},qs=function(t,n,e){var o,r,i,u;o=t.element(),I(["top","left","right","bottom"],function(t){return Ri(o,t).isSome()})||Hi(t.element(),"position"),r=t,i="visibility",u=n.cloakVisibilityAttr,Mr(r.element(),u).fold(function(){return Hi(r.element(),i)},function(t){return Di(r.element(),i,t)})},Ks=/* */Object.freeze({__proto__:null,cloak:Ys,decloak:qs,open:Ws,openWhileCloaked:function(t,n,e,o,r){Ys(t,n),Ws(t,n,e,o),r(),qs(t,n)},close:Gs,isOpen:Xs,isPartOf:function(n,e,t,o){return Xs(0,0,t)&&t.get().exists(function(t){return e.isPartOf(n,t,o)})},getState:function(t,n,e){return e.get()},setContent:function(t,n,e,o){return e.get().map(function(){return Us(t,n,e,o)})}}),Js=/* */Object.freeze({__proto__:null,events:function(e,o){return Yo([Jo(Co(),function(t,n){Gs(t,e,o)})])}}),$s=[$u("onOpen"),$u("onClose"),zn("isPartOf"),zn("getAttachPoint"),Zn("cloakVisibilityAttr","data-precloak-visibility")],Qs=La({fields:$s,name:"sandboxing",active:Js,apis:Ks,state:/* */Object.freeze({__proto__:null,init:function(){var n=ce(st.none()),t=at("not-implemented");return oi({readState:t,isOpen:function(){return n.get().isSome()},clear:function(){n.set(st.none())},set:function(t){n.set(st.some(t))},get:function(){return n.get()}})}})}),Zs=at("dismiss.popups"),tl=at("reposition.popups"),nl=at("mouse.released"),el=sn([Zn("isExtraPart",at(!1)),Qn("fireEventInstead",[Zn("event",Fo())])]),ol=function(t){var n,e=_n("Dismissal",el,t);return(n={})[Zs()]={schema:sn([zn("target")]),onReceive:function(n,t){Qs.isOpen(n)&&(Qs.isPartOf(n,t.target)||e.isExtraPart(n,t.target)||e.fireEventInstead.fold(function(){return Qs.close(n)},function(t){return Lo(n,t.event)}))}},n},rl=sn([Qn("fireEventInstead",[Zn("event",Io())]),Un("doReposition")]),il=function(t){var n,e=_n("Reposition",rl,t);return(n={})[tl()]={onReceive:function(n){Qs.isOpen(n)&&e.fireEventInstead.fold(function(){return e.doReposition(n)},function(t){return Lo(n,t.event)})}},n},ul=function(t,n,e){n.store.manager.onLoad(t,n,e)},al=function(t,n,e){n.store.manager.onUnload(t,n,e)},cl=/* */Object.freeze({__proto__:null,onLoad:ul,onUnload:al,setValue:function(t,n,e,o){n.store.manager.setValue(t,n,e,o)},getValue:function(t,n,e){return n.store.manager.getValue(t,n,e)},getState:function(t,n,e){return e}}),sl=/* */Object.freeze({__proto__:null,events:function(e,o){var t=e.resetOnDom?[or(function(t,n){ul(t,e,o)}),rr(function(t,n){al(t,e,o)})]:[Ia(e,o,ul)];return Yo(t)}}),ll=function(){var t=ce(null);return oi({set:t.set,get:t.get,isNotSet:function(){return null===t.get()},clear:function(){t.set(null)},readState:function(){return{mode:"memory",value:t.get()}}})},fl=function(){var i=ce({}),u=ce({});return oi({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(t){return bt(i.get(),t).orThunk(function(){return bt(u.get(),t)})},update:function(t){var n=i.get(),e=u.get(),o={},r={};it(t,function(n){o[n.value]=n,bt(n,"meta").each(function(t){bt(t,"text").each(function(t){r[t]=n})})}),i.set(et(et({},n),o)),u.set(et(et({},e),r))},clear:function(){i.set({}),u.set({})}})},dl=/* */Object.freeze({__proto__:null,memory:ll,dataset:fl,manual:function(){return oi({readState:function(){}})},init:function(t){return t.store.manager.state(t)}}),ml=function(t,n,e,o){var r=n.store;e.update([o]),r.setValue(t,o),n.onSetValue(t,o)},gl=[Yn("initialValue"),zn("getFallbackEntry"),zn("getDataKey"),zn("setValue"),na("manager",{setValue:ml,getValue:function(t,n,e){var o=n.store,r=o.getDataKey(t);return e.lookup(r).fold(function(){return o.getFallbackEntry(r)},function(t){return t})},onLoad:function(n,e,o){e.store.initialValue.each(function(t){ml(n,e,o,t)})},onUnload:function(t,n,e){e.clear()},state:fl})],pl=[zn("getValue"),Zn("setValue",Z),Yn("initialValue"),na("manager",{setValue:function(t,n,e,o){n.store.setValue(t,o),n.onSetValue(t,o)},getValue:function(t,n,e){return n.store.getValue(t)},onLoad:function(n,e,t){e.store.initialValue.each(function(t){e.store.setValue(n,t)})},onUnload:Z,state:ei.init})],hl=[Yn("initialValue"),na("manager",{setValue:function(t,n,e,o){e.set(o),n.onSetValue(t,o)},getValue:function(t,n,e){return e.get()},onLoad:function(t,n,e){n.store.initialValue.each(function(t){e.isNotSet()&&e.set(t)})},onUnload:function(t,n,e){e.clear()},state:ll})],vl=[te("store",{mode:"memory"},Bn("mode",{memory:hl,manual:pl,dataset:gl})),$u("onSetValue"),Zn("resetOnDom",!1)],bl=La({fields:vl,name:"representing",active:sl,apis:cl,extra:{setValueFrom:function(t,n){var e=bl.getValue(n);bl.setValue(t,e)}},state:dl}),yl=function(o,t){return ue(o,{},V(t,function(t){return n=t.name(),e="Cannot configure "+t.name()+" for "+o,hn(n,n,Wt(),cn(function(t){return Vt("The field: "+n+" is forbidden. "+e)}));var n,e}).concat([ae("dump",ct)]))},xl=function(t){return t.dump},wl=function(t,n){return et(et({},t.dump),za(n))},Sl=yl,kl=wl,Cl="placeholder",Ol=wt([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),_l=function(t){return yt(t,"uiType")},Tl=function(t,n,e,o){return _l(e)&&e.uiType===Cl?(i=e,u=o,(r=t).exists(function(t){return t!==i.owner})?Ol.single(!0,at(i)):bt(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+lt(u)+"]\nNamespace: "+r.getOr("none")+"\nSpec: "+JSON.stringify(i,null,2))},function(t){return t.replace()})):Ol.single(!1,at(e));var r,i,u},El=function(i,u,a,c){return Tl(i,0,a,c).fold(function(t,n){var e=_l(a)?n(u,a.config,a.validated):n(u),o=bt(e,"components").getOr([]),r=U(o,function(t){return El(i,u,t,c)});return[et(et({},e),{components:r})]},function(t,n){if(_l(a)){var e=n(u,a.config,a.validated);return a.validated.preprocess.getOr(ct)(e)}return n(u)})},Bl=function(n,e,t,o){var r,i,u,a=dt(o,function(t,n){return o=t,r=!1,{name:at(e=n),required:function(){return o.fold(function(t,n){return t},function(t,n){return t})},used:function(){return r},replace:function(){if(r)throw new Error("Trying to use the same placeholder more than once: "+e);return r=!0,o}};var e,o,r}),c=(r=n,i=e,u=a,U(t,function(t){return El(r,i,t,u)}));return ft(a,function(t){if(!1===t.used()&&t.required())throw new Error("Placeholder: "+t.name()+" was not found in components list\nNamespace: "+n.getOr("none")+"\nComponents: "+JSON.stringify(e.components,null,2))}),c},Dl=Ol.single,Ml=Ol.multiple,Al=at(Cl),Fl=wt([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Il=Zn("factory",{sketch:ct}),Rl=Zn("schema",[]),Vl=zn("name"),Hl=hn("pname","pname",Gt(function(t){return"<alloy."+Nr(t.name)+">"}),Dn()),Pl=ae("schema",function(){return[Yn("preprocess")]}),zl=Zn("defaults",at({})),Nl=Zn("overrides",at({})),Ll=ln([Il,Rl,Vl,Hl,zl,Nl]),jl=ln([Il,Rl,Vl,zl,Nl]),Ul=ln([Il,Rl,Vl,Hl,zl,Nl]),Wl=ln([Il,Pl,Vl,zn("unit"),Hl,zl,Nl]),Gl=function(t){return t.fold(st.some,st.none,st.some,st.some)},Xl=function(t){var n=function(t){return t.name};return t.fold(n,n,n,n)},Yl=function(e,o){return function(t){var n=_n("Converting part type",o,t);return e(n)}},ql=Yl(Fl.required,Ll),Kl=Yl(Fl.external,jl),Jl=Yl(Fl.optional,Ul),$l=Yl(Fl.group,Wl),Ql=at("entirety"),Zl=/* */Object.freeze({__proto__:null,required:ql,external:Kl,optional:Jl,group:$l,asNamedPart:Gl,name:Xl,asCommon:function(t){return t.fold(ct,ct,ct,ct)},original:Ql}),tf=function(t,n,e,o){return Ct(n.defaults(t,e,o),e,{uid:t.partUids[n.name]},n.overrides(t,e,o))},nf=function(r,t){var n={};return it(t,function(t){Gl(t).each(function(e){var o=ef(r,e.pname);n[e.name]=function(t){var n=_n("Part: "+e.name+" in "+r,ln(e.schema),t);return et(et({},o),{config:t,validated:n})}})}),n},ef=function(t,n){return{uiType:Al(),owner:t,name:n}},of=function(t,n,e){return{uiType:Al(),owner:t,name:n,config:e,validated:{}}},rf=function(t){return U(t,function(t){return t.fold(st.none,st.some,st.none,st.none).map(function(t){return Wn(t.name,t.schema.concat([ea(Ql())]))}).toArray()})},uf=function(t){return V(t,Xl)},af=function(t,n,e){return o=n,i={},r={},it(e,function(t){t.fold(function(o){i[o.pname]=Dl(!0,function(t,n,e){return o.factory.sketch(tf(t,o,n,e))})},function(t){var n=o.parts[t.name];r[t.name]=at(t.factory.sketch(tf(o,t,n[Ql()]),n))},function(o){i[o.pname]=Dl(!1,function(t,n,e){return o.factory.sketch(tf(t,o,n,e))})},function(r){i[r.pname]=Ml(!0,function(n,t,e){var o=n[r.name];return V(o,function(t){return r.factory.sketch(Ct(r.defaults(n,t,e),t,r.overrides(n,t)))})})})}),{internals:at(i),externals:at(r)};var o,i,r},cf=function(t,n,e){return Bl(st.some(t),n,n.components,e)},sf=function(t,n,e){var o=n.partUids[e];return t.getSystem().getByUid(o).toOption()},lf=function(t,n,e){return sf(t,n,e).getOrDie("Could not find part: "+e)},ff=function(t,n,e){var o={},r=n.partUids,i=t.getSystem();return it(e,function(t){o[t]=at(i.getByUid(r[t]))}),o},df=function(t,n){var e=t.getSystem();return dt(n.partUids,function(t,n){return at(e.getByUid(t))})},mf=function(t){return lt(t.partUids)},gf=function(t,n,e){var o={},r=n.partUids,i=t.getSystem();return it(e,function(t){o[t]=at(i.getByUid(r[t]).getOrDie())}),o},pf=function(n,t){var e=uf(t);return Jt(V(e,function(t){return{key:t,value:n+"-"+t}}))},hf=function(n){return hn("partUids","partUids",Xt(function(t){return pf(t.uid,n)}),Dn())},vf=/* */Object.freeze({__proto__:null,generate:nf,generateOne:of,schemas:rf,names:uf,substitutes:af,components:cf,defaultUids:pf,defaultUidsSchema:hf,getAllParts:df,getAllPartNames:mf,getPart:sf,getPartOrDie:lf,getParts:ff,getPartsOrDie:gf}),bf=function(t,n,e,o,r){var i,u,a=(u=r,(0<(i=o).length?[Wn("parts",i)]:[]).concat([zn("uid"),Zn("dom",{}),Zn("components",[]),ea("originalSpec"),Zn("debug.sketcher",{})]).concat(u));return _n(t+" [SpecSchema]",sn(a.concat(n)),e)},yf=function(t,n,e,o,r){var i=xf(r),u=rf(e),a=hf(e),c=bf(t,n,i,u,[a]),s=af(0,c,e);return o(c,cf(t,c,s.internals()),i,s.externals())},xf=function(t){return yt(t,"uid")?t:et(et({},t),{uid:Yr("uid")})};var wf,Sf,kf=sn([zn("name"),zn("factory"),zn("configFields"),Zn("apis",{}),Zn("extraApis",{})]),Cf=sn([zn("name"),zn("factory"),zn("configFields"),zn("partFields"),Zn("apis",{}),Zn("extraApis",{})]),Of=function(t){var i=_n("Sketcher for "+t.name,kf,t),n=dt(i.apis,ni),e=dt(i.extraApis,function(t,n){return Qr(t,n)});return et(et({name:at(i.name),configFields:at(i.configFields),sketch:function(t){return n=i.name,e=i.configFields,o=i.factory,r=xf(t),o(bf(n,e,r,[],[]),r);var n,e,o,r}},n),e)},_f=function(t){var n=_n("Sketcher for "+t.name,Cf,t),e=nf(n.name,n.partFields),o=dt(n.apis,ni),r=dt(n.extraApis,function(t,n){return Qr(t,n)});return et(et({name:at(n.name),partFields:at(n.partFields),configFields:at(n.configFields),sketch:function(t){return yf(n.name,n.configFields,n.partFields,n.factory,t)},parts:at(e)},o),r)},Tf=function(t){for(var n=[],e=function(t){n.push(t)},o=0;o<t.length;o++)t[o].each(e);return n},Ef=function(t){return"input"===Cr(t)&&"radio"!==Dr(t,"type")||"textarea"===Cr(t)},Bf=/* */Object.freeze({__proto__:null,getCurrent:function(t,n,e){return n.find(t)}}),Df=[zn("find")],Mf=La({fields:Df,name:"composing",apis:Bf}),Af=function(e,o,t,r){var n=Yc(e.element(),"."+o.highlightClass);it(n,function(n){I(r,function(t){return t.element()===n})||(xi(n,o.highlightClass),e.getSystem().getByDom(n).each(function(t){o.onDehighlight(e,t),Lo(t,No())}))})},Ff=function(t,n,e,o){Af(t,n,0,[o]),If(t,n,e,o)||(bi(o.element(),n.highlightClass),n.onHighlight(t,o),Lo(o,zo()))},If=function(t,n,e,o){return wi(o.element(),n.highlightClass)},Rf=function(t,n,e,o){var r=Yc(t.element(),"."+n.itemClass);return st.from(r[o]).fold(function(){return ot.error("No element found with index "+o)},t.getSystem().getByDom)},Vf=function(n,t,e){return Mu(n.element(),"."+t.itemClass).bind(function(t){return n.getSystem().getByDom(t).toOption()})},Hf=function(n,t,e){var o=Yc(n.element(),"."+t.itemClass);return(0<o.length?st.some(o[o.length-1]):st.none()).bind(function(t){return n.getSystem().getByDom(t).toOption()})},Pf=function(e,n,t,o){var r=Yc(e.element(),"."+n.itemClass);return j(r,function(t){return wi(t,n.highlightClass)}).bind(function(t){var n=cc(t,o,0,r.length-1);return e.getSystem().getByDom(r[n]).toOption()})},zf=function(n,t,e){var o=Yc(n.element(),"."+t.itemClass);return Tf(V(o,function(t){return n.getSystem().getByDom(t).toOption()}))},Nf=/* */Object.freeze({__proto__:null,dehighlightAll:function(t,n,e){return Af(t,n,0,[])},dehighlight:function(t,n,e,o){If(t,n,e,o)&&(xi(o.element(),n.highlightClass),n.onDehighlight(t,o),Lo(o,No()))},highlight:Ff,highlightFirst:function(n,e,o){Vf(n,e).each(function(t){Ff(n,e,o,t)})},highlightLast:function(n,e,o){Hf(n,e).each(function(t){Ff(n,e,o,t)})},highlightAt:function(n,e,o,t){Rf(n,e,o,t).fold(function(t){throw new Error(t)},function(t){Ff(n,e,o,t)})},highlightBy:function(n,e,o,t){var r=zf(n,e);L(r,t).each(function(t){Ff(n,e,o,t)})},isHighlighted:If,getHighlighted:function(n,t,e){return Mu(n.element(),"."+t.highlightClass).bind(function(t){return n.getSystem().getByDom(t).toOption()})},getFirst:Vf,getLast:Hf,getPrevious:function(t,n,e){return Pf(t,n,0,-1)},getNext:function(t,n,e){return Pf(t,n,0,1)},getCandidates:zf}),Lf=[zn("highlightClass"),zn("itemClass"),$u("onHighlight"),$u("onDehighlight")],jf=La({fields:Lf,name:"highlighting",apis:Nf}),Uf=function(){return[13]},Wf=function(){return[27]},Gf=function(){return[32]},Xf=function(){return[37]},Yf=function(){return[38]},qf=function(){return[39]},Kf=function(){return[40]},Jf=function(t,n,e){var o=G(t.slice(0,n)),r=G(t.slice(n+1));return L(o.concat(r),e)},$f=function(t,n,e){var o=G(t.slice(0,n));return L(o,e)},Qf=function(t,n,e){var o=t.slice(0,n),r=t.slice(n+1);return L(r.concat(o),e)},Zf=function(t,n,e){var o=t.slice(n+1);return L(o,e)},td=function(e){return function(t){var n=t.raw();return F(e,n.which)}},nd=function(t){return function(n){return W(t,function(t){return t(n)})}},ed=function(t){return!0===t.raw().shiftKey},od=function(t){return!0===t.raw().ctrlKey},rd=x(ed),id=function(t,n){return{matches:t,classification:n}},ud=function(t,n,e){n.exists(function(n){return e.exists(function(t){return Le(t,n)})})||jo(t,Ro(),{prevFocus:n,newFocus:e})},ad=function(){var r=function(t){return $a(t.element())};return{get:r,set:function(t,n){var e=r(t);t.getSystem().triggerFocus(n,t.element());var o=r(t);ud(t,e,o)}}},cd=function(){var r=function(t){return jf.getHighlighted(t).map(function(t){return t.element()})};return{get:r,set:function(n,t){var e=r(n);n.getSystem().getByDom(t).fold(Z,function(t){jf.highlight(n,t)});var o=r(n);ud(n,e,o)}}};(Sf=wf=wf||{}).OnFocusMode="onFocus",Sf.OnEnterOrSpaceMode="onEnterOrSpace",Sf.OnApiMode="onApi";var sd,ld=function(t,n,e,o,a){var c=function(n,e,t,o,r){var i,u,a=t(n,e,o,r);return i=a,u=e.event(),L(i,function(t){return t.matches(u)}).map(function(t){return t.classification}).bind(function(t){return t(n,e,o,r)})},r={schema:function(){return t.concat([Zn("focusManager",ad()),te("focusInside","onFocus",Sn(function(t){return F(["onFocus","onEnterOrSpace","onApi"],t)?ot.value(t):ot.error("Invalid value for focusInside")})),na("handler",r),na("state",n),na("sendFocusIn",a)])},processKey:c,toEvents:function(i,u){var t=i.focusInside!==wf.OnFocusMode?st.none():a(i).map(function(e){return Jo(ho(),function(t,n){e(t,i,u),n.stop()})}),n=[Jo(ao(),function(o,r){c(o,r,e,i,u).fold(function(){var n,e,t;n=o,e=r,t=td(Gf().concat(Uf()))(e.event()),i.focusInside===wf.OnEnterOrSpaceMode&&t&&Ye(n,e)&&a(i).each(function(t){t(n,i,u),e.stop()})},function(t){r.stop()})}),Jo(co(),function(t,n){c(t,n,o,i,u).each(function(t){n.stop()})})];return Yo(t.toArray().concat(n))}};return r},fd=function(t){var n=[Yn("onEscape"),Yn("onEnter"),Zn("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),Zn("firstTabstop",0),Zn("useTabstopAt",at(!0)),Yn("visibilitySelector")].concat([t]),u=function(t,n){var e=t.visibilitySelector.bind(function(t){return Au(n,t)}).getOr(n);return 0<tu(e)},e=function(n,e,t){var o,r,i;o=e,r=Yc(n.element(),o.selector),i=P(r,function(t){return u(o,t)}),st.from(i[o.firstTabstop]).each(function(t){e.focusManager.set(n,t)})},a=function(n,t,e,o,r){return r(t,e,function(t){return u(n=o,e=t)&&n.useTabstopAt(e);var n,e}).fold(function(){return o.cyclic?st.some(!0):st.none()},function(t){return o.focusManager.set(n,t),st.some(!0)})},r=function(n,t,e,o){var r,i,u=Yc(n.element(),e.selector);return r=n,(i=e).focusManager.get(r).bind(function(t){return Au(t,i.selector)}).bind(function(t){return j(u,g(Le,t)).bind(function(t){return a(n,u,t,e,o)})})},o=at([id(nd([ed,td([9])]),function(t,n,e){var o=e.cyclic?Jf:$f;return r(t,0,e,o)}),id(td([9]),function(t,n,e){var o=e.cyclic?Qf:Zf;return r(t,0,e,o)}),id(td(Wf()),function(n,e,t){return t.onEscape.bind(function(t){return t(n,e)})}),id(nd([rd,td(Uf())]),function(n,e,t){return t.onEnter.bind(function(t){return t(n,e)})})]),i=at([]);return ld(n,ei.init,o,i,function(){return st.some(e)})},dd=fd(ae("cyclic",at(!1))),md=fd(ae("cyclic",at(!0))),gd=function(t,n,e){return Ef(e)&&td(Gf())(n.event())?st.none():(Wo(t,e,xo()),st.some(!0))},pd=function(t,n){return st.some(!0)},hd=[Zn("execute",gd),Zn("useSpace",!1),Zn("useEnter",!0),Zn("useControlEnter",!1),Zn("useDown",!1)],vd=function(t,n,e){return e.execute(t,n,t.element())},bd=ld(hd,ei.init,function(t,n,e,o){var r=e.useSpace&&!Ef(t.element())?Gf():[],i=e.useEnter?Uf():[],u=e.useDown?Kf():[],a=r.concat(i).concat(u);return[id(td(a),vd)].concat(e.useControlEnter?[id(nd([od,td(Uf())]),vd)]:[])},function(t,n,e,o){return e.useSpace&&!Ef(t.element())?[id(td(Gf()),pd)]:[]},function(){return st.none()}),yd=function(){var e=ce(st.none());return oi({readState:function(){return e.get().map(function(t){return{numRows:String(t.numRows),numColumns:String(t.numColumns)}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(t,n){e.set(st.some({numRows:t,numColumns:n}))},getNumRows:function(){return e.get().map(function(t){return t.numRows})},getNumColumns:function(){return e.get().map(function(t){return t.numColumns})}})},xd=/* */Object.freeze({__proto__:null,flatgrid:yd,init:function(t){return t.state(t)}}),wd=function(i){return function(t,n,e,o){var r=i(t.element());return Od(r,t,n,e,o)}},Sd=function(t,n){var e=kc(t,n);return wd(e)},kd=function(t,n){var e=kc(n,t);return wd(e)},Cd=function(r){return function(t,n,e,o){return Od(r,t,n,e,o)}},Od=function(n,e,t,o,r){return o.focusManager.get(e).bind(function(t){return n(e.element(),t,o,r)}).map(function(t){return o.focusManager.set(e,t),!0})},_d=Cd,Td=Cd,Ed=Cd,Bd=function(t){return!((n=t.dom()).offsetWidth<=0&&n.offsetHeight<=0);var n},Dd=function(t,n,e){var o,r=Yc(t,e),i=P(r,Bd);return j(o=i,function(t){return Le(t,n)}).map(function(t){return{index:at(t),candidates:at(o)}})},Md=function(t,n){return j(t,function(t){return Le(n,t)})},Ad=function(e,t,o,n){return n(Math.floor(t/o),t%o).bind(function(t){var n=t.row()*o+t.column();return 0<=n&&n<e.length?st.some(e[n]):st.none()})},Fd=function(r,t,i,u,a){return Ad(r,t,u,function(t,n){var e=t===i-1?r.length-t*u:u,o=cc(n,a,0,e-1);return st.some({row:at(t),column:at(o)})})},Id=function(i,t,u,a,c){return Ad(i,t,a,function(t,n){var e=cc(t,c,0,u-1),o=e===u-1?i.length-e*a:a,r=sc(n,0,o-1);return st.some({row:at(e),column:at(r)})})},Rd=[zn("selector"),Zn("execute",gd),Qu("onEscape"),Zn("captureTab",!1),oa()],Vd=function(n,e,t){Mu(n.element(),e.selector).each(function(t){e.focusManager.set(n,t)})},Hd=function(r){return function(t,n,e,o){return Dd(t,n,e.selector).bind(function(t){return r(t.candidates(),t.index(),o.getNumRows().getOr(e.initSize.numRows),o.getNumColumns().getOr(e.initSize.numColumns))})}},Pd=function(t,n,e){return e.captureTab?st.some(!0):st.none()},zd=Hd(function(t,n,e,o){return Fd(t,n,e,o,-1)}),Nd=Hd(function(t,n,e,o){return Fd(t,n,e,o,1)}),Ld=Hd(function(t,n,e,o){return Id(t,n,e,o,-1)}),jd=Hd(function(t,n,e,o){return Id(t,n,e,o,1)}),Ud=at([id(td(Xf()),Sd(zd,Nd)),id(td(qf()),kd(zd,Nd)),id(td(Yf()),_d(Ld)),id(td(Kf()),Td(jd)),id(nd([ed,td([9])]),Pd),id(nd([rd,td([9])]),Pd),id(td(Wf()),function(t,n,e){return e.onEscape(t,n)}),id(td(Gf().concat(Uf())),function(n,e,o,t){return r=n,(i=o).focusManager.get(r).bind(function(t){return Au(t,i.selector)}).bind(function(t){return o.execute(n,e,t)});var r,i})]),Wd=at([id(td(Gf()),pd)]),Gd=ld(Rd,yd,Ud,Wd,function(){return st.some(Vd)}),Xd=function(t,n,e,i){var u=function(t,n,e){var o,r=cc(n,i,0,e.length-1);return r===t?st.none():(o=e[r],"button"===Cr(o)&&"disabled"===Dr(o,"disabled")?u(t,r,e):st.from(e[r]))};return Dd(t,e,n).bind(function(t){var n=t.index(),e=t.candidates();return u(n,n,e)})},Yd=[zn("selector"),Zn("getInitial",st.none),Zn("execute",gd),Qu("onEscape"),Zn("executeOnMove",!1),Zn("allowVertical",!0)],qd=function(n,e,o){return t=n,(r=o).focusManager.get(t).bind(function(t){return Au(t,r.selector)}).bind(function(t){return o.execute(n,e,t)});var t,r},Kd=function(n,e,t){e.getInitial(n).orThunk(function(){return Mu(n.element(),e.selector)}).each(function(t){e.focusManager.set(n,t)})},Jd=function(t,n,e){return Xd(t,e.selector,n,-1)},$d=function(t,n,e){return Xd(t,e.selector,n,1)},Qd=function(r){return function(t,n,e,o){return r(t,n,e,o).bind(function(){return e.executeOnMove?qd(t,n,e):st.some(!0)})}},Zd=function(t,n,e){return e.onEscape(t,n)},tm=at([id(td(Gf()),pd)]),nm=ld(Yd,ei.init,function(t,n,e,o){var r=Xf().concat(e.allowVertical?Yf():[]),i=qf().concat(e.allowVertical?Kf():[]);return[id(td(r),Qd(Sd(Jd,$d))),id(td(i),Qd(kd(Jd,$d))),id(td(Uf()),qd),id(td(Gf()),qd),id(td(Wf()),Zd)]},tm,function(){return st.some(Kd)}),em=function(t,n,e){return st.from(t[n]).bind(function(t){return st.from(t[e]).map(function(t){return{rowIndex:n,columnIndex:e,cell:t}})})},om=function(t,n,e,o){var r=t[n].length,i=cc(e,o,0,r-1);return em(t,n,i)},rm=function(t,n,e,o){var r=cc(e,o,0,t.length-1),i=t[r].length,u=sc(n,0,i-1);return em(t,r,u)},im=function(t,n,e,o){var r=t[n].length,i=sc(e+o,0,r-1);return em(t,n,i)},um=function(t,n,e,o){var r=sc(e+o,0,t.length-1),i=t[r].length,u=sc(n,0,i-1);return em(t,r,u)},am=[Wn("selectors",[zn("row"),zn("cell")]),Zn("cycles",!0),Zn("previousSelector",st.none),Zn("execute",gd)],cm=function(n,e,t){e.previousSelector(n).orThunk(function(){var t=e.selectors;return Mu(n.element(),t.cell)}).each(function(t){e.focusManager.set(n,t)})},sm=function(t,n){return function(e,o,i){var u=i.cycles?t:n;return Au(o,i.selectors.row).bind(function(t){var n=Yc(t,i.selectors.cell);return Md(n,o).bind(function(o){var r=Yc(e,i.selectors.row);return Md(r,t).bind(function(t){var n,e=(n=i,V(r,function(t){return Yc(t,n.selectors.cell)}));return u(e,t,o).map(function(t){return t.cell})})})})}},lm=sm(function(t,n,e){return om(t,n,e,-1)},function(t,n,e){return im(t,n,e,-1)}),fm=sm(function(t,n,e){return om(t,n,e,1)},function(t,n,e){return im(t,n,e,1)}),dm=sm(function(t,n,e){return rm(t,e,n,-1)},function(t,n,e){return um(t,e,n,-1)}),mm=sm(function(t,n,e){return rm(t,e,n,1)},function(t,n,e){return um(t,e,n,1)}),gm=at([id(td(Xf()),Sd(lm,fm)),id(td(qf()),kd(lm,fm)),id(td(Yf()),_d(dm)),id(td(Kf()),Td(mm)),id(td(Gf().concat(Uf())),function(n,e,o){return $a(n.element()).bind(function(t){return o.execute(n,e,t)})})]),pm=at([id(td(Gf()),pd)]),hm=ld(am,ei.init,gm,pm,function(){return st.some(cm)}),vm=[zn("selector"),Zn("execute",gd),Zn("moveOnTab",!1)],bm=function(n,e,o){return o.focusManager.get(n).bind(function(t){return o.execute(n,e,t)})},ym=function(n,e,t){Mu(n.element(),e.selector).each(function(t){e.focusManager.set(n,t)})},xm=function(t,n,e){return Xd(t,e.selector,n,-1)},wm=function(t,n,e){return Xd(t,e.selector,n,1)},Sm=at([id(td(Yf()),Ed(xm)),id(td(Kf()),Ed(wm)),id(nd([ed,td([9])]),function(t,n,e,o){return e.moveOnTab?Ed(xm)(t,n,e,o):st.none()}),id(nd([rd,td([9])]),function(t,n,e,o){return e.moveOnTab?Ed(wm)(t,n,e,o):st.none()}),id(td(Uf()),bm),id(td(Gf()),bm)]),km=at([id(td(Gf()),pd)]),Cm=ld(vm,ei.init,Sm,km,function(){return st.some(ym)}),Om=[Qu("onSpace"),Qu("onEnter"),Qu("onShiftEnter"),Qu("onLeft"),Qu("onRight"),Qu("onTab"),Qu("onShiftTab"),Qu("onUp"),Qu("onDown"),Qu("onEscape"),Zn("stopSpaceKeyup",!1),Yn("focusIn")],_m=ld(Om,ei.init,function(t,n,e){return[id(td(Gf()),e.onSpace),id(nd([rd,td(Uf())]),e.onEnter),id(nd([ed,td(Uf())]),e.onShiftEnter),id(nd([ed,td([9])]),e.onShiftTab),id(nd([rd,td([9])]),e.onTab),id(td(Yf()),e.onUp),id(td(Kf()),e.onDown),id(td(Xf()),e.onLeft),id(td(qf()),e.onRight),id(td(Gf()),e.onSpace),id(td(Wf()),e.onEscape)]},function(t,n,e){return e.stopSpaceKeyup?[id(td(Gf()),pd)]:[]},function(t){return t.focusIn}),Tm=dd.schema(),Em=md.schema(),Bm=nm.schema(),Dm=Gd.schema(),Mm=hm.schema(),Am=bd.schema(),Fm=Cm.schema(),Im=_m.schema(),Rm=Ua({branchKey:"mode",branches:/* */Object.freeze({__proto__:null,acyclic:Tm,cyclic:Em,flow:Bm,flatgrid:Dm,matrix:Mm,execution:Am,menu:Fm,special:Im}),name:"keying",active:{events:function(t,n){return t.handler.toEvents(t,n)}},apis:{focusIn:function(n,e,o){e.sendFocusIn(e).fold(function(){n.getSystem().triggerFocus(n.element(),n.element())},function(t){t(n,e,o)})},setGridSize:function(t,n,e,o,r){xt(e,"setGridSize")?e.setGridSize(o,r):nt.console.error("Layout does not support setGridSize")}},state:xd}),Vm=function(t,n,e,o){var r=t.getSystem().build(o);Vs(t,r,e)},Hm=function(t,n,e,o){var r=Pm(t);L(r,function(t){return Le(o.element(),t.element())}).each(Ps)},Pm=function(t,n){return t.components()},zm=function(n,t,e,r,o){var i=Pm(n);return st.from(i[r]).map(function(t){return Hm(n,0,0,t),o.each(function(t){Vm(n,0,function(t,n){var e,o;o=n,mr(e=t,r).fold(function(){vr(e,o)},function(t){gr(t,o)})},t)}),t})},Nm=La({fields:[],name:"replacing",apis:/* */Object.freeze({__proto__:null,append:function(t,n,e,o){Vm(t,0,vr,o)},prepend:function(t,n,e,o){Vm(t,0,hr,o)},remove:Hm,replaceAt:zm,replaceBy:function(n,t,e,o,r){var i=Pm(n);return j(i,o).bind(function(t){return zm(n,0,0,t,r)})},set:function(n,t,e,o){Qa(function(){var t=V(o,n.getSystem().build);Is(n,t)},n.element())},contents:Pm})}),Lm=function(t,n){var e,o;return{key:t,value:{config:{},me:(e=t,o=Yo(n),La({fields:[zn("enabled")],name:e,active:{events:at(o)}})),configAsRaw:at({}),initialConfig:{},state:ei}}},jm=function(t,n){n.ignore||(Ka(t.element()),n.onFocus(t))},Um=/* */Object.freeze({__proto__:null,focus:jm,blur:function(t,n){n.ignore||t.element().dom().blur()},isFocused:function(t){return n=t.element(),e=ar(n).dom(),n.dom()===e.activeElement;var n,e}}),Wm=/* */Object.freeze({__proto__:null,exhibit:function(t,n){var e=n.ignore?{}:{attributes:{tabindex:"-1"}};return ii(e)},events:function(e){return Yo([Jo(ho(),function(t,n){jm(t,e),n.stop()})].concat(e.stopMousedown?[Jo(to(),function(t,n){n.event().prevent()})]:[]))}}),Gm=[$u("onFocus"),Zn("stopMousedown",!1),Zn("ignore",!1)],Xm=La({fields:Gm,name:"focusing",active:Wm,apis:Um}),Ym=function(t,n,e){var o=n.aria;o.update(t,o,e.get())},qm=function(n,t,e){t.toggleClass.each(function(t){(e.get()?bi:xi)(n.element(),t)})},Km=function(t,n,e){Qm(t,n,e,!e.get())},Jm=function(t,n,e){e.set(!0),qm(t,n,e),Ym(t,n,e)},$m=function(t,n,e){e.set(!1),qm(t,n,e),Ym(t,n,e)},Qm=function(t,n,e,o){(o?Jm:$m)(t,n,e)},Zm=function(t,n,e){Qm(t,n,e,n.selected)},tg=/* */Object.freeze({__proto__:null,onLoad:Zm,toggle:Km,isOn:function(t,n,e){return e.get()},on:Jm,off:$m,set:Qm}),ng=/* */Object.freeze({__proto__:null,exhibit:function(){return ii({})},events:function(t,n){var e,o,r,i=(e=t,o=n,r=Km,ur(function(t){r(t,e,o)})),u=Ia(t,n,Zm);return Yo(ut([t.toggleOnExecute?[i]:[],[u]]))}}),eg=function(t,n,e){Br(t.element(),"aria-expanded",e)},og=[Zn("selected",!1),Yn("toggleClass"),Zn("toggleOnExecute",!0),te("aria",{mode:"none"},Bn("mode",{pressed:[Zn("syncWithExpanded",!1),na("update",function(t,n,e){Br(t.element(),"aria-pressed",e),n.syncWithExpanded&&eg(t,n,e)})],checked:[na("update",function(t,n,e){Br(t.element(),"aria-checked",e)})],expanded:[na("update",eg)],selected:[na("update",function(t,n,e){Br(t.element(),"aria-selected",e)})],none:[na("update",Z)]}))],rg=La({fields:og,name:"toggling",active:ng,apis:tg,state:(sd=!1,{init:function(){var n=ce(sd);return{get:function(){return n.get()},set:function(t){return n.set(t)},clear:function(){return n.set(sd)},readState:function(){return n.get()}}}})}),ig=function(){var t=function(t,n){n.stop(),Uo(t)};return[Jo(fo(),t),Jo(So(),t),nr(Je()),nr(to())]},ug=function(t){return Yo(ut([t.map(function(e){return ur(function(t,n){e(t),n.stop()})}).toArray(),ig()]))},ag="alloy.item-hover",cg="alloy.item-focus",sg=function(t){($a(t.element()).isNone()||Xm.isFocused(t))&&(Xm.isFocused(t)||Xm.focus(t),jo(t,ag,{item:t}))},lg=function(t){jo(t,cg,{item:t})},fg=at(ag),dg=at(cg),mg=[zn("data"),zn("components"),zn("dom"),Zn("hasSubmenu",!1),Yn("toggling"),Sl("itemBehaviours",[rg,Xm,Rm,bl]),Zn("ignoreFocus",!1),Zn("domModification",{}),na("builder",function(t){return{dom:t.dom,domModification:et(et({},t.domModification),{attributes:et(et(et({role:t.toggling.isSome()?"menuitemcheckbox":"menuitem"},t.domModification.attributes),{"aria-haspopup":t.hasSubmenu}),t.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:kl(t.itemBehaviours,[t.toggling.fold(rg.revoke,function(t){return rg.config(et({aria:{mode:"checked"}},t))}),Xm.config({ignore:t.ignoreFocus,stopMousedown:t.ignoreFocus,onFocus:function(t){lg(t)}}),Rm.config({mode:"execution"}),bl.config({store:{mode:"memory",initialValue:t.data}}),Lm("item-type-events",b(ig(),[Jo(ro(),sg),Jo(wo(),Xm.focus)]))]),components:t.components,eventOrder:t.eventOrder}}),Zn("eventOrder",{})],gg=[zn("dom"),zn("components"),na("builder",function(t){return{dom:t.dom,components:t.components,events:Yo([(n=wo(),Jo(n,function(t,n){n.stop()}))])};var n})],pg=function(){return"item-widget"},hg=at([ql({name:"widget",overrides:function(n){return{behaviours:za([bl.config({store:{mode:"manual",getValue:function(t){return n.data},setValue:function(){}}})])}}})]),vg=[zn("uid"),zn("data"),zn("components"),zn("dom"),Zn("autofocus",!1),Zn("ignoreFocus",!1),Sl("widgetBehaviours",[bl,Xm,Rm]),Zn("domModification",{}),hf(hg()),na("builder",function(e){var t=af(pg(),e,hg()),n=cf(pg(),e,t.internals()),o=function(t){return sf(t,e,"widget").map(function(t){return Rm.focusIn(t),t})},r=function(t,n){return Ef(n.event().target())||e.autofocus&&n.setSource(t.element()),st.none()};return{dom:e.dom,components:n,domModification:e.domModification,events:Yo([ur(function(t,n){o(t).each(function(t){n.stop()})}),Jo(ro(),sg),Jo(wo(),function(t,n){e.autofocus?o(t):Xm.focus(t)})]),behaviours:kl(e.widgetBehaviours,[bl.config({store:{mode:"memory",initialValue:e.data}}),Xm.config({ignore:e.ignoreFocus,onFocus:function(t){lg(t)}}),Rm.config({mode:"special",focusIn:e.autofocus?function(t){o(t)}:Wa(),onLeft:r,onRight:r,onEscape:function(t,n){return Xm.isFocused(t)||e.autofocus?(e.autofocus&&n.setSource(t.element()),st.none()):(Xm.focus(t),st.some(!0))}})])}})],bg=Bn("type",{widget:vg,item:mg,separator:gg}),yg=at([$l({factory:{sketch:function(t){var n=_n("menu.spec item",bg,t);return n.builder(n)}},name:"items",unit:"item",defaults:function(t,n){return n.hasOwnProperty("uid")?n:et(et({},n),{uid:Yr("item")})},overrides:function(t,n){return{type:n.type,ignoreFocus:t.fakeFocus,domModification:{classes:[t.markers.item]}}}})]),xg=at([zn("value"),zn("items"),zn("dom"),zn("components"),Zn("eventOrder",{}),yl("menuBehaviours",[jf,bl,Mf,Rm]),te("movement",{mode:"menu",moveOnTab:!0},Bn("mode",{grid:[oa(),na("config",function(t,n){return{mode:"flatgrid",selector:"."+t.markers.item,initSize:{numColumns:n.initSize.numColumns,numRows:n.initSize.numRows},focusManager:t.focusManager}})],matrix:[na("config",function(t,n){return{mode:"matrix",selectors:{row:n.rowSelector,cell:"."+t.markers.item},focusManager:t.focusManager}}),zn("rowSelector")],menu:[Zn("moveOnTab",!0),na("config",function(t,n){return{mode:"menu",selector:"."+t.markers.item,moveOnTab:n.moveOnTab,focusManager:t.focusManager}})]})),Nn("markers",Xu()),Zn("fakeFocus",!1),Zn("focusManager",ad()),$u("onHighlight")]),wg=at("alloy.menu-focus"),Sg=_f({name:"Menu",configFields:xg(),partFields:yg(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,markers:t.markers,behaviours:wl(t.menuBehaviours,[jf.config({highlightClass:t.markers.selectedItem,itemClass:t.markers.item,onHighlight:t.onHighlight}),bl.config({store:{mode:"memory",initialValue:t.value}}),Mf.config({find:st.some}),Rm.config(t.movement.config(t,t.movement))]),events:Yo([Jo(dg(),function(n,e){var t=e.event();n.getSystem().getByDom(t.target()).each(function(t){jf.highlight(n,t),e.stop(),jo(n,wg(),{menu:n,item:t})})}),Jo(fg(),function(t,n){var e=n.event().item();jf.highlight(t,e)})]),components:n,eventOrder:t.eventOrder,domModification:{attributes:{role:"menu"}}}}}),kg=function(e,o,r,t){return bt(r,t).bind(function(t){return bt(e,t).bind(function(t){var n=kg(e,o,r,t);return st.some([t].concat(n))})}).getOr([])},Cg=function(t,n){var e={};ft(t,function(t,n){it(t,function(t){e[t]=n})});var o=n,r=mt(n,function(t,n){return{k:t,v:n}}),i=dt(r,function(t,n){return[n].concat(kg(e,o,r,n))});return dt(e,function(t){return bt(i,t).getOr([t])})},Og=function(t){return"prepared"===t.type?st.some(t.menu):st.none()},_g={init:function(){var i=ce({}),u=ce({}),a=ce({}),c=ce(st.none()),s=ce({}),r=function(t,o,r){return e(t).bind(function(n){return e=t,ht(i.get(),function(t,n){return t===e}).bind(function(t){return o(t).map(function(t){return{triggeredMenu:n,triggeringItem:t,triggeringPath:r}})});var e})},e=function(t){return n(t).bind(Og)},n=function(t){return bt(u.get(),t)},l=function(t){return bt(i.get(),t)};return{setMenuBuilt:function(t,n){var e;u.set(et(et({},u.get()),((e={})[t]={type:"prepared",menu:n},e)))},setContents:function(t,n,e,o){c.set(st.some(t)),i.set(e),u.set(n),s.set(o);var r=Cg(o,e);a.set(r)},expand:function(e){return bt(i.get(),e).map(function(t){var n=bt(a.get(),e).getOr([]);return[t].concat(n)})},refresh:function(t){return bt(a.get(),t)},collapse:function(t){return bt(a.get(),t).bind(function(t){return 1<t.length?st.some(t.slice(1)):st.none()})},lookupMenu:n,lookupItem:l,otherMenus:function(t){var n=s.get();return X(lt(n),t)},getPrimary:function(){return c.get().bind(e)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(st.none())},isClear:function(){return c.get().isNone()},getTriggeringPath:function(t,o){var n=P(l(t).toArray(),function(t){return e(t).isSome()});return bt(a.get(),t).bind(function(t){var e=G(n.concat(t));return function(t){for(var n=[],e=0;e<t.length;e++){var o=t[e];if(!o.isSome())return st.none();n.push(o.getOrDie())}return st.some(n)}(U(e,function(t,n){return r(t,o,e.slice(0,n+1)).fold(function(){return c.get().is(t)?[]:[st.none()]},function(t){return[st.some(t)]})}))})}}},extractPreparedMenu:Og},Tg=at("collapse-item"),Eg=Of({name:"TieredMenu",configFields:[ta("onExecute"),ta("onEscape"),Zu("onOpenMenu"),Zu("onOpenSubmenu"),$u("onRepositionMenu"),$u("onCollapseMenu"),Zn("highlightImmediately",!0),Wn("data",[zn("primary"),zn("menus"),zn("expansions")]),Zn("fakeFocus",!1),$u("onHighlight"),$u("onHover"),qu(),zn("dom"),Zn("navigateOnHover",!0),Zn("stayInDom",!1),yl("tmenuBehaviours",[Rm,jf,Mf,Nm]),Zn("eventOrder",{})],apis:{collapseMenu:function(t,n){t.collapseMenu(n)},highlightPrimary:function(t,n){t.highlightPrimary(n)},repositionMenus:function(t,n){t.repositionMenus(n)}},factory:function(a,t){var c,n,i=ce(st.none()),s=_g.init(),e=function(t){var o,r,n,e=(o=t,r=a.data.primary,n=a.data.menus,dt(n,function(t,n){var e=function(){return Sg.sketch(et(et({dom:t.dom},t),{value:n,items:t.items,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:(a.fakeFocus?cd:ad)()}))};return n===r?{type:"prepared",menu:o.getSystem().build(e())}:{type:"notbuilt",nbMenu:e}})),i=u();return s.setContents(a.data.primary,e,a.data.expansions,i),s.getPrimary()},l=function(t){return bl.getValue(t).value},u=function(t){return dt(a.data.menus,function(t,n){return U(t.items,function(t){return"separator"===t.type?[]:[t.data.value]})})},f=function(n,t){jf.highlight(n,t),jf.getHighlighted(t).orThunk(function(){return jf.getFirst(t)}).each(function(t){Wo(n,t.element(),wo())})},d=function(n,t){return Tf(V(t,function(t){return n.lookupMenu(t).bind(function(t){return"prepared"===t.type?st.some(t.menu):st.none()})}))},m=function(n,t,e){var o=d(t,t.otherMenus(e));it(o,function(t){ki(t.element(),[a.markers.backgroundMenu]),a.stayInDom||Nm.remove(n,t)})},g=function(t,o){var r,n=(r=t,i.get().getOrThunk(function(){var e={},t=Yc(r.element(),"."+a.markers.item),n=P(t,function(t){return"true"===Dr(t,"aria-haspopup")});return it(n,function(t){r.getSystem().getByDom(t).each(function(t){var n=l(t);e[n]=t})}),i.set(st.some(e)),e}));ft(n,function(t,n){var e=F(o,n);Br(t.element(),"aria-expanded",e)})},p=function(o,r,i){return st.from(i[0]).bind(function(t){return r.lookupMenu(t).bind(function(t){if("notbuilt"===t.type)return st.none();var n=t.menu,e=d(r,i.slice(1));return it(e,function(t){bi(t.element(),a.markers.backgroundMenu)}),Oi(n.element())||Nm.append(o,$i(n)),ki(n.element(),[a.markers.backgroundMenu]),f(o,n),m(o,r,i),st.some(n)})})};(n=c=c||{})[n.HighlightSubmenu=0]="HighlightSubmenu",n[n.HighlightParent=1]="HighlightParent";var h=function(r,i,u){void 0===u&&(u=c.HighlightSubmenu);var t=l(i);return s.expand(t).bind(function(o){return g(r,o),st.from(o[0]).bind(function(e){return s.lookupMenu(e).bind(function(t){var n=function(t,n,e){if("notbuilt"!==e.type)return e.menu;var o=t.getSystem().build(e.nbMenu());return s.setMenuBuilt(n,o),o}(r,e,t);return Oi(n.element())||Nm.append(r,$i(n)),a.onOpenSubmenu(r,i,n,G(o)),u===c.HighlightSubmenu?(jf.highlightFirst(n),p(r,s,o)):(jf.dehighlightAll(n),st.some(i))})})})},o=function(n,e){var t=l(e);return s.collapse(t).bind(function(t){return g(n,t),p(n,s,t).map(function(t){return a.onCollapseMenu(n,e,t),t})})},r=function(e){return function(n,t){return Au(t.getSource(),"."+a.markers.item).bind(function(t){return n.getSystem().getByDom(t).toOption().bind(function(t){return e(n,t).map(function(){return!0})})})}},v=Yo([Jo(wg(),function(e,o){var t=o.event().item();s.lookupItem(l(t)).each(function(){var t=o.event().menu();jf.highlight(e,t);var n=l(o.event().item());s.refresh(n).each(function(t){return m(e,s,t)})})}),ur(function(n,t){var e=t.event().target();n.getSystem().getByDom(e).each(function(t){0===l(t).indexOf("collapse-item")&&o(n,t),h(n,t,c.HighlightSubmenu).fold(function(){a.onExecute(n,t)},function(){})})}),or(function(n,t){e(n).each(function(t){Nm.append(n,$i(t)),a.onOpenMenu(n,t),a.highlightImmediately&&f(n,t)})})].concat(a.navigateOnHover?[Jo(fg(),function(t,n){var e,o,r=n.event().item();e=t,o=l(r),s.refresh(o).bind(function(t){return g(e,t),p(e,s,t)}),h(t,r,c.HighlightParent),a.onHover(t,r)})]:[])),b=function(t){return jf.getHighlighted(t).bind(jf.getHighlighted)},y={collapseMenu:function(n){b(n).each(function(t){o(n,t)})},highlightPrimary:function(n){s.getPrimary().each(function(t){f(n,t)})},repositionMenus:function(o){s.getPrimary().bind(function(n){return b(o).bind(function(t){var n=l(t),e=vt(s.getMenus()),o=Tf(V(e,_g.extractPreparedMenu));return s.getTriggeringPath(n,function(t){return e=t,Q(o,function(t){if(!t.getSystem().isConnected())return st.none();var n=jf.getCandidates(t);return L(n,function(t){return l(t)===e})});var e})}).map(function(t){return{primary:n,triggeringPath:t}})}).fold(function(){var t;t=o,st.from(t.components()[0]).filter(function(t){return"menu"===Dr(t.element(),"role")}).each(function(t){a.onRepositionMenu(o,t,[])})},function(t){var n=t.primary,e=t.triggeringPath;a.onRepositionMenu(o,n,e)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:wl(a.tmenuBehaviours,[Rm.config({mode:"special",onRight:r(function(t,n){return Ef(n.element())?st.none():h(t,n,c.HighlightSubmenu)}),onLeft:r(function(t,n){return Ef(n.element())?st.none():o(t,n)}),onEscape:r(function(t,n){return o(t,n).orThunk(function(){return a.onEscape(t,n).map(function(){return t})})}),focusIn:function(n,t){s.getPrimary().each(function(t){Wo(n,t.element(),wo())})}}),jf.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),Mf.config({find:function(t){return jf.getHighlighted(t)}}),Nm.config({})]),eventOrder:a.eventOrder,apis:y,events:v}},extraApis:{tieredData:function(t,n,e){return{primary:t,menus:n,expansions:e}},singleData:function(t,n){return{primary:t,menus:Kt(t,n),expansions:{}}},collapseItem:function(t){return{value:Nr(Tg()),meta:{text:t}}}}}),Bg=Of({name:"InlineView",configFields:[zn("lazySink"),$u("onShow"),$u("onHide"),$n("onEscape"),yl("inlineBehaviours",[Qs,bl,Ya]),Qn("fireDismissalEventInstead",[Zn("event",Fo())]),Qn("fireRepositionEventInstead",[Zn("event",Io())]),Zn("getRelated",st.none),Zn("isExtraPart",c),Zn("eventOrder",st.none)],factory:function(m,t){var o=function(t,n,e,o){r(t,n,e,function(){return o.map(function(t){return wu(t)})})},r=function(t,n,e,o){var r=m.lazySink(t).getOrDie();Qs.openWhileCloaked(t,e,function(){return Ds.positionWithinBounds(r,n,t,o())}),bl.setValue(t,st.some({mode:"position",anchor:n,getBounds:o}))},i=function(t,n,e,o){var r,i,u,a,c,s,l,f,d=(r=m,i=t,u=n,c=o,s=function(){return r.lazySink(i)},l="horizontal"===(a=e).type?{layouts:{onLtr:function(){return Aa()},onRtl:function(){return Fa()}}}:{},f=function(t){return 2===t.length?l:{}},Eg.sketch({dom:{tag:"div"},data:a.data,markers:a.menu.markers,highlightImmediately:a.menu.highlightImmediately,onEscape:function(){return Qs.close(i),r.onEscape.map(function(t){return t(i)}),st.some(!0)},onExecute:function(){return st.some(!0)},onOpenMenu:function(t,n){Ds.positionWithinBounds(s().getOrDie(),u,n,c())},onOpenSubmenu:function(t,n,e,o){var r=s().getOrDie();Ds.position(r,et({anchor:"submenu",item:n},f(o)),e)},onRepositionMenu:function(t,n,e){var o=s().getOrDie();Ds.positionWithinBounds(o,u,n,c()),it(e,function(t){var n=f(t.triggeringPath);Ds.position(o,et({anchor:"submenu",item:t.triggeringItem},n),t.triggeredMenu)})}}));Qs.open(t,d),bl.setValue(t,st.some({mode:"menu",menu:d}))},n=function(e){Qs.isOpen(e)&&bl.getValue(e).each(function(t){switch(t.mode){case"menu":Qs.getState(e).each(function(t){Eg.repositionMenus(t)});break;case"position":var n=m.lazySink(e).getOrDie();Ds.positionWithinBounds(n,t.anchor,e,t.getBounds())}})},e={setContent:function(t,n){Qs.setContent(t,n)},showAt:function(t,n,e){o(t,n,e,st.none())},showWithin:o,showWithinBounds:r,showMenuAt:function(t,n,e){i(t,n,e,function(){return st.none()})},showMenuWithinBounds:i,hide:function(t){Qs.isOpen(t)&&(bl.setValue(t,st.none()),Qs.close(t))},getContent:function(t){return Qs.getState(t)},reposition:n,isOpen:Qs.isOpen};return{uid:m.uid,dom:m.dom,behaviours:wl(m.inlineBehaviours,[Qs.config({isPartOf:function(t,n,e){return Ru(n,e)||(o=t,r=e,m.getRelated(o).exists(function(t){return Ru(t,r)}));var o,r},getAttachPoint:function(t){return m.lazySink(t).getOrDie()},onOpen:function(t){m.onShow(t)},onClose:function(t){m.onHide(t)}}),bl.config({store:{mode:"memory",initialValue:st.none()}}),Ya.config({channels:et(et({},ol(et({isExtraPart:t.isExtraPart},m.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),il(et(et({},m.fireRepositionEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})),{doReposition:n})))})]),eventOrder:m.eventOrder,apis:e}},apis:{showAt:function(t,n,e,o){t.showAt(n,e,o)},showWithin:function(t,n,e,o,r){t.showWithin(n,e,o,r)},showWithinBounds:function(t,n,e,o,r){t.showWithinBounds(n,e,o,r)},showMenuAt:function(t,n,e,o){t.showMenuAt(n,e,o)},showMenuWithinBounds:function(t,n,e,o,r){t.showMenuWithinBounds(n,e,o,r)},hide:function(t,n){t.hide(n)},isOpen:function(t,n){return t.isOpen(n)},getContent:function(t,n){return t.getContent(n)},setContent:function(t,n,e){t.setContent(n,e)},reposition:function(t,n){t.reposition(n)}}}),Dg=function(t){return t.x},Mg=function(t,n){return t.x+t.width/2-n.width/2},Ag=function(t,n){return t.x+t.width-n.width},Fg=function(t){return t.y},Ig=function(t,n){return t.y+t.height-n.height},Rg=function(t,n,e){return ra(Ag(t,n),Ig(t,n),e.innerSoutheast(),sa(),ga(t,{right:0,bottom:3}),"layout-inner-se")},Vg=function(t,n,e){return ra(Dg(t),Ig(t,n),e.innerSouthwest(),ca(),ga(t,{left:1,bottom:3}),"layout-inner-sw")},Hg=function(t,n,e){return ra(Ag(t,n),Fg(t),e.innerNortheast(),aa(),ga(t,{right:0,top:2}),"layout-inner-ne")},Pg=function(t,n,e){return ra(Dg(t),Fg(t),e.innerNorthwest(),ua(),ga(t,{left:1,top:2}),"layout-inner-nw")},zg=function(t,n,e){return ra(Mg(t,n),Fg(t),e.innerNorth(),la(),ga(t,{top:2}),"layout-inner-n")},Ng=function(t,n,e){return ra(Mg(t,n),Ig(t,n),e.innerSouth(),fa(),ga(t,{bottom:3}),"layout-inner-s")},Lg=tinymce.util.Tools.resolve("tinymce.util.Delay"),jg=Of({name:"Button",factory:function(t){var n=ug(t.action),e=t.dom.tag,o=function(n){return bt(t.dom,"attributes").bind(function(t){return bt(t,n)})};return{uid:t.uid,dom:t.dom,components:t.components,events:n,behaviours:kl(t.buttonBehaviours,[Xm.config({}),Rm.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==e)return{role:o("role").getOr("button")};var t=o("type").getOr("button"),n=o("role").map(function(t){return{role:t}}).getOr({});return et({type:t},n)}()},eventOrder:t.eventOrder}},configFields:[Zn("uid",undefined),zn("dom"),Zn("components",[]),Sl("buttonBehaviours",[Xm,Rm]),Yn("action"),Yn("role"),Zn("eventOrder",{})]}),Ug=function(t){var n=function e(t){return t.uid!==undefined}(t)&&xt(t,"uid")?t.uid:Yr("memento");return{get:function(t){return t.getSystem().getByUid(n).getOrDie()},getOpt:function(t){return t.getSystem().getByUid(n).toOption()},asSpec:function(){return et(et({},t),{uid:n})}}},Wg=function(t){return st.from(t()["temporary-placeholder"]).getOr("!not found!")},Gg=function(t,n){return st.from(n()[t]).getOrThunk(function(){return Wg(n)})},Xg={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},Yg=Of({name:"Notification",factory:function(n){var t,e,o=Ug({dom:{tag:"p",innerHtml:n.translationProvider(n.text)},behaviours:za([Nm.config({})])}),r=function(t){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+t+"%"}}}},i=function(t){return{dom:{tag:"div",classes:["tox-text"],innerHtml:t+"%"}}},u=Ug({dom:{tag:"div",classes:n.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[r(0)]},i(0)],behaviours:za([Nm.config({})])}),a={updateProgress:function(t,n){t.getSystem().isConnected()&&u.getOpt(t).each(function(t){Nm.set(t,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[r(n)]},i(n)])})},updateText:function(t,n){if(t.getSystem().isConnected()){var e=o.get(t);Nm.set(e,[Yi(n)])}}},c=ut([n.icon.toArray(),n.level.toArray(),n.level.bind(function(t){return st.from(Xg[t])}).toArray()]);return{uid:n.uid,dom:{tag:"div",attributes:{role:"alert"},classes:n.level.map(function(t){return["tox-notification","tox-notification--in","tox-notification--"+t]}).getOr(["tox-notification","tox-notification--in"])},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:(t=c,e=n.iconProvider,Q(t,function(t){return st.from(e()[t])}).getOrThunk(function(){return Wg(e)}))}},{dom:{tag:"div",classes:["tox-notification__body"]},components:[o.asSpec()],behaviours:za([Nm.config({})])}].concat(n.progress?[u.asSpec()]:[]).concat(n.closeButton?[jg.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:Gg("close",n.iconProvider),attributes:{"aria-label":n.translationProvider("Close")}}}],action:function(t){n.onAction(t)}})]:[]),apis:a}},configFields:[Yn("level"),zn("progress"),zn("icon"),zn("onAction"),zn("text"),zn("iconProvider"),zn("translationProvider"),re("closeButton",!0)],apis:{updateProgress:function(t,n,e){t.updateProgress(n,e)},updateText:function(t,n,e){t.updateText(n,e)}}});function qg(t,u,a){var c=u.backstage;return{open:function(t,n){var e=!t.closeButton&&t.timeout&&(0<t.timeout||t.timeout<0),o=function(){n(),Bg.hide(i)},r=Ji(Yg.sketch({text:t.text,level:F(["success","error","warning","warn","info"],t.type)?t.type:undefined,progress:!0===t.progressBar,icon:st.from(t.icon),closeButton:!e,onAction:o,iconProvider:c.shared.providers.icons,translationProvider:c.shared.providers.translate})),i=Ji(Bg.sketch(et({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:u.backstage.shared.getSink,fireDismissalEventInstead:{}},c.shared.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}})));return a.add(i),0<t.timeout&&Lg.setTimeout(function(){o()},t.timeout),{close:o,moveTo:function(t,n){Bg.showAt(i,{anchor:"makeshift",x:t,y:n},$i(r))},moveRel:function(t,n){if("banner"!==n){var e=function(t){switch(t){case"bc-bc":return Ng;case"tc-tc":return zg;case"tc-bc":return Oa;case"bc-tc":default:return _a}}(n),o={anchor:"node",root:_i(),node:st.some(le.fromDom(t)),layouts:{onRtl:function(){return[e]},onLtr:function(){return[e]}}};Bg.showAt(i,o,$i(r))}else Bg.showAt(i,u.backstage.shared.anchors.banner(),$i(r))},text:function(t){Yg.updateText(r,t)},settings:t,getEl:function(){return r.element().dom()},progressBar:{value:function(t){Yg.updateProgress(r,t)}}}},close:function(t){t.close()},reposition:function(t){var e;it(t,function(t){return t.moveTo(0,0)}),0<(e=t).length&&(K(e).each(function(t){return t.moveRel(null,"banner")}),it(e,function(t,n){0<n&&t.moveRel(e[n-1].getEl(),"bc-tc")}))},getArgs:function(t){return t.settings}}}var Kg,Jg,$g=function(e,o){var r=null;return{cancel:function(){null!==r&&(nt.clearTimeout(r),r=null)},throttle:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];null!==r&&nt.clearTimeout(r),r=nt.setTimeout(function(){e.apply(null,t),r=null},o)}}},Qg=tinymce.util.Tools.resolve("tinymce.dom.TextSeeker"),Zg=function(o,t,n,e,r){var i=Qg(o,function(t){return e=t,(n=o).isBlock(e)||F(["BR","IMG","HR","INPUT"],e.nodeName)||"false"===n.getContentEditable(e);var n,e});return st.from(i.backwards(t,n,e,r))},tp=function(e,n){return np(le.fromDom(e.selection.getNode())).getOrThunk(function(){var t=le.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',e.getDoc());return vr(t,le.fromDom(n.extractContents())),n.insertNode(t.dom()),lr(t).each(function(t){return t.dom().normalize()}),Xc(t,Gc).map(function(t){var n;e.selection.setCursorLocation(t.dom(),"img"===Cr(n=t)?1:Uc(n).fold(function(){return dr(n).length},function(t){return t.length}))}),t})},np=function(t){return Au(t,"[data-mce-autocompleter]")},ep=function(t){return t.toString().replace(/\u00A0/g," ").replace(/\uFEFF/g,"")},op=function(t){return""!==t&&-1!==" \xa0\f\n\r\t\x0B".indexOf(t)},rp=function(t,n){return t.substring(n.length)},ip=function(t,o,r,i){if(void 0===i&&(i=0),!(n=o).collapsed||3!==n.startContainer.nodeType)return st.none();var n,e=t.getParent(o.startContainer,t.isBlock)||t.getRoot();return Zg(t,o.startContainer,o.startOffset,function(t,n,e){return function(t,n,e){var o;for(o=n-1;0<=o;o--){var r=t.charAt(o);if(op(r))return st.none();if(r===e)break}return st.some(o)}(e,n,r).getOr(n)},e).bind(function(t){var n=o.cloneRange();if(n.setStart(t.container,t.offset),n.setEnd(o.endContainer,o.endOffset),n.collapsed)return st.none();var e=ep(n);return 0!==e.lastIndexOf(r)||rp(e,r).length<i?st.none():st.some({text:rp(e,r),range:n,triggerChar:r})})},up=function(o,t,r,n){return void 0===n&&(n=0),np(le.fromDom(t.startContainer)).fold(function(){return ip(o,t,r,n)},function(t){var n=o.createRng();n.selectNode(t.dom());var e=ep(n);return st.some({range:n,text:rp(e,r),triggerChar:r})})},ap=function(e,t){t.on("keypress compositionend",e.onKeypress.throttle),t.on("remove",e.onKeypress.cancel);var o=function(t,n){jo(t,ao(),{raw:n})};t.on("keydown",function(n){var t=function(){return e.getView().bind(jf.getHighlighted)};8===n.which&&e.onKeypress.throttle(n),e.isActive()&&(27===n.which&&e.cancelIfNecessary(),e.isMenuOpen()?13===n.which?(t().each(Uo),n.preventDefault()):40===n.which?(t().fold(function(){e.getView().each(jf.highlightFirst)},function(t){o(t,n)}),n.preventDefault(),n.stopImmediatePropagation()):37!==n.which&&38!==n.which&&39!==n.which||t().each(function(t){o(t,n),n.preventDefault(),n.stopImmediatePropagation()}):13!==n.which&&38!==n.which&&40!==n.which||e.cancelIfNecessary())}),t.on("NodeChange",function(t){e.isActive()&&!e.isProcessingAction()&&np(le.fromDom(t.element)).isNone()&&e.cancelIfNecessary()})},cp=tinymce.util.Tools.resolve("tinymce.util.Promise"),sp=function(t,n){return{container:t,offset:n}},lp=function(t){if(t.nodeType===nt.Node.TEXT_NODE)return sp(t,t.data.length);var n=t.childNodes;return 0<n.length?lp(n[n.length-1]):sp(t,n.length)},fp=function(t,n){var e=t.childNodes;return 0<e.length&&n<e.length?fp(e[n],0):0<e.length&&t.nodeType===nt.Node.ELEMENT_NODE&&e.length===n?lp(e[e.length-1]):sp(t,n)},dp=function(r){return function(t){var n,e,o=fp(t.startContainer,t.startOffset);return!Zg(n=r,(e=o).container,e.offset,function(t,n){return 0===n?-1:n},n.getRoot()).filter(function(t){var n=t.container.data.charAt(t.offset-1);return!op(n)}).isSome()}},mp=function(n,e){var o,r,t=e(),i=n.selection.getRng();return o=n.dom,r=i,Q(t.triggerChars,function(t){return up(o,r,t)}).bind(function(t){return gp(n,e,t)})},gp=function(n,t,e,o){void 0===o&&(o={});var r=t(),i=n.selection.getRng().startContainer.nodeValue,u=P(r.lookupByChar(e.triggerChar),function(t){return e.text.length>=t.minChars&&t.matches.getOrThunk(function(){return dp(n.dom)})(e.range,i,e.text)});if(0===u.length)return st.none();var a=cp.all(V(u,function(n){return n.fetch(e.text,n.maxResults,o).then(function(t){return{matchText:e.text,items:t,columns:n.columns,onAction:n.onAction}})}));return st.some({lookupData:a,context:e})},pp=ln([Ln("type"),Jn("text")]),hp=ln([ae("type",function(){return"autocompleteitem"}),ae("active",function(){return!1}),ae("disabled",function(){return!1}),Zn("meta",{}),Ln("value"),Jn("text"),Jn("icon")]),vp=ln([Ln("type"),Ln("ch"),ne("minChars",1),Zn("columns",1),ne("maxResults",10),$n("matches"),Un("fetch"),Un("onAction")]),bp=function(t){var n,e,o=t.ui.registry.getAll().popups,r=dt(o,function(t){return Cn("Autocompleter",vp,t).fold(function(t){throw new Error(Tn(t))},function(t){return t})}),i=(n=pt(r,function(t){return t.ch}),e={},it(n,function(t){e[t]={}}),lt(e)),u=vt(r);return{dataset:r,triggerChars:i,lookupByChar:function(n){return P(u,function(t){return t.ch===n})}}},yp=[re("disabled",!1),Jn("text"),Jn("shortcut"),hn("value","value",Gt(function(){return Nr("menuitem-value")}),Dn()),Zn("meta",{})],xp=ln([Ln("type"),ie("onSetup",function(){return Z}),ie("onAction",Z),Jn("icon")].concat(yp)),wp=ln([Ln("type"),Un("getSubmenuItems"),ie("onSetup",function(){return Z}),Jn("icon")].concat(yp)),Sp=ln([Ln("type"),Jn("icon"),re("active",!1),ie("onSetup",function(){return Z}),Un("onAction")].concat(yp)),kp=ln([Ln("type"),re("active",!1),Jn("icon")].concat(yp)),Cp=ln([Ln("type"),jn("fancytype",["inserttable","colorswatch"]),ie("onAction",Z)]),Op=function(t,o,n){var r=Yc(t.element(),"."+n);if(0<r.length){var e=j(r,function(t){var n=t.dom().getBoundingClientRect().top,e=r[0].dom().getBoundingClientRect().top;return Math.abs(n-e)>o}).getOr(r.length);return st.some({numColumns:e,numRows:Math.ceil(r.length/e)})}return st.none()},_p=function(t,n){return za([Lm(t,n)])},Tp=function(t){return _p(Nr("unnamed-events"),t)},Ep=[zn("lazySink"),zn("tooltipDom"),Zn("exclusive",!0),Zn("tooltipComponents",[]),Zn("delay",300),oe("mode","normal",["normal","follow-highlight"]),Zn("anchor",function(t){return{anchor:"hotspot",hotspot:t,layouts:{onLtr:at([_a,Oa,wa,ka,Sa,Ca]),onRtl:at([_a,Oa,wa,ka,Sa,Ca])}}}),$u("onHide"),$u("onShow")],Bp=/* */Object.freeze({__proto__:null,init:function(){var e=ce(st.none()),n=ce(st.none()),o=function(){e.get().each(function(t){nt.clearTimeout(t)})},t=at("not-implemented");return oi({getTooltip:function(){return n.get()},isShowing:function(){return n.get().isSome()},setTooltip:function(t){n.set(st.some(t))},clearTooltip:function(){n.set(st.none())},clearTimer:o,resetTimer:function(t,n){o(),e.set(st.some(nt.setTimeout(function(){t()},n)))},readState:t})}}),Dp=Nr("tooltip.exclusive"),Mp=Nr("tooltip.show"),Ap=Nr("tooltip.hide"),Fp=function(t,n,e){t.getSystem().broadcastOn([Dp],{})},Ip=/* */Object.freeze({__proto__:null,hideAllExclusive:Fp,setComponents:function(t,n,e,o){e.getTooltip().each(function(t){t.getSystem().isConnected()&&Nm.set(t,o)})}}),Rp=La({fields:Ep,name:"tooltipping",active:/* */Object.freeze({__proto__:null,events:function(o,r){var e=function(n){r.getTooltip().each(function(t){Ps(t),o.onHide(n,t),r.clearTooltip()}),r.clearTimer()};return Yo(ut([[Jo(Mp,function(t){r.resetTimer(function(){!function(n){if(!r.isShowing()){Fp(n);var t=o.lazySink(n).getOrDie(),e=n.getSystem().build({dom:o.tooltipDom,components:o.tooltipComponents,events:Yo("normal"===o.mode?[Jo(ro(),function(t){Lo(n,Mp)}),Jo(eo(),function(t){Lo(n,Ap)})]:[]),behaviours:za([Nm.config({})])});r.setTooltip(e),Rs(t,e),o.onShow(n,e),Ds.position(t,o.anchor(n),e)}}(t)},o.delay)}),Jo(Ap,function(t){r.resetTimer(function(){e(t)},o.delay)}),Jo(yo(),function(t,n){F(n.channels(),Dp)&&e(t)}),rr(function(t){e(t)})],"normal"===o.mode?[Jo(io(),function(t){Lo(t,Mp)}),Jo(vo(),function(t){Lo(t,Ap)}),Jo(ro(),function(t){Lo(t,Mp)}),Jo(eo(),function(t){Lo(t,Ap)})]:[Jo(zo(),function(t,n){Lo(t,Mp)}),Jo(No(),function(t){Lo(t,Ap)})]]))}}),state:Bp,apis:Ip}),Vp=tinymce.util.Tools.resolve("tinymce.util.I18n"),Hp="tox-menu-nav__js",Pp="tox-collection__item",zp="tox-swatch",Np={normal:Hp,color:zp},Lp="tox-collection__item--enabled",jp="tox-collection__item-label",Up="tox-collection__item-caret",Wp="tox-collection__item--active",Gp=function(t){return bt(Np,t).getOr(Hp)},Xp=tinymce.util.Tools.resolve("tinymce.Env"),Yp=function(t){return{dom:{tag:"div",classes:["tox-collection__item-icon"],innerHtml:t}}},qp=function(t){return{dom:{tag:"div",classes:[jp]},components:[Yi(Vp.translate(t))]}},Kp=function(t,n){return{dom:{tag:"div",classes:[jp]},components:[{dom:{tag:t.tag,styles:t.styles},components:[Yi(Vp.translate(n))]}]}},Jp=function(t){return{dom:{tag:"div",classes:["tox-collection__item-accessory"],innerHtml:(n=t,e=Xp.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},o=n.split("+"),r=V(o,function(t){var n=t.toLowerCase().trim();return yt(e,n)?e[n]:t}),Xp.mac?r.join(""):r.join("+"))}};var n,e,o,r},$p=function(t){return{dom:{tag:"div",classes:["tox-collection__item-checkmark"],innerHtml:Gg("checkmark",t)}}},Qp=function(t,n,e,o,r){var i=e?n.or(st.some("")).map(Yp):st.none(),u=t.checkMark,a=t.ariaLabel.map(function(t){return{attributes:{title:Vp.translate(t)}}}).getOr({});return{dom:et({tag:"div",classes:[Hp,Pp].concat(r?["tox-collection__item-icon-rtl"]:[])},a),optComponents:[i,t.htmlContent.fold(function(){return t.textContent.map(o)},function(t){return st.some({dom:{tag:"div",classes:[jp],innerHtml:t}})}),t.shortcutContent.map(Jp),u,t.caret]}},Zp=["list-num-default","list-num-lower-alpha","list-num-lower-greek","list-num-lower-roman","list-num-upper-alpha","list-num-upper-roman"],th=["list-bull-circle","list-bull-default","list-bull-square"],nh=function(t,r,n,i){void 0===i&&(i=st.none());var e,o,u,a,c,s=Vp.isRtl()&&t.iconContent.exists(function(t){return F(th,t)}),l=t.iconContent.map(function(t){return Vp.isRtl()&&F(Zp,t)?t+"-rtl":t}).map(function(t){return n=t,e=r.icons,o=i,st.from(e()[n]).or(o).getOrThunk(function(){return Wg(e)});var n,e,o}),f=st.from(t.meta).fold(function(){return qp},function(t){return yt(t,"style")?g(Kp,t.style):qp});return"color"===t.presets?(e=t.ariaLabel,o=t.value,u=r,{dom:(a=l.getOr(""),c={tag:"div",attributes:e.map(function(t){return{title:u.translate(t)}}).getOr({}),classes:["tox-swatch"]},et(et({},c),"custom"===o?{tag:"button",classes:b(c.classes,["tox-swatches__picker-btn"]),innerHtml:a}:"remove"===o?{classes:b(c.classes,["tox-swatch--remove"]),innerHtml:a}:{attributes:et(et({},c.attributes),{"data-mce-color":o}),styles:{"background-color":o}})),optComponents:[]}):Qp(t,l,n,f,s)},eh=["input","button","textarea","select"],oh=function(t,n,e){(n.disabled()?sh:lh)(t,n,e)},rh=function(t,n){return!0===n.useNative&&F(eh,Cr(t.element()))},ih=function(t){Br(t.element(),"disabled","disabled")},uh=function(t){Fr(t.element(),"disabled")},ah=function(t){Br(t.element(),"aria-disabled","true")},ch=function(t){Br(t.element(),"aria-disabled","false")},sh=function(n,t,e){t.disableClass.each(function(t){bi(n.element(),t)}),(rh(n,t)?ih:ah)(n),t.onDisabled(n)},lh=function(n,t,e){t.disableClass.each(function(t){xi(n.element(),t)}),(rh(n,t)?uh:ch)(n),t.onEnabled(n)},fh=function(t,n){return rh(t,n)?Ar(t.element(),"disabled"):"true"===Dr(t.element(),"aria-disabled")},dh=/* */Object.freeze({__proto__:null,enable:lh,disable:sh,isDisabled:fh,onLoad:oh,set:function(t,n,e,o){(o?sh:lh)(t,n,e)}}),mh=/* */Object.freeze({__proto__:null,exhibit:function(t,n){return ii({classes:n.disabled?n.disableClass.map(Y).getOr([]):[]})},events:function(e,t){return Yo([qo(xo(),function(t,n){return fh(t,e)}),Ia(e,t,oh)])}}),gh=[ie("disabled",c),Zn("useNative",!0),Yn("disableClass"),$u("onDisabled"),$u("onEnabled")],ph=La({fields:gh,name:"disabling",active:mh,apis:dh}),hh=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),vh=tinymce.util.Tools.resolve("tinymce.EditorManager"),bh=function(t){return t.getParam("height",Math.max(t.getElement().offsetHeight,200))},yh=function(t){return t.getParam("width",hh.DOM.getStyle(t.getElement(),"width"))},xh=function(t){return st.from(t.settings.min_width).filter(rt)},wh=function(t){return st.from(t.settings.min_height).filter(rt)},Sh=function(t){return st.from(t.getParam("max_width")).filter(rt)},kh=function(t){return st.from(t.getParam("max_height")).filter(rt)},Ch=function(t){return!1!==t.getParam("menubar",!0,"boolean")},Oh=function(t){var n=t.getParam("toolbar",!0),e=!0===n,o=S(n),r=C(n)&&0<n.length;return!Th(t)&&(r||o||e)},_h=function(n){var t=lt(n.settings),e=P(t,function(t){return/^toolbar([1-9])$/.test(t)}),o=V(e,function(t){return n.getParam(t,!1,"string")}),r=P(o,function(t){return"string"==typeof t});return 0<r.length?st.some(r):st.none()},Th=function(t){return _h(t).fold(function(){return 0<t.getParam("toolbar",[],"string[]").length},function(){return!0})};(Jg=Kg=Kg||{})["default"]="wrap",Jg.floating="floating",Jg.sliding="sliding",Jg.scrolling="scrolling";var Eh,Bh,Dh=function(t){return t.getParam("toolbar_mode","","string")};(Bh=Eh=Eh||{}).auto="auto",Bh.top="top",Bh.bottom="bottom";var Mh,Ah,Fh=function(t){return t.getParam("toolbar_location",Eh.auto,"string")},Ih=function(t){return Fh(t)===Eh.bottom},Rh=function(t){var n=t.getParam("fixed_toolbar_container","","string");return 0<n.length&&t.inline?Mu(_i(),n):st.none()},Vh=function(t){return t.inline&&Rh(t).isSome()},Hh=function(t){return t.inline&&!Ch(t)&&!Oh(t)&&!Th(t)},Ph=function(t){return(t.getParam("toolbar_sticky",!1,"boolean")||t.inline)&&!Vh(t)&&!Hh(t)},zh="silver.readonly",Nh=ln([Nn("readonly",In)]),Lh=function(t,n){var e=t.outerContainer.element();n&&(t.mothership.broadcastOn([Zs()],{target:e}),t.uiMothership.broadcastOn([Zs()],{target:e})),t.mothership.broadcastOn([zh],{readonly:n}),t.uiMothership.broadcastOn([zh],{readonly:n})},jh=function(t,n){t.on("init",function(){t.mode.isReadOnly()&&Lh(n,!0)}),t.on("SwitchMode",function(){return Lh(n,t.mode.isReadOnly())}),t.getParam("readonly",!1,"boolean")&&t.setMode("readonly")},Uh=function(){var t;return Ya.config({channels:((t={})[zh]={schema:Nh,onReceive:function(t,n){ph.set(t,n.readonly)}},t)})},Wh=function(t){return ph.config({disabled:t,disableClass:"tox-collection__item--state-disabled"})},Gh=function(t){return ph.config({disabled:t})},Xh=function(t){return ph.config({disabled:t,disableClass:"tox-tbtn--disabled"})},Yh=function(t){return ph.config({disabled:t,disableClass:"tox-tbtn--disabled",useNative:!1})},qh=function(t,n){var e=t.getApi(n);return function(t){t(e)}},Kh=function(e,o){return or(function(t){qh(e,t)(function(t){var n=e.onSetup(t);null!==n&&n!==undefined&&o.set(n)})})},Jh=function(n,e){return rr(function(t){return qh(n,t)(e.get())})};(Ah=Mh=Mh||{})[Ah.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",Ah[Ah.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";var $h=Mh,Qh={"alloy.execute":["disabling","alloy.base.behaviour","toggling","item-events"]},Zh=function(t){return U(t,function(t){return t.toArray()})},tv=function(t,n,e,o){var r,i,u=ce(Z);return{type:"item",dom:n.dom,components:Zh(n.optComponents),data:t.data,eventOrder:Qh,hasSubmenu:t.triggersSubmenu,itemBehaviours:za([Lm("item-events",[(r=t,i=e,ur(function(t,n){qh(r,t)(r.onAction),r.triggersSubmenu||i!==$h.CLOSE_ON_EXECUTE||(Lo(t,Co()),n.stop())})),Kh(t,u),Jh(t,u)]),Wh(function(){return t.disabled||o.isReadOnly()}),Uh(),Nm.config({})].concat(t.itemBehaviours))}},nv=function(t){return{value:t.value,meta:et({text:t.text.getOr("")},t.meta)}},ev=function(t,n){var e,o=Vp.translate(t),r=(e=o,hh.DOM.encode(e));if(0<n.length){var i=new RegExp(n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gi");return r.replace(i,function(t){return'<span class="tox-autocompleter-highlight">'+t+"</span>"})}return r},ov=at(nf(pg(),hg())),rv=Nr("cell-over"),iv=Nr("cell-execute"),uv=function(n,e,t){var o,r=function(t){return jo(t,iv,{row:n,col:e})},i=function(t,n){n.stop(),r(t)};return Ji({dom:{tag:"div",attributes:((o={role:"button"})["aria-labelledby"]=t,o)},behaviours:za([Lm("insert-table-picker-cell",[Jo(ro(),Xm.focus),Jo(xo(),r),Jo(fo(),i),Jo(So(),i)]),rg.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Xm.config({onFocus:function(t){return jo(t,rv,{row:n,col:e})}})])})},av=function(t){return{value:t}},cv=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,sv=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,lv=function(t){return cv.test(t)||sv.test(t)},fv=function(t){var n={value:t.value.replace(cv,function(t,n,e,o){return n+n+e+e+o+o})},e=sv.exec(n.value);return null===e?["FFFFFF","FF","FF","FF"]:e},dv=function(t){var n=t.toString(16);return 1===n.length?"0"+n:n},mv=function(t){var n=dv(t.red)+dv(t.green)+dv(t.blue);return av(n)},gv=Math.min,pv=Math.max,hv=Math.round,vv=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,bv=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,yv=function(t,n,e,o){return{red:t,green:n,blue:e,alpha:o}},xv=function(t){var n=parseInt(t,10);return n.toString()===t&&0<=n&&n<=255},wv=function(t){var n,e,o,r=(t.hue||0)%360,i=t.saturation/100,u=t.value/100;if(i=pv(0,gv(i,1)),u=pv(0,gv(u,1)),0===i)return n=e=o=hv(255*u),yv(n,e,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),l=u-c;switch(Math.floor(a)){case 0:n=c,e=s,o=0;break;case 1:n=s,e=c,o=0;break;case 2:n=0,e=c,o=s;break;case 3:n=0,e=s,o=c;break;case 4:n=s,e=0,o=c;break;case 5:n=c,e=0,o=s;break;default:n=e=o=0}return n=hv(255*(n+l)),e=hv(255*(e+l)),o=hv(255*(o+l)),yv(n,e,o,1)},Sv=function(t){var n=fv(t),e=parseInt(n[1],16),o=parseInt(n[2],16),r=parseInt(n[3],16);return yv(e,o,r,1)},kv=function(t,n,e,o){var r=parseInt(t,10),i=parseInt(n,10),u=parseInt(e,10),a=parseFloat(o);return yv(r,i,u,a)},Cv=function(t){return"rgba("+t.red+","+t.green+","+t.blue+","+t.alpha+")"},Ov=yv(255,0,0,1),_v=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),Tv="tinymce-custom-colors";var Ev="choiceitem",Bv=[{type:Ev,text:"Light Green",value:"#BFEDD2"},{type:Ev,text:"Light Yellow",value:"#FBEEB8"},{type:Ev,text:"Light Red",value:"#F8CAC6"},{type:Ev,text:"Light Purple",value:"#ECCAFA"},{type:Ev,text:"Light Blue",value:"#C2E0F4"},{type:Ev,text:"Green",value:"#2DC26B"},{type:Ev,text:"Yellow",value:"#F1C40F"},{type:Ev,text:"Red",value:"#E03E2D"},{type:Ev,text:"Purple",value:"#B96AD9"},{type:Ev,text:"Blue",value:"#3598DB"},{type:Ev,text:"Dark Turquoise",value:"#169179"},{type:Ev,text:"Orange",value:"#E67E23"},{type:Ev,text:"Dark Red",value:"#BA372A"},{type:Ev,text:"Dark Purple",value:"#843FA1"},{type:Ev,text:"Dark Blue",value:"#236FA1"},{type:Ev,text:"Light Gray",value:"#ECF0F1"},{type:Ev,text:"Medium Gray",value:"#CED4D9"},{type:Ev,text:"Gray",value:"#95A5A6"},{type:Ev,text:"Dark Gray",value:"#7E8C8D"},{type:Ev,text:"Navy Blue",value:"#34495E"},{type:Ev,text:"Black",value:"#000000"},{type:Ev,text:"White",value:"#ffffff"}],Dv=function zF(e){void 0===e&&(e=10);var t,n=_v.getItem(Tv),o=S(n)?JSON.parse(n):[],r=e-(t=o).length<0?t.slice(0,e):t,i=function(t){r.splice(t,1)};return{add:function(t){var n;(-1===(n=A(r,t))?st.none():st.some(n)).each(i),r.unshift(t),r.length>e&&r.pop(),_v.setItem(Tv,JSON.stringify(r))},state:function(){return r.slice(0)}}}(10),Mv=function(t){return!1!==t.getParam("custom_colors")},Av=function(t){var n=t.getParam("color_map");return n!==undefined?function(t){var n=[],u=nt.document.createElement("canvas");u.height=1,u.width=1;for(var a=u.getContext("2d"),c=function(t,n){var e=n/255;return("0"+Math.round(t*e+255*(1-e)).toString(16)).slice(-2).toUpperCase()},e=function(t){if(/^[0-9A-Fa-f]{6}$/.test(t))return"#"+t.toUpperCase();a.clearRect(0,0,u.width,u.height),a.fillStyle="#FFFFFF",a.fillStyle=t,a.fillRect(0,0,1,1);var n=a.getImageData(0,0,1,1).data,e=n[0],o=n[1],r=n[2],i=n[3];return"#"+c(e,i)+c(o,i)+c(r,i)},o=0;o<t.length;o+=2)n.push({text:t[o+1],value:e(t[o]),type:"choiceitem"});return n}(n):Bv},Fv=function(t){Dv.add(t)},Iv=function(t,n){return t.fire("ResizeContent",n)},Rv=function(i){i.addCommand("mceApplyTextcolor",function(t,n){var e,o,r;o=t,r=n,(e=i).undoManager.transact(function(){e.focus(),e.formatter.apply(o,{value:r}),e.nodeChanged()})}),i.addCommand("mceRemoveTextcolor",function(t){var n,e;e=t,(n=i).undoManager.transact(function(){n.focus(),n.formatter.remove(e,{value:null},null,!0),n.nodeChanged()})})},Vv=function(t){var n,e,o=Av(t),r=(n=o.length,Math.max(5,Math.ceil(Math.sqrt(n))));return e=r,t.getParam("color_cols",e,"number")},Hv=function(n,e,t,o){"custom"===t?Uv(n)(function(t){t.each(function(t){Fv(t),n.execCommand("mceApplyTextcolor",e,t),o(t)})},"#000000"):"remove"===t?(o(""),n.execCommand("mceRemoveTextcolor",e)):(o(t),n.execCommand("mceApplyTextcolor",e,t))},Pv=function(t,n){return t.concat(V(Dv.state(),function(t){return{type:Ev,text:t,value:t}}).concat((o={type:e="choiceitem",text:"Remove color",icon:"color-swatch-remove-color",value:"remove"},n?[o,{type:e,text:"Custom color",icon:"color-picker",value:"custom"}]:[o])));var e,o},zv=function(n,e){return function(t){t(Pv(n,e))}},Nv=function(t,n,e){var o,r;o="forecolor"===n?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color",r=e,t.setIconFill(o,r),t.setIconStroke(o,r)},Lv=function(i,e,u,t,o){i.ui.registry.addSplitButton(e,{tooltip:t,presets:"color",icon:"forecolor"===e?"text-color":"highlight-bg-color",select:function(e){var t,o,r;return st.from((o=u,(t=i).dom.getParents(t.selection.getStart(),function(t){var n;(n=t.style["forecolor"===o?"color":"background-color"])&&(r=r||n)}),r)).bind(function(t){return function(t){if("transparent"===t)return st.some(yv(0,0,0,0));var n=vv.exec(t);if(null!==n)return st.some(kv(n[1],n[2],n[3],"1"));var e=bv.exec(t);return null!==e?st.some(kv(e[1],e[2],e[3],e[4])):st.none()}(t).map(function(t){var n=mv(t).value;return Ee(e.toLowerCase(),n)})}).getOr(!1)},columns:Vv(i),fetch:zv(Av(i),Mv(i)),onAction:function(t){null!==o.get()&&Hv(i,u,o.get(),function(){})},onItemAction:function(t,n){Hv(i,u,n,function(t){var n;o.set(t),n={name:e,color:t},i.fire("TextColorChange",n)})},onSetup:function(n){null!==o.get()&&Nv(n,e,o.get());var t=function(t){t.name===e&&Nv(n,t.name,t.color)};return i.on("TextColorChange",t),function(){i.off("TextColorChange",t)}}})},jv=function(n,t,e,o){n.ui.registry.addNestedMenuItem(t,{text:o,icon:"forecolor"===t?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(t){Hv(n,e,t.value,Z)}}]}})},Uv=function(i){return function(t,n){var e,o={colorpicker:n},r=(e=t,function(t){var n=t.getData();e(st.from(n.colorpicker)),t.close()});i.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onAction:function(t,n){"hex-valid"===n.name&&(n.value?t.enable("ok"):t.disable("ok"))},onSubmit:r,onClose:function(){},onCancel:function(){t(st.none())}})}},Wv=function(t){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:"color"===t?"tox-swatches":"tox-menu",tieredMenu:"tox-tiered-menu"}},Gv=function(t){var n=Wv(t);return{backgroundMenu:n.backgroundMenu,selectedMenu:n.selectedMenu,menu:n.menu,selectedItem:n.selectedItem,item:Gp(t)}},Xv=[Sg.parts().items({})],Yv=function(t,n,e){var o=Wv(e);return{dom:{tag:"div",classes:ut([[o.tieredMenu]])},markers:Gv(e)}},qv=function(e,o){return function(t){var n=R(t,o);return V(n,function(t){return{dom:e,components:t}})}},Kv=function(t,e){var o=[],r=[];return it(t,function(t,n){e(t,n)?(0<r.length&&o.push(r),r=[],yt(t.dom,"innerHtml")&&r.push(t)):r.push(t)}),0<r.length&&o.push(r),V(o,function(t){return{dom:{tag:"div",classes:["tox-collection__group"]},components:t}})},Jv=function(n,e,t){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===n?["tox-collection--list"]:["tox-collection--grid"])},components:[Sg.parts().items({preprocess:function(t){return"auto"!==n&&1<n?qv({tag:"div",classes:["tox-collection__group"]},n)(t):Kv(t,function(t,n){return"separator"===e[n].type})}})]}},$v=function(t){return I(t,function(t){return"icon"in t&&t.icon!==undefined})},Qv=function(t){return nt.console.error(Tn(t)),nt.console.log(t),st.none()},Zv=function(t,n,e,o,r){var i,u=(i=e,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Sg.parts().items({preprocess:function(t){return Kv(t,function(t,n){return"separator"===i[n].type})}})]});return{value:t,dom:u.dom,components:u.components,items:e}},tb=function(t,n,e,o,r){var i,u,a,c,s,l;return"color"===r?{value:t,dom:(l=(i=o,{dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Sg.parts().items({preprocess:"auto"!==i?qv({tag:"div",classes:["tox-swatches__row"]},i):ct})]}]})).dom,components:l.components,items:e}:"normal"===r&&"auto"===o?{value:t,dom:(l=Jv(o,e)).dom,components:l.components,items:e}:"normal"===r&&1===o?{value:t,dom:(l=Jv(1,e)).dom,components:l.components,items:e}:"normal"===r?{value:t,dom:(l=Jv(o,e)).dom,components:l.components,items:e}:"listpreview"!==r||"auto"===o?{value:t,dom:(a=n,c=o,s=Wv(r),{tag:"div",classes:ut([[s.menu,"tox-menu-"+c+"-column"],a?[s.hasIcons]:[]])}),components:Xv,items:e}:{value:t,dom:(l=(u=o,{dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Sg.parts().items({preprocess:qv({tag:"div",classes:["tox-collection__group"]},u)})]})).dom,components:l.components,items:e}},nb=function(t,n,e,o,r,i,u,a){var c=$v(n),s=eb(n,e,o,"color"!==r?"normal":"color",i,u,a);return tb(t,c,s,o,r)},eb=function(e,o,r,i,u,a,c){return Tf(V(e,function(n){return"choiceitem"===n.type?Cn("choicemenuitem",kp,n).fold(Qv,function(t){return st.some(function(n,t,e,o,r,i,u,a){void 0===a&&(a=!0);var c=nh({presets:e,textContent:t?n.text:st.none(),htmlContent:st.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:t?n.shortcut:st.none(),checkMark:t?st.some($p(u.icons)):st.none(),caret:st.none(),value:n.value},u,a);return Ct(tv({data:nv(n),disabled:n.disabled,getApi:function(n){return{setActive:function(t){rg.set(n,t)},isActive:function(){return rg.isOn(n)},isDisabled:function(){return ph.isDisabled(n)},setDisabled:function(t){return ph.set(n,t)}}},onAction:function(t){return o(n.value)},onSetup:function(t){return t.setActive(r),function(){}},triggersSubmenu:!1,itemBehaviours:[]},c,i,u),{toggling:{toggleClass:Lp,toggleOnExecute:!1,selected:n.active}})}(t,1===r,i,o,a(n.value),u,c,$v(e)))}):st.none()}))},ob=function(t,n){var e=Gv(n);return 1===t?{mode:"menu",moveOnTab:!0}:"auto"===t?{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===n?"tox-swatches__row":"tox-collection__group")}};var rb,ib,ub={inserttable:function(o){var t=Nr("size-label"),i=function(t,n,e){for(var o=[],r=0;r<n;r++){for(var i=[],u=0;u<e;u++)i.push(uv(r,u,t));o.push(i)}return o}(t,10,10),u=Ug({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[Yi("0x0")],behaviours:za([Nm.config({})])});return{type:"widget",data:{value:Nr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ov().widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:U(i,function(t){return V(t,$i)}).concat(u.asSpec()),behaviours:za([Lm("insert-table-picker",[tr(rv,function(t,n,e){var o=e.event().row(),r=e.event().col();!function(t,n,e,o,r){for(var i=0;i<o;i++)for(var u=0;u<r;u++)rg.set(t[i][u],i<=n&&u<=e)}(i,o,r,10,10),Nm.set(u.get(t),[Yi(r+1+"x"+(o+1))])}),tr(iv,function(t,n,e){o.onAction({numRows:e.event().row()+1,numColumns:e.event().col()+1}),Lo(t,Co())})]),Rm.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:function NF(n,t){var e=Pv(t.colorinput.getColors(),t.colorinput.hasCustomColors()),o=t.colorinput.getColorCols(),r=nb(Nr("menu-value"),e,function(t){n.onAction({value:t})},o,"color",$h.CLOSE_ON_EXECUTE,function(){return!1},t.shared.providers),i=et(et({},r),{markers:Gv("color"),movement:ob(o,"color")});return{type:"widget",data:{value:Nr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ov().widget(Sg.sketch(i))]}}},ab=function(n,e,t,o,r,i,u,a){void 0===a&&(a=!0);var c,s,l=nh({presets:o,textContent:st.none(),htmlContent:t?n.text.map(function(t){return ev(t,e)}):st.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:st.none(),checkMark:st.none(),caret:st.none(),value:n.value},u.providers,a,n.icon);return tv({data:nv(n),disabled:n.disabled,getApi:function(){return{}},onAction:function(t){return r(n.value,n.meta)},onSetup:function(){return function(){}},triggersSubmenu:!1,itemBehaviours:(c=n.meta,s=u,bt(c,"tooltipWorker").map(function(e){return[Rp.config({lazySink:s.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(t){return{anchor:"submenu",item:t,overrides:{maxHeightFunction:hc}}},mode:"follow-highlight",onShow:function(n,t){e(function(t){Rp.setComponents(n,[qi({element:le.fromDom(t)})])})}})]}).getOr([]))},l,i,u.providers)},cb=function(t){var n=t.text.fold(function(){return{}},function(t){return{innerHtml:t}});return{type:"separator",dom:et({tag:"div",classes:[Pp,"tox-collection__group-heading"]},n),components:[]}},sb=function(t,n,e,o){void 0===o&&(o=!0);var r=nh({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:st.none(),ariaLabel:t.text,caret:st.none(),checkMark:st.none(),shortcutContent:t.shortcut},e,o);return tv({data:nv(t),getApi:function(n){return{isDisabled:function(){return ph.isDisabled(n)},setDisabled:function(t){return ph.set(n,t)}}},disabled:t.disabled,onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n,e)},lb=function(t,n,e,o,r){void 0===o&&(o=!0),void 0===r&&(r=!1);var i,u,a=r?(u=e.icons,{dom:{tag:"div",classes:[Up],innerHtml:Gg("chevron-down",u)}}):(i=e.icons,{dom:{tag:"div",classes:[Up],innerHtml:Gg("chevron-right",i)}}),c=nh({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:st.none(),ariaLabel:t.text,caret:st.some(a),checkMark:st.none(),shortcutContent:t.shortcut},e,o);return tv({data:nv(t),getApi:function(n){return{isDisabled:function(){return ph.isDisabled(n)},setDisabled:function(t){return ph.set(n,t)}}},disabled:t.disabled,onAction:Z,onSetup:t.onSetup,triggersSubmenu:!0,itemBehaviours:[]},c,n,e)},fb=function(t,n,e,o){void 0===o&&(o=!0);var r=nh({iconContent:t.icon,textContent:t.text,htmlContent:st.none(),ariaLabel:t.text,checkMark:st.some($p(e.icons)),caret:st.none(),shortcutContent:t.shortcut,presets:"normal",meta:t.meta},e,o);return Ct(tv({data:nv(t),disabled:t.disabled,getApi:function(n){return{setActive:function(t){rg.set(n,t)},isActive:function(){return rg.isOn(n)},isDisabled:function(){return ph.isDisabled(n)},setDisabled:function(t){return ph.set(n,t)}}},onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n,e),{toggling:{toggleClass:Lp,toggleOnExecute:!1,selected:t.active}})},db=function(n,e){return t=ub,o=n.fancytype,(Object.prototype.hasOwnProperty.call(t,o)?st.some(t[o]):st.none()).map(function(t){return t(n,e)});var t,o};(ib=rb=rb||{})[ib.ContentFocus=0]="ContentFocus",ib[ib.UiFocus=1]="UiFocus";var mb=function(t,n,e,o,r){var i=e.shared.providers,u=function(t){return r?et(et({},t),{shortcut:st.none(),icon:t.text.isSome()?st.none():t.icon}):t};switch(t.type){case"menuitem":return Cn("menuitem",xp,t).fold(Qv,function(t){return st.some(sb(u(t),n,i,o))});case"nestedmenuitem":return Cn("nestedmenuitem",wp,t).fold(Qv,function(t){return st.some(lb(u(t),n,i,o,r))});case"togglemenuitem":return Cn("togglemenuitem",Sp,t).fold(Qv,function(t){return st.some(fb(u(t),n,i,o))});case"separator":return Cn("separatormenuitem",pp,t).fold(Qv,function(t){return st.some(cb(t))});case"fancymenuitem":return Cn("fancymenuitem",Cp,t).fold(Qv,function(t){return db(u(t),e)});default:return nt.console.error("Unknown item in general menu",t),st.none()}},gb=function(t,n,e,o,r,i){var u=1===o,a=!u||$v(t);return Tf(V(t,function(t){return"separator"===t.type?Cn("Autocompleter.Separator",pp,t).fold(Qv,function(t){return st.some(cb(t))}):Cn("Autocompleter.Item",hp,t).fold(Qv,function(t){return st.some(ab(t,n,u,"normal",e,r,i,a))})}))},pb=function(t,n,e,o,r){var i=$v(n),u=Tf(V(n,function(t){var n=function(t){return mb(t,e,o,(n=t,r?!n.hasOwnProperty("text"):i),r);var n};return"nestedmenuitem"===t.type&&t.getSubmenuItems().length<=0?n(et(et({},t),{disabled:!0})):n(t)}));return(r?Zv:tb)(t,i,u,1,"normal")},hb=function(t){return Eg.singleData(t.value,t)},vb=function(d,c){var e=ce(st.none()),s=ce(!1),m=Ji(Bg.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:za([Lm("dismissAutocompleter",[Jo(Fo(),function(){return f()})])]),lazySink:c.getSink})),o=function(){return e.get().isSome()},l=function(){o()&&Bg.hide(m)},f=function(){if(o()){var t=e.get().map(function(t){return t.element});np(t.getOr(le.fromDom(d.selection.getNode()))).each(wr),l(),e.set(st.none()),s.set(!1)}},r=_t(function(){return bp(d)}),g=function(t,n,e,o){t.matchLength=n.text.length;var r,i,u,a,c,s,l,f=Q(e,function(t){return st.from(t.columns)}).getOr(1);Bg.showAt(m,{anchor:"node",root:le.fromDom(d.getBody()),node:st.from(t.element)},Sg.sketch((r=tb("autocompleter-value",!0,o,f,"normal"),i=f,u=rb.ContentFocus,a="normal",c=(u===rb.ContentFocus?cd:ad)(),s=ob(i,a),l=Gv(a),{dom:r.dom,components:r.components,items:r.items,value:r.value,markers:{selectedItem:l.selectedItem,item:l.item},movement:s,fakeFocus:u===rb.ContentFocus,focusManager:c,menuBehaviours:Tp("auto"!==i?[]:[or(function(o,t){Op(o,4,l.item).each(function(t){var n=t.numColumns,e=t.numRows;Rm.setGridSize(o,e,n)})})])}))),Bg.getContent(m).each(jf.highlightFirst)},p=function(t){var n;n=t,e.get().map(function(t){return up(d.dom,d.selection.getRng(),t.triggerChar).bind(function(t){return gp(d,r,t,n)})}).getOrThunk(function(){return mp(d,r)}).fold(f,function(a){!function(t){if(!o()){var n=tp(d,t.range);e.set(st.some({triggerChar:t.triggerChar,element:n,matchLength:t.text.length})),s.set(!1)}}(a.context),a.lookupData.then(function(u){e.get().map(function(t){var n,e,o,r=a.context;if(t.triggerChar===r.triggerChar){var i=(n=r.triggerChar,o=Q(e=u,function(t){return st.from(t.columns)}).getOr(1),U(e,function(i){var t=i.items;return gb(t,i.matchText,function(o,r){var t=d.selection.getRng();up(d.dom,t,n).fold(function(){return nt.console.error("Lost context. Cursor probably moved")},function(t){var n=t.range,e={hide:function(){f()},reload:function(t){l(),p(t)}};s.set(!0),i.onAction(e,n,o,r),s.set(!1)})},o,$h.BUBBLE_TO_SANDBOX,c)}));0<i.length?g(t,r,u,i):(10<=r.text.length-t.matchLength?f:l)()}})})})},t={onKeypress:$g(function(t){27!==t.which&&p()},50),cancelIfNecessary:f,isMenuOpen:function(){return Bg.isOpen(m)},isActive:o,isProcessingAction:s.get,getView:function(){return Bg.getContent(m)}};ap(t,d)},bb=at(!0),yb=function(t,n,e){return fu(t,n,bb,e,!1)},xb=function(t,n,e){return fu(t,n,bb,e,!0)},wb=lu,Sb=function(t,n,e){return Au(t,n,e).isSome()};function kb(e,o){var r=null;return{cancel:function(){null!==r&&(nt.clearTimeout(r),r=null)},schedule:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];r=nt.setTimeout(function(){e.apply(null,t),r=null},o)}}}var Cb=function(t){var n=t.raw();return n.touches===undefined||1!==n.touches.length?st.none():st.some(n.touches[0])},Ob=function(e){var u=ce(st.none()),o=ce(!1),r=kb(function(t){e.triggerEvent(ko(),t),o.set(!0)},400),i=Jt([{key:Je(),value:function(e){return Cb(e).each(function(t){r.cancel();var n={x:t.clientX,y:t.clientY,target:e.target()};r.schedule(e),o.set(!1),u.set(st.some(n))}),st.none()}},{key:$e(),value:function(t){return r.cancel(),Cb(t).each(function(i){u.get().each(function(t){var n,e,o,r;n=i,e=t,o=Math.abs(n.clientX-e.x),r=Math.abs(n.clientY-e.y),(5<o||5<r)&&u.set(st.none())})}),st.none()}},{key:Qe(),value:function(n){r.cancel();return u.get().filter(function(t){return Le(t.target,n.target())}).map(function(t){return o.get()?(n.prevent(),!1):e.triggerEvent(So(),n)})}}]);return{fireIfReady:function(n,t){return bt(i,t).bind(function(t){return t(n)})}}},_b=function(){return Pe().browser.isFirefox()},Tb=sn([Un("triggerEvent"),Zn("stopBackspace",!0)]),Eb=function(n,t){var e,o,r,i,u=_n("Getting GUI events settings",Tb,t),a=Ob(u),c=V(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(t){return yb(n,t,function(n){a.fireIfReady(n,t).each(function(t){t&&n.kill()}),u.triggerEvent(t,n)&&n.kill()})}),s=ce(st.none()),l=yb(n,"paste",function(n){a.fireIfReady(n,"paste").each(function(t){t&&n.kill()}),u.triggerEvent("paste",n)&&n.kill(),s.set(st.some(nt.setTimeout(function(){u.triggerEvent(bo(),n)},0)))}),f=yb(n,"keydown",function(t){var n;u.triggerEvent("keydown",t)?t.kill():!0!==u.stopBackspace||(8!==(n=t).raw().which||F(["input","textarea"],Cr(n.target()))||Sb(n.target(),'[contenteditable="true"]'))||t.prevent()}),d=(e=n,o=function(t){u.triggerEvent("focusin",t)&&t.kill()},_b()?xb(e,"focus",o):yb(e,"focusin",o)),m=ce(st.none()),g=(r=n,i=function(t){u.triggerEvent("focusout",t)&&t.kill(),m.set(st.some(nt.setTimeout(function(){u.triggerEvent(vo(),t)},0)))},_b()?xb(r,"blur",i):yb(r,"focusout",i));return{unbind:function(){it(c,function(t){t.unbind()}),f.unbind(),d.unbind(),g.unbind(),l.unbind(),s.get().each(nt.clearTimeout),m.get().each(nt.clearTimeout)}}},Bb=function(t,n){var e=bt(t,"target").map(function(t){return t()}).getOr(n);return ce(e)},Db=wt([{stopped:[]},{resume:["element"]},{complete:[]}]),Mb=function(t,o,n,e,r,i){var u,a,c,s,l=t(o,e),f=(u=n,a=r,c=ce(!1),s=ce(!1),{stop:function(){c.set(!0)},cut:function(){s.set(!0)},isStopped:c.get,isCut:s.get,event:at(u),setSource:a.set,getSource:a.get});return l.fold(function(){return i.logEventNoHandlers(o,e),Db.complete()},function(n){var e=n.descHandler;return ai(e)(f),f.isStopped()?(i.logEventStopped(o,n.element,e.purpose()),Db.stopped()):f.isCut()?(i.logEventCut(o,n.element,e.purpose()),Db.complete()):lr(n.element).fold(function(){return i.logNoParent(o,n.element,e.purpose()),Db.complete()},function(t){return i.logEventResponse(o,n.element,e.purpose()),Db.resume(t)})})},Ab=function(n,e,o,t,r,i){return Mb(n,e,o,t,r,i).fold(function(){return!0},function(t){return Ab(n,e,o,t,r,i)},function(){return!1})},Fb=function(t,n,e){var o,r,i=(o=n,r=ce(!1),{stop:function(){r.set(!0)},cut:Z,isStopped:r.get,isCut:at(!1),event:at(o),setSource:u("Cannot set source of a broadcasted event"),getSource:u("Cannot get source of a broadcasted event")});return it(t,function(t){var n=t.descHandler();ai(n)(i)}),i.isStopped()},Ib=function(t,n,e,o,r){var i=Bb(e,o);return Ab(t,n,e,o,i,r)},Rb=function(t,n){return{element:t,descHandler:n}},Vb=function(t,n){return{id:at(t),descHandler:at(n)}};function Hb(){var i={};return{registerId:function(o,r,t){ft(t,function(t,n){var e=i[n]!==undefined?i[n]:{};e[r]=ui(t,o),i[n]=e})},unregisterId:function(e){ft(i,function(t,n){t.hasOwnProperty(e)&&delete t[e]})},filterByType:function(t){return bt(i,t).map(function(t){return pt(t,function(t,n){return Vb(n,t)})}).getOr([])},find:function(t,n,e){var r=bt(i,n);return Xe(e,function(t){return e=r,Xr(o=t).fold(function(){return st.none()},function(n){return e.bind(function(t){return bt(t,n)}).map(function(t){return Rb(o,t)})});var e,o},t)}}}function Pb(){var o=Hb(),r={},i=function(o){var t=o.element();return Xr(t).fold(function(){return t="uid-",n=o.element(),e=Nr(Ur+t),Gr(n,e),e;var t,n,e},function(t){return t})},u=function(t){Xr(t.element()).each(function(t){delete r[t],o.unregisterId(t)})};return{find:function(t,n,e){return o.find(t,n,e)},filter:function(t){return o.filterByType(t)},register:function(t){var n=i(t);xt(r,n)&&function(t,n){var e=r[n];if(e!==t)throw new Error('The tagId "'+n+'" is already used by: '+Vr(e.element())+"\nCannot use it for: "+Vr(t.element())+"\nThe conflicting element is"+(Oi(e.element())?" ":" not ")+"already in the DOM");u(t)}(t,n);var e=[t];o.registerId(e,n,t.events()),r[n]=t},unregister:u,getById:function(t){return bt(r,t)}}}var zb,Nb,Lb,jb,Ub=Of({name:"Container",factory:function(t){var n=t.dom,e=n.attributes,o=y(n,["attributes"]);return{uid:t.uid,dom:et({tag:"div",attributes:et({role:"presentation"},e)},o),components:t.components,behaviours:xl(t.containerBehaviours),events:t.events,domModification:t.domModification,eventOrder:t.eventOrder}},configFields:[Zn("components",[]),yl("containerBehaviours",[]),Zn("events",{}),Zn("domModification",{}),Zn("eventOrder",{})]}),Wb=function(e){var o=function(n){return lr(e.element()).fold(function(){return!0},function(t){return Le(n,t)})},r=Pb(),s=function(t,n){return r.find(o,t,n)},t=Eb(e.element(),{triggerEvent:function(u,a){return ju(u,a.target(),function(t){return n=s,e=u,r=t,i=(o=a).target(),Ib(n,e,o,i,r);var n,e,o,r,i})}}),i={debugInfo:at("real"),triggerEvent:function(n,e,o){ju(n,e,function(t){return Ib(s,n,o,e,t)})},triggerFocus:function(a,c){Xr(a).fold(function(){Ka(a)},function(t){ju(ho(),a,function(t){var n,e,o,r,i,u;return n=s,e=ho(),o={originator:at(c),kill:Z,prevent:Z,target:at(a)},i=t,u=Bb(o,r=a),Mb(n,e,o,r,u,i),!1})})},triggerEscape:function(t,n){i.triggerEvent("keydown",t.element(),n.event())},getByUid:function(t){return g(t)},getByDom:function(t){return p(t)},build:Ji,addToGui:function(t){a(t)},removeFromGui:function(t){c(t)},addToWorld:function(t){n(t)},removeFromWorld:function(t){u(t)},broadcast:function(t){f(t)},broadcastOn:function(t,n){d(t,n)},broadcastEvent:function(t,n){m(t,n)},isConnected:at(!0)},n=function(t){t.connect(i),Tr(t.element())||(r.register(t),it(t.components(),n),i.triggerEvent(_o(),t.element(),{target:at(t.element())}))},u=function(t){Tr(t.element())||(it(t.components(),u),r.unregister(t)),t.disconnect()},a=function(t){Rs(e,t)},c=function(t){Ps(t)},l=function(e){var t=r.filter(yo());it(t,function(t){var n=t.descHandler();ai(n)(e)})},f=function(t){l({universal:at(!0),data:at(t)})},d=function(t,n){l({universal:at(!1),channels:at(t),data:at(n)})},m=function(t,n){var e=r.filter(t);return Fb(e,n)},g=function(t){return r.getById(t).fold(function(){return ot.error(new Error('Could not find component with uid: "'+t+'" in system.'))},ot.value)},p=function(t){var n=Xr(t).getOr("not found");return g(n)};return n(e),{root:at(e),element:e.element,destroy:function(){t.unbind(),xr(e.element())},add:a,remove:c,getByUid:g,getByDom:p,addToWorld:n,removeFromWorld:u,broadcast:f,broadcastOn:d,broadcastEvent:m}},Gb=Nr("form-component-change"),Xb=Nr("form-close"),Yb=Nr("form-cancel"),qb=Nr("form-action"),Kb=Nr("form-submit"),Jb=Nr("form-block"),$b=Nr("form-unblock"),Qb=Nr("form-tabchange"),Zb=Nr("form-resize"),ty=at([Zn("prefix","form-field"),yl("fieldBehaviours",[Mf,bl])]),ny=at([Jl({schema:[zn("dom")],name:"label"}),Jl({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[zn("text")],name:"aria-descriptor"}),ql({factory:{sketch:function(t){var n=qt(t,["factory"]);return t.factory.sketch(n)}},schema:[zn("factory")],name:"field"})]),ey=_f({name:"FormField",configFields:ty(),partFields:ny(),factory:function(r,t,n,e){var o=wl(r.fieldBehaviours,[Mf.config({find:function(t){return sf(t,r,"field")}}),bl.config({store:{mode:"manual",getValue:function(t){return Mf.getCurrent(t).bind(bl.getValue)},setValue:function(t,n){Mf.getCurrent(t).each(function(t){bl.setValue(t,n)})}}})]),i=Yo([or(function(t,n){var o=ff(t,r,["label","field","aria-descriptor"]);o.field().each(function(e){var n=Nr(r.prefix);o.label().each(function(t){Br(t.element(),"for",n),Br(e.element(),"id",n)}),o["aria-descriptor"]().each(function(t){var n=Nr(r.prefix);Br(t.element(),"id",n),Br(e.element(),"aria-describedby",n)})})})]),u={getField:function(t){return sf(t,r,"field")},getLabel:function(t){return sf(t,r,"label")}};return{uid:r.uid,dom:r.dom,components:t,behaviours:o,events:i,apis:u}},apis:{getField:function(t,n){return t.getField(n)},getLabel:function(t,n){return t.getLabel(n)}}}),oy=at([Yn("data"),Zn("inputAttributes",{}),Zn("inputStyles",{}),Zn("tag","input"),Zn("inputClasses",[]),$u("onSetValue"),Zn("styles",{}),Zn("eventOrder",{}),yl("inputBehaviours",[bl,Xm]),Zn("selectOnFocus",!0)]),ry=function(t){return za([Xm.config({onFocus:t.selectOnFocus?function(t){var n=t.element(),e=zi(n);n.dom().setSelectionRange(0,e.length)}:Z})])},iy=function(t){return{tag:t.tag,attributes:et({type:"text"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses}},uy=Of({name:"Input",configFields:oy(),factory:function(t,n){return{uid:t.uid,dom:iy(t),components:[],behaviours:et(et({},ry(e=t)),wl(e.inputBehaviours,[bl.config({store:et(et({mode:"manual"},e.data.map(function(t){return{initialValue:t}}).getOr({})),{getValue:function(t){return zi(t.element())},setValue:function(t,n){zi(t.element())!==n&&Ni(t.element(),n)}}),onSetValue:e.onSetValue})])),eventOrder:t.eventOrder};var e}}),ay={},cy={exports:ay};zb=undefined,Nb=ay,Lb=cy,jb=undefined,function(t){if("object"==typeof Nb&&void 0!==Lb)Lb.exports=t();else if("function"==typeof zb&&zb.amd)zb([],t);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=t()}}(function(){return function l(i,u,a){function c(n,t){if(!u[n]){if(!i[n]){var e="function"==typeof jb&&jb;if(!t&&e)return e(n,!0);if(s)return s(n,!0);var o=new Error("Cannot find module '"+n+"'");throw o.code="MODULE_NOT_FOUND",o}var r=u[n]={exports:{}};i[n][0].call(r.exports,function(t){return c(i[n][1][t]||t)},r,r.exports,l,i,u,a)}return u[n].exports}for(var s="function"==typeof jb&&jb,t=0;t<a.length;t++)c(a[t]);return c}({1:[function(t,n,e){var o,r,i=n.exports={};function u(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function c(t){if(o===setTimeout)return setTimeout(t,0);if((o===u||!o)&&setTimeout)return o=setTimeout,setTimeout(t,0);try{return o(t,0)}catch(n){try{return o.call(null,t,0)}catch(n){return o.call(this,t,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:u}catch(t){o=u}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(t){r=a}}();var s,l=[],f=!1,d=-1;function m(){f&&s&&(f=!1,s.length?l=s.concat(l):d=-1,l.length&&g())}function g(){if(!f){var t=c(m);f=!0;for(var n=l.length;n;){for(s=l,l=[];++d<n;)s&&s[d].run();d=-1,n=l.length}s=null,f=!1,function e(t){if(r===clearTimeout)return clearTimeout(t);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{return r(t)}catch(n){try{return r.call(null,t)}catch(n){return r.call(this,t)}}}(t)}}function p(t,n){this.fun=t,this.array=n}function h(){}i.nextTick=function(t){var n=new Array(arguments.length-1);if(1<arguments.length)for(var e=1;e<arguments.length;e++)n[e-1]=arguments[e];l.push(new p(t,n)),1!==l.length||f||c(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(t,f,n){(function(n){function o(){}function i(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],l(t,this)}function r(o,r){for(;3===o._state;)o=o._value;0!==o._state?(o._handled=!0,i._immediateFn(function(){var t=1===o._state?r.onFulfilled:r.onRejected;if(null!==t){var n;try{n=t(o._value)}catch(e){return void a(r.promise,e)}u(r.promise,n)}else(1===o._state?u:a)(r.promise,o._value)})):o._deferreds.push(r)}function u(t,n){try{if(n===t)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if(n instanceof i)return t._state=3,t._value=n,void c(t);if("function"==typeof e)return void l(function o(t,n){return function(){t.apply(n,arguments)}}(e,n),t)}t._state=1,t._value=n,c(t)}catch(r){a(t,r)}}function a(t,n){t._state=2,t._value=n,c(t)}function c(t){2===t._state&&0===t._deferreds.length&&i._immediateFn(function(){t._handled||i._unhandledRejectionFn(t._value)});for(var n=0,e=t._deferreds.length;n<e;n++)r(t,t._deferreds[n]);t._deferreds=null}function s(t,n,e){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof n?n:null,this.promise=e}function l(t,n){var e=!1;try{t(function(t){e||(e=!0,u(n,t))},function(t){e||(e=!0,a(n,t))})}catch(o){if(e)return;e=!0,a(n,o)}}var t,e;t=this,e=setTimeout,i.prototype["catch"]=function(t){return this.then(null,t)},i.prototype.then=function(t,n){var e=new this.constructor(o);return r(this,new s(t,n,e)),e},i.all=function(t){var c=Array.prototype.slice.call(t);return new i(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(n,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if("function"==typeof e)return void e.call(t,function(t){a(n,t)},i)}c[n]=t,0==--u&&r(c)}catch(o){i(o)}}for(var t=0;t<c.length;t++)a(t,c[t])})},i.resolve=function(n){return n&&"object"==typeof n&&n.constructor===i?n:new i(function(t){t(n)})},i.reject=function(e){return new i(function(t,n){n(e)})},i.race=function(r){return new i(function(t,n){for(var e=0,o=r.length;e<o;e++)r[e].then(t,n)})},i._immediateFn="function"==typeof n?function(t){n(t)}:function(t){e(t,0)},i._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)},i._setImmediateFn=function(t){i._immediateFn=t},i._setUnhandledRejectionFn=function(t){i._unhandledRejectionFn=t},void 0!==f&&f.exports?f.exports=i:t.Promise||(t.Promise=i)}).call(this,t("timers").setImmediate)},{timers:3}],3:[function(c,t,s){(function(t,n){var o=c("process/browser.js").nextTick,e=Function.prototype.apply,r=Array.prototype.slice,i={},u=0;function a(t,n){this._id=t,this._clearFn=n}s.setTimeout=function(){return new a(e.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new a(e.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(t){t.close()},a.prototype.unref=a.prototype.ref=function(){},a.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(t,n){clearTimeout(t._idleTimeoutId),t._idleTimeout=n},s.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},s._unrefActive=s.active=function(t){clearTimeout(t._idleTimeoutId);var n=t._idleTimeout;0<=n&&(t._idleTimeoutId=setTimeout(function(){t._onTimeout&&t._onTimeout()},n))},s.setImmediate="function"==typeof t?t:function(t){var n=u++,e=!(arguments.length<2)&&r.call(arguments,1);return i[n]=!0,o(function(){i[n]&&(e?t.apply(null,e):t.call(null),s.clearImmediate(n))}),n},s.clearImmediate="function"==typeof n?n:function(t){delete i[t]}}).call(this,c("timers").setImmediate,c("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(t,n,e){var o=t("promise-polyfill"),r="undefined"!=typeof window?window:Function("return this;")();n.exports={boltExport:r.Promise||o}},{"promise-polyfill":2}]},{},[4])(4)});var sy,ly,fy=cy.exports.boltExport,dy=function(t){var e=st.none(),n=[],o=function(t){r()?u(t):n.push(t)},r=function(){return e.isSome()},i=function(t){it(t,u)},u=function(n){e.each(function(t){nt.setTimeout(function(){n(t)},0)})};return t(function(t){e=st.some(t),i(n),n=[]}),{get:o,map:function(e){return dy(function(n){o(function(t){n(e(t))})})},isReady:r}},my={nu:dy,pure:function(n){return dy(function(t){t(n)})}},gy=function(t){nt.setTimeout(function(){throw t},0)},py=function(e){var t=function(t){e().then(t,gy)};return{map:function(t){return py(function(){return e().then(t)})},bind:function(n){return py(function(){return e().then(function(t){return n(t).toPromise()})})},anonBind:function(t){return py(function(){return e().then(function(){return t.toPromise()})})},toLazy:function(){return my.nu(t)},toCached:function(){var t=null;return py(function(){return null===t&&(t=e()),t})},toPromise:e,get:t}},hy=function(t){return py(function(){return new fy(t)})},vy=function(t){return py(function(){return fy.resolve(t)})},by=["input","textarea"],yy=function(t){var n=Cr(t);return F(by,n)},xy=function(t,n){var e=n.getRoot(t).getOr(t.element());xi(e,n.invalidClass),n.notify.each(function(n){yy(t.element())&&Br(t.element(),"aria-invalid",!1),n.getContainer(t).each(function(t){kr(t,n.validHtml)}),n.onValid(t)})},wy=function(n,t,e,o){var r=t.getRoot(n).getOr(n.element());bi(r,t.invalidClass),t.notify.each(function(t){yy(n.element())&&Br(n.element(),"aria-invalid",!0),t.getContainer(n).each(function(t){kr(t,o)}),t.onInvalid(n,o)})},Sy=function(n,t,e){return t.validator.fold(function(){return vy(ot.value(!0))},function(t){return t.validate(n)})},ky=function(n,e,t){return e.notify.each(function(t){t.onValidate(n)}),Sy(n,e).map(function(t){return n.getSystem().isConnected()?t.fold(function(t){return wy(n,e,0,t),ot.error(t)},function(t){return xy(n,e),ot.value(t)}):ot.error("No longer in system")})},Cy=/* */Object.freeze({__proto__:null,markValid:xy,markInvalid:wy,query:Sy,run:ky,isInvalid:function(t,n){var e=n.getRoot(t).getOr(t.element());return wi(e,n.invalidClass)}}),Oy=/* */Object.freeze({__proto__:null,events:function(n,t){return n.validator.map(function(t){return Yo([Jo(t.onEvent,function(t){ky(t,n).get(ct)})].concat(t.validateOnLoad?[or(function(t){ky(t,n).get(Z)})]:[]))}).getOr({})}}),_y=[zn("invalidClass"),Zn("getRoot",st.none),Qn("notify",[Zn("aria","alert"),Zn("getContainer",st.none),Zn("validHtml",""),$u("onValid"),$u("onInvalid"),$u("onValidate")]),Qn("validator",[zn("validate"),Zn("onEvent","input"),Zn("validateOnLoad",!0)])],Ty=La({fields:_y,name:"invalidating",active:Oy,apis:Cy,extra:{validation:function(e){return function(t){var n=bl.getValue(t);return vy(e(n))}}}}),Ey=/* */Object.freeze({__proto__:null,exhibit:function(t,n){return ii({attributes:Jt([{key:n.tabAttr,value:"true"}])})}}),By=[Zn("tabAttr","data-alloy-tabstop")],Dy=La({fields:By,name:"tabstopping",active:Ey}),My=function(t,n,e,o){var r=Ay(t,n,e,o);return ey.sketch(r)},Ay=function(t,n,e,o){return{dom:Fy(e),components:t.toArray().concat([n]),fieldBehaviours:za(o)}},Fy=function(t){return{tag:"div",classes:["tox-form__group"].concat(t)}},Iy=function(t,n){return ey.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}})},Ry=/* */Object.freeze({__proto__:null,getCoupled:function(t,n,e,o){return e.getOrCreate(t,n,o)}}),Vy=[Nn("others",kn(ot.value,Dn()))],Hy=La({fields:Vy,name:"coupling",apis:Ry,state:/* */Object.freeze({__proto__:null,init:function(){var i={},t=at({});return oi({readState:t,getOrCreate:function(e,o,r){var t=lt(o.others);if(t)return bt(i,r).getOrThunk(function(){var t=bt(o.others,r).getOrDie("No information found for coupled component: "+r)(e),n=e.getSystem().build(t);return i[r]=n});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(t,null,2))}})}})}),Py=at("sink"),zy=at(Jl({name:Py(),overrides:at({dom:{tag:"div"},behaviours:za([Ds.config({useFixed:i})]),events:Yo([nr(ao()),nr(to()),nr(fo())])})}));(ly=sy=sy||{})[ly.HighlightFirst=0]="HighlightFirst",ly[ly.HighlightNone=1]="HighlightNone";var Ny=function(t,n){var e=t.getHotspot(n).getOr(n),o=t.getAnchorOverrides();return t.layouts.fold(function(){return{anchor:"hotspot",hotspot:e,overrides:o}},function(t){return{anchor:"hotspot",hotspot:e,overrides:o,layouts:t}})},Ly=function(t,n,e,o,r,i,u){var a,c,s,l,f,d,m,g,p,h,v=Ny(t,e);return(c=v,l=o,f=r,d=u,m=n,g=s=e,p=(0,(a=t).fetch)(g).map(m),h=Gy(s,a),p.map(function(t){return t.bind(function(t){return st.from(Eg.sketch(et(et({},f.menu()),{uid:Yr(""),data:t,highlightImmediately:d===sy.HighlightFirst,onOpenMenu:function(t,n){var e=h().getOrDie();Ds.position(e,c,n),Qs.decloak(l)},onOpenSubmenu:function(t,n,e){var o=h().getOrDie();Ds.position(o,{anchor:"submenu",item:n},e),Qs.decloak(l)},onRepositionMenu:function(t,n,e){var o=h().getOrDie();Ds.position(o,c,n),it(e,function(t){Ds.position(o,{anchor:"submenu",item:t.triggeringItem},t.triggeredMenu)})},onEscape:function(){return Xm.focus(s),Qs.close(l),st.some(!0)}})))})})).map(function(t){return t.fold(function(){Qs.isOpen(o)&&Qs.close(o)},function(t){Qs.cloak(o),Qs.open(o,t),i(o)}),o})},jy=function(t,n,e,o,r,i,u){return Qs.close(o),vy(o)},Uy=function(t,n,e,o,r,i){var u=Hy.getCoupled(e,"sandbox");return(Qs.isOpen(u)?jy:Ly)(t,n,e,u,o,r,i)},Wy=function(t,n,e){var o,r,i=Mf.getCurrent(n).getOr(n),u=cu(t.element());e?Di(i.element(),"min-width",u+"px"):(o=i.element(),r=u,au.set(o,r))},Gy=function(n,t){return n.getSystem().getByUid(t.uid+"-"+Py()).map(function(t){return function(){return ot.value(t)}}).getOrThunk(function(){return t.lazySink.fold(function(){return function(){return ot.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(t){return function(){return t(n)}})})},Xy=function(t){Qs.getState(t).each(function(t){Eg.repositionMenus(t)})},Yy=function(o,r,i){var u=Fu(),t=Gy(r,o);return{dom:{tag:"div",classes:o.sandboxClasses,attributes:{id:u.id,role:"listbox"}},behaviours:kl(o.sandboxBehaviours,[bl.config({store:{mode:"memory",initialValue:r}}),Qs.config({onOpen:function(t,n){var e=Ny(o,r);u.link(r.element()),o.matchWidth&&Wy(e.hotspot,n,o.useMinWidth),o.onOpen(e,t,n),i!==undefined&&i.onOpen!==undefined&&i.onOpen(t,n)},onClose:function(t,n){u.unlink(r.element()),i!==undefined&&i.onClose!==undefined&&i.onClose(t,n)},isPartOf:function(t,n,e){return Ru(n,e)||Ru(r,e)},getAttachPoint:function(){return t().getOrDie()}}),Mf.config({find:function(t){return Qs.getState(t).bind(function(t){return Mf.getCurrent(t)})}}),Ya.config({channels:et(et({},ol({isExtraPart:c})),il({doReposition:Xy}))})])}},qy=function(t){var n=Hy.getCoupled(t,"sandbox");Xy(n)},Ky=function(){return[Zn("sandboxClasses",[]),Sl("sandboxBehaviours",[Mf,Ya,Qs,bl])]},Jy=at([zn("dom"),zn("fetch"),$u("onOpen"),Qu("onExecute"),Zn("getHotspot",st.some),Zn("getAnchorOverrides",at({})),Tc(),yl("dropdownBehaviours",[rg,Hy,Rm,Xm]),zn("toggleClass"),Zn("eventOrder",{}),Yn("lazySink"),Zn("matchWidth",!1),Zn("useMinWidth",!1),Yn("role")].concat(Ky())),$y=at([Kl({schema:[qu()],name:"menu",defaults:function(t){return{onExecute:t.onExecute}}}),zy()]),Qy=_f({name:"Dropdown",configFields:Jy(),partFields:$y(),factory:function(n,t,e,o){var r,i,u=function(t){Qs.getState(t).each(function(t){Eg.highlightPrimary(t)})},a={expand:function(t){rg.isOn(t)||Uy(n,function(t){return t},t,o,Z,sy.HighlightNone).get(Z)},open:function(t){rg.isOn(t)||Uy(n,function(t){return t},t,o,Z,sy.HighlightFirst).get(Z)},isOpen:rg.isOn,close:function(t){rg.isOn(t)&&Uy(n,function(t){return t},t,o,Z,sy.HighlightFirst).get(Z)},repositionMenus:function(t){rg.isOn(t)&&qy(t)}},c=function(t,n){return Uo(t),st.some(!0)};return{uid:n.uid,dom:n.dom,components:t,behaviours:wl(n.dropdownBehaviours,[rg.config({toggleClass:n.toggleClass,aria:{mode:"expanded"}}),Hy.config({others:{sandbox:function(t){return Yy(n,t,{onOpen:function(){rg.on(t)},onClose:function(){rg.off(t)}})}}}),Rm.config({mode:"special",onSpace:c,onEnter:c,onDown:function(t,n){if(Qy.isOpen(t)){var e=Hy.getCoupled(t,"sandbox");u(e)}else Qy.open(t);return st.some(!0)},onEscape:function(t,n){return Qy.isOpen(t)?(Qy.close(t),st.some(!0)):st.none()}}),Xm.config({})]),events:ug(st.some(function(t){Uy(n,function(t){return t},t,o,u,sy.HighlightFirst).get(Z)})),eventOrder:et(et({},n.eventOrder),((r={})[xo()]=["disabling","toggling","alloy.base.behaviour"],r)),apis:a,domModification:{attributes:et(et({"aria-haspopup":"true"},n.role.fold(function(){return{}},function(t){return{role:t}})),"button"===n.dom.tag?{type:(i="type",bt(n.dom,"attributes").bind(function(t){return bt(t,i)}).getOr("button"))}:{})}}},apis:{open:function(t,n){return t.open(n)},expand:function(t,n){return t.expand(n)},close:function(t,n){return t.close(n)},isOpen:function(t,n){return t.isOpen(n)},repositionMenus:function(t,n){return t.repositionMenus(n)}}}),Zy=La({fields:[],name:"unselecting",active:/* */Object.freeze({__proto__:null,events:function(){return Yo([qo(go(),at(!0))])},exhibit:function(){return ii({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),tx=Nr("color-input-change"),nx=Nr("color-swatch-change"),ex=Nr("color-picker-cancel"),ox=function(e,n,o){var r,i,t=ey.parts().field({factory:uy,inputClasses:["tox-textfield"],onSetValue:function(t){return Ty.run(t).get(function(){})},inputBehaviours:za([ph.config({disabled:n.providers.isReadOnly}),Uh(),Dy.config({}),Ty.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(t){return lr(t.element())},notify:{onValid:function(t){var n=bl.getValue(t);jo(t,tx,{color:n})}},validator:{validateOnLoad:!1,validate:function(t){var n=bl.getValue(t);if(0===n.length)return vy(ot.value(!0));var e=le.fromTag("span");Di(e,"background-color",n);var o=Ri(e,"background-color").fold(function(){return ot.error("blah")},function(t){return ot.value(n)});return vy(o)}}})]),selectOnFocus:!1}),u=e.label.map(function(t){return Iy(t,n.providers)}),a=function(t,n){jo(t,nx,{value:n})},c=Ug((r={dom:{tag:"span",attributes:{"aria-label":n.providers.translate("Color swatch")}},layouts:{onRtl:function(){return[Sa,wa,_a]},onLtr:function(){return[wa,Sa,_a]}},components:[],fetch:zv(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:function(t,e){c.getOpt(t).each(function(n){"custom"===e?o.colorPicker(function(t){t.fold(function(){return Lo(n,ex)},function(t){a(n,t),Fv(t)})},"#ffffff"):a(n,"remove"===e?"":e)})}},i=n,Qy.sketch({dom:r.dom,components:r.components,toggleClass:"mce-active",dropdownBehaviours:za([Gh(i.providers.isReadOnly),Uh(),Zy.config({}),Dy.config({})]),layouts:r.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:i.getSink,fetch:function(n){return hy(function(t){return r.fetch(t)}).map(function(t){return st.from(hb(Ct(nb(Nr("menu-value"),t,function(t){r.onItemAction(n,t)},r.columns,r.presets,$h.CLOSE_ON_EXECUTE,function(){return!1},i.providers),{movement:ob(r.columns,r.presets)})))})},parts:{menu:Yv(0,0,r.presets)}})));return ey.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:u.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[t,c.asSpec()]}]),fieldBehaviours:za([Lm("form-field-events",[Jo(tx,function(t,n){c.getOpt(t).each(function(t){Di(t.element(),"background-color",n.event().color())}),jo(t,Gb,{name:e.name})}),Jo(nx,function(n,e){ey.getField(n).each(function(t){bl.setValue(t,e.event().value()),Mf.getCurrent(n).each(Xm.focus)})}),Jo(ex,function(n,t){ey.getField(n).each(function(t){Mf.getCurrent(n).each(Xm.focus)})})])])})},rx=function(t,n,e){return{hue:t,saturation:n,value:e}},ix=Nr("rgb-hex-update"),ux=Nr("slider-update"),ax=Nr("palette-update"),cx=Jl({schema:[zn("dom")],name:"label"}),sx=function(n){return Jl({name:n+"-edge",overrides:function(t){return t.model.manager.edgeActions[n].fold(function(){return{}},function(o){return{events:Yo([$o(Je(),function(t,n,e){return o(t,e)},[t]),$o(to(),function(t,n,e){return o(t,e)},[t]),$o(no(),function(t,n,e){e.mouseIsDown.get()&&o(t,e)},[t])])}})}})},lx=sx("top-left"),fx=sx("top"),dx=sx("top-right"),mx=sx("right"),gx=sx("bottom-right"),px=sx("bottom"),hx=sx("bottom-left"),vx=[cx,sx("left"),mx,fx,px,lx,dx,hx,gx,ql({name:"thumb",defaults:at({dom:{styles:{position:"absolute"}}}),overrides:function(t){return{events:Yo([Zo(Je(),t,"spectrum"),Zo($e(),t,"spectrum"),Zo(Qe(),t,"spectrum"),Zo(to(),t,"spectrum"),Zo(no(),t,"spectrum"),Zo(oo(),t,"spectrum")])}}}),ql({schema:[ae("mouseIsDown",function(){return ce(!1)})],name:"spectrum",overrides:function(e){var o=e.model.manager,r=function(n,t){return o.getValueFromEvent(t).map(function(t){return o.setValueFrom(n,e,t)})};return{behaviours:za([Rm.config({mode:"special",onLeft:function(t){return o.onLeft(t,e)},onRight:function(t){return o.onRight(t,e)},onUp:function(t){return o.onUp(t,e)},onDown:function(t){return o.onDown(t,e)}}),Xm.config({})]),events:Yo([Jo(Je(),r),Jo($e(),r),Jo(to(),r),Jo(no(),function(t,n){e.mouseIsDown.get()&&r(t,n)})])}}})],bx=at("slider.change.value"),yx=function(t){var n=t.event().raw();if(-1!==n.type.indexOf("touch")){return n.touches!==undefined&&1===n.touches.length?st.some(n.touches[0]).map(function(t){return ou(t.clientX,t.clientY)}):st.none()}return n.clientX!==undefined?st.some(n).map(function(t){return ou(t.clientX,t.clientY)}):st.none()},xx=function(t){return t.model.minX},wx=function(t){return t.model.minY},Sx=function(t){return t.model.minX-1},kx=function(t){return t.model.minY-1},Cx=function(t){return t.model.maxX},Ox=function(t){return t.model.maxY},_x=function(t){return t.model.maxX+1},Tx=function(t){return t.model.maxY+1},Ex=function(t,n,e){return n(t)-e(t)},Bx=function(t){return Ex(t,Cx,xx)},Dx=function(t){return Ex(t,Ox,wx)},Mx=function(t){return Bx(t)/2},Ax=function(t){return Dx(t)/2},Fx=function(t){return t.stepSize},Ix=function(t){return t.snapToGrid},Rx=function(t){return t.snapStart},Vx=function(t){return t.rounded},Hx=function(t,n){return t[n+"-edge"]!==undefined},Px=function(t){return Hx(t,"left")},zx=function(t){return Hx(t,"right")},Nx=function(t){return Hx(t,"top")},Lx=function(t){return Hx(t,"bottom")},jx=function(t){return t.model.value.get()},Ux=function(t){return{x:at(t)}},Wx=function(t){return{y:at(t)}},Gx=function(t,n){return{x:at(t),y:at(n)}},Xx=function(t,n){jo(t,bx(),{value:n})},Yx=function(t,n,e,o){return t<n?t:e<t?e:t===n?n-1:Math.max(n,t-o)},qx=function(t,n,e,o){return e<t?t:t<n?n:t===e?e+1:Math.min(e,t+o)},Kx=function(t,n,e){return Math.max(n,Math.min(e,t))},Jx=function(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.step,u=t.snap,a=t.snapStart,c=t.rounded,s=t.hasMinEdge,l=t.hasMaxEdge,f=t.minBound,d=t.maxBound,m=t.screenRange,g=s?n-1:n,p=l?e+1:e;if(r<f)return g;if(d<r)return p;var h,v,b,y,x,w,S,k=(x=r,w=f,S=d,Math.min(S,Math.max(x,w))-w),C=Kx(k/m*o+n,g,p);return u&&n<=C&&C<=e?(h=C,v=n,b=e,y=i,a.fold(function(){var t=h-v,n=Math.round(t/y)*y;return Kx(v+n,v-1,b+1)},function(t){var n=(h-t)%y,e=Math.round(n/y),o=Math.floor((h-t)/y),r=Math.floor((b-t)/y),i=t+Math.min(r,o+e)*y;return Math.max(t,i)})):c?Math.round(C):C},$x=function(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.hasMinEdge,u=t.hasMaxEdge,a=t.maxBound,c=t.maxOffset,s=t.centerMinEdge,l=t.centerMaxEdge;return r<n?i?0:s:e<r?u?a:l:(r-n)/o*c},Qx="left",Zx=function(t){return t.element().dom().getBoundingClientRect()},tw=function(t,n){return t[n]},nw=function(t){var n=Zx(t);return tw(n,Qx)},ew=function(t){var n=Zx(t);return tw(n,"right")},ow=function(t){var n=Zx(t);return tw(n,"top")},rw=function(t){var n=Zx(t);return tw(n,"bottom")},iw=function(t){var n=Zx(t);return tw(n,"width")},uw=function(t){var n=Zx(t);return tw(n,"height")},aw=function(t,n,e){return(t+n)/2-e},cw=function(t,n){var e=Zx(t),o=Zx(n),r=tw(e,Qx),i=tw(e,"right"),u=tw(o,Qx);return aw(r,i,u)},sw=function(t,n){var e=Zx(t),o=Zx(n),r=tw(e,"top"),i=tw(e,"bottom"),u=tw(o,"top");return aw(r,i,u)},lw=function(t,n){jo(t,bx(),{value:n})},fw=function(t){return{x:at(t)}},dw=function(t,n,e){var o={min:xx(n),max:Cx(n),range:Bx(n),value:e,step:Fx(n),snap:Ix(n),snapStart:Rx(n),rounded:Vx(n),hasMinEdge:Px(n),hasMaxEdge:zx(n),minBound:nw(t),maxBound:ew(t),screenRange:iw(t)};return Jx(o)},mw=function(i){return function(t,n){return e=t,r=(0<i?qx:Yx)(jx(o=n).x(),xx(o),Cx(o),Fx(o)),lw(e,fw(r)),st.some(r).map(function(){return!0});var e,o,r}},gw=function(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=(a=i,c=e,s=o,l=r,f=iw(u=n),d=s.bind(function(t){return st.some(cw(t,u))}).getOr(0),m=l.bind(function(t){return st.some(cw(t,u))}).getOr(f),g={min:xx(a),max:Cx(a),range:Bx(a),value:c,hasMinEdge:Px(a),hasMaxEdge:zx(a),minBound:nw(u),minOffset:0,maxBound:ew(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m},$x(g));return nw(n)-nw(t)+p},pw=mw(-1),hw=mw(1),vw=st.none,bw=st.none,yw={"top-left":st.none(),top:st.none(),"top-right":st.none(),right:st.some(function(t,n){Xx(t,Ux(_x(n)))}),"bottom-right":st.none(),bottom:st.none(),"bottom-left":st.none(),left:st.some(function(t,n){Xx(t,Ux(Sx(n)))})},xw=/* */Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=dw(t,n,e),r=fw(o);return lw(t,r),o},setToMin:function(t,n){var e=xx(n);lw(t,fw(e))},setToMax:function(t,n){var e=Cx(n);lw(t,fw(e))},findValueOfOffset:dw,getValueFromEvent:function(t){return yx(t).map(function(t){return t.left()})},findPositionOfValue:gw,setPositionFromValue:function(t,n,e,o){var r=jx(e),i=gw(t,o.getSpectrum(t),r.x(),o.getLeftEdge(t),o.getRightEdge(t),e),u=cu(n.element())/2;Di(n.element(),"left",i-u+"px")},onLeft:pw,onRight:hw,onUp:vw,onDown:bw,edgeActions:yw}),ww=function(t,n){jo(t,bx(),{value:n})},Sw=function(t){return{y:at(t)}},kw=function(t,n,e){var o={min:wx(n),max:Ox(n),range:Dx(n),value:e,step:Fx(n),snap:Ix(n),snapStart:Rx(n),rounded:Vx(n),hasMinEdge:Nx(n),hasMaxEdge:Lx(n),minBound:ow(t),maxBound:rw(t),screenRange:uw(t)};return Jx(o)},Cw=function(i){return function(t,n){return e=t,r=(0<i?qx:Yx)(jx(o=n).y(),wx(o),Ox(o),Fx(o)),ww(e,Sw(r)),st.some(r).map(function(){return!0});var e,o,r}},Ow=function(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=(a=i,c=e,s=o,l=r,f=uw(u=n),d=s.bind(function(t){return st.some(sw(t,u))}).getOr(0),m=l.bind(function(t){return st.some(sw(t,u))}).getOr(f),g={min:wx(a),max:Ox(a),range:Dx(a),value:c,hasMinEdge:Nx(a),hasMaxEdge:Lx(a),minBound:ow(u),minOffset:0,maxBound:rw(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m},$x(g));return ow(n)-ow(t)+p},_w=st.none,Tw=st.none,Ew=Cw(-1),Bw=Cw(1),Dw={"top-left":st.none(),top:st.some(function(t,n){Xx(t,Wx(kx(n)))}),"top-right":st.none(),right:st.none(),"bottom-right":st.none(),bottom:st.some(function(t,n){Xx(t,Wx(Tx(n)))}),"bottom-left":st.none(),left:st.none()},Mw=/* */Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=kw(t,n,e),r=Sw(o);return ww(t,r),o},setToMin:function(t,n){var e=wx(n);ww(t,Sw(e))},setToMax:function(t,n){var e=Ox(n);ww(t,Sw(e))},findValueOfOffset:kw,getValueFromEvent:function(t){return yx(t).map(function(t){return t.top()})},findPositionOfValue:Ow,setPositionFromValue:function(t,n,e,o){var r=jx(e),i=Ow(t,o.getSpectrum(t),r.y(),o.getTopEdge(t),o.getBottomEdge(t),e),u=tu(n.element())/2;Di(n.element(),"top",i-u+"px")},onLeft:_w,onRight:Tw,onUp:Ew,onDown:Bw,edgeActions:Dw}),Aw=function(t,n){jo(t,bx(),{value:n})},Fw=function(t,n){return{x:at(t),y:at(n)}},Iw=function(c,s){return function(t,n){return o=t,r=n,i=0<c?qx:Yx,u=(e=s)?jx(r).x():i(jx(r).x(),xx(r),Cx(r),Fx(r)),a=e?i(jx(r).y(),wx(r),Ox(r),Fx(r)):jx(r).y(),Aw(o,Fw(u,a)),st.some(u).map(function(){return!0});var e,o,r,i,u,a}},Rw=Iw(-1,!1),Vw=Iw(1,!1),Hw=Iw(-1,!0),Pw=Iw(1,!0),zw={"top-left":st.some(function(t,n){Xx(t,Gx(Sx(n),kx(n)))}),top:st.some(function(t,n){Xx(t,Gx(Mx(n),kx(n)))}),"top-right":st.some(function(t,n){Xx(t,Gx(_x(n),kx(n)))}),right:st.some(function(t,n){Xx(t,Gx(_x(n),Ax(n)))}),"bottom-right":st.some(function(t,n){Xx(t,Gx(_x(n),Tx(n)))}),bottom:st.some(function(t,n){Xx(t,Gx(Mx(n),Tx(n)))}),"bottom-left":st.some(function(t,n){Xx(t,Gx(Sx(n),Tx(n)))}),left:st.some(function(t,n){Xx(t,Gx(Sx(n),Ax(n)))})},Nw=/* */Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=dw(t,n,e.left()),r=kw(t,n,e.top()),i=Fw(o,r);return Aw(t,i),i},setToMin:function(t,n){var e=xx(n),o=wx(n);Aw(t,Fw(e,o))},setToMax:function(t,n){var e=Cx(n),o=Ox(n);Aw(t,Fw(e,o))},getValueFromEvent:function(t){return yx(t)},setPositionFromValue:function(t,n,e,o){var r=jx(e),i=gw(t,o.getSpectrum(t),r.x(),o.getLeftEdge(t),o.getRightEdge(t),e),u=Ow(t,o.getSpectrum(t),r.y(),o.getTopEdge(t),o.getBottomEdge(t),e),a=cu(n.element())/2,c=tu(n.element())/2;Di(n.element(),"left",i-a+"px"),Di(n.element(),"top",u-c+"px")},onLeft:Rw,onRight:Vw,onUp:Hw,onDown:Pw,edgeActions:zw}),Lw=_f({name:"Slider",configFields:[Zn("stepSize",1),Zn("onChange",Z),Zn("onChoose",Z),Zn("onInit",Z),Zn("onDragStart",Z),Zn("onDragEnd",Z),Zn("snapToGrid",!1),Zn("rounded",!0),Yn("snapStart"),Nn("model",Bn("mode",{x:[Zn("minX",0),Zn("maxX",100),ae("value",function(t){return ce(t.mode.minX)}),zn("getInitialValue"),na("manager",xw)],y:[Zn("minY",0),Zn("maxY",100),ae("value",function(t){return ce(t.mode.minY)}),zn("getInitialValue"),na("manager",Mw)],xy:[Zn("minX",0),Zn("maxX",100),Zn("minY",0),Zn("maxY",100),ae("value",function(t){return ce({x:at(t.mode.minX),y:at(t.mode.minY)})}),zn("getInitialValue"),na("manager",Nw)]})),yl("sliderBehaviours",[Rm,bl]),ae("mouseIsDown",function(){return ce(!1)})],partFields:vx,factory:function(i,t,n,e){var o,u=function(t){return lf(t,i,"thumb")},a=function(t){return lf(t,i,"spectrum")},r=function(t){return sf(t,i,"left-edge")},c=function(t){return sf(t,i,"right-edge")},s=function(t){return sf(t,i,"top-edge")},l=function(t){return sf(t,i,"bottom-edge")},f=i.model,d=f.manager,m=function(t,n){d.setPositionFromValue(t,n,i,{getLeftEdge:r,getRightEdge:c,getTopEdge:s,getBottomEdge:l,getSpectrum:a})},g=function(t,n){f.value.set(n);var e=u(t);return m(t,e),i.onChange(t,e,n),st.some(!0)},p=function(e){var t=i.mouseIsDown.get();i.mouseIsDown.set(!1),t&&sf(e,i,"thumb").each(function(t){var n=f.value.get();i.onChoose(e,t,n)})},h=function(t,n){n.stop(),i.mouseIsDown.set(!0),i.onDragStart(t,u(t))},v=function(t,n){n.stop(),i.onDragEnd(t,u(t)),p(t)};return{uid:i.uid,dom:i.dom,components:t,behaviours:wl(i.sliderBehaviours,[Rm.config({mode:"special",focusIn:function(t){return sf(t,i,"spectrum").map(Rm.focusIn).map(at(!0))}}),bl.config({store:{mode:"manual",getValue:function(t){return f.value.get()}}}),Ya.config({channels:((o={})[nl()]={onReceive:p},o)})]),events:Yo([Jo(bx(),function(t,n){g(t,n.event().value())}),or(function(t,n){var e=f.getInitialValue();f.value.set(e);var o=u(t);m(t,o);var r=a(t);i.onInit(t,o,r,f.value.get())}),Jo(Je(),h),Jo(Qe(),v),Jo(to(),h),Jo(oo(),v)]),apis:{resetToMin:function(t){d.setToMin(t,i)},resetToMax:function(t){d.setToMax(t,i)},changeValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(t,n){t.resetToMin(n)},resetToMax:function(t,n){t.resetToMax(n)},refresh:function(t,n){t.refresh(n)}}}),jw=[yl("formBehaviours",[bl])],Uw=function(t){return"<alloy.field."+t+">"},Ww=function(o,t){return{uid:o.uid,dom:o.dom,components:t,behaviours:wl(o.formBehaviours,[bl.config({store:{mode:"manual",getValue:function(t){var n=df(t,o);return dt(n,function(t,r){return t().bind(function(t){var n,e,o=Mf.getCurrent(t);return n=o,e=new Error("Cannot find a current component to extract the value from for form part '"+r+"': "+Vr(t.element())),n.fold(function(){return ot.error(e)},ot.value)}).map(bl.getValue)})},setValue:function(e,t){ft(t,function(n,t){sf(e,o,t).each(function(t){Mf.getCurrent(t).each(function(t){bl.setValue(t,n)})})})}}})]),apis:{getField:function(t,n){return sf(t,o,n).bind(Mf.getCurrent)}}}},Gw={getField:ni(function(t,n,e){return t.getField(n,e)}),sketch:function(t){var e,n=(e=[],{field:function(t,n){return e.push(t),of("form",Uw(t),n)},record:function(){return e}}),o=t(n),r=n.record(),i=V(r,function(t){return ql({name:t,pname:Uw(t)})});return yf("form",jw,i,Ww,o)}},Xw=Nr("valid-input"),Yw=Nr("invalid-input"),qw=Nr("validating-input"),Kw="colorcustom.rgb.",Jw=function(d,m,g,p){var h=function(t,n,e,o,r){var i,u,a=d(Kw+"range"),c=[ey.parts().label({dom:{tag:"label",innerHtml:e,attributes:{"aria-label":o}}}),ey.parts().field({data:r,factory:uy,inputAttributes:et({type:"text"},"hex"===n?{"aria-live":"polite"}:{}),inputClasses:[m("textfield")],inputBehaviours:za([(i=n,u=t,Ty.config({invalidClass:m("invalid"),notify:{onValidate:function(t){jo(t,qw,{type:i})},onValid:function(t){jo(t,Xw,{type:i,value:bl.getValue(t)})},onInvalid:function(t){jo(t,Yw,{type:i,value:bl.getValue(t)})}},validator:{validate:function(t){var n=bl.getValue(t),e=u(n)?ot.value(!0):ot.error(d("aria.input.invalid"));return vy(e)},validateOnLoad:!1}})),Dy.config({})]),onSetValue:function(t){Ty.isInvalid(t)&&Ty.run(t).get(Z)}})],s="hex"!==n?[ey.parts()["aria-descriptor"]({text:a})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:c.concat(s)}},v=function(t,n){var e=n.red,o=n.green,r=n.blue;bl.setValue(t,{red:e,green:o,blue:r})},b=Ug({dom:{tag:"div",classes:[m("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),y=function(t,n){b.getOpt(t).each(function(t){Di(t.element(),"background-color","#"+n.value)})};return Of({factory:function(){var e={red:ce(st.some(255)),green:ce(st.some(255)),blue:ce(st.some(255)),hex:ce(st.some("ffffff"))},o=function(t){return e[t].get()},i=function(t,n){e[t].set(n)},r=function(t){var n=t.red,e=t.green,o=t.blue;i("red",st.some(n)),i("green",st.some(e)),i("blue",st.some(o))},n=function(t,n){var e=n.event();"hex"!==e.type()?i(e.type(),st.none()):p(t)},u=function(r,t,n){var e=parseInt(n,10);i(t,st.some(e)),o("red").bind(function(e){return o("green").bind(function(n){return o("blue").map(function(t){return yv(e,n,t,1)})})}).each(function(t){var n,e,o=(n=r,e=mv(t),Gw.getField(n,"hex").each(function(t){Xm.isFocused(t)||bl.setValue(n,{hex:e.value})}),e);y(r,o)})},a=function(t,n){var e=n.event();"hex"===e.type()?function(t,n){g(t);var e=av(n);i("hex",st.some(n));var o=Sv(e);v(t,o),r(o),jo(t,ix,{hex:e}),y(t,e)}(t,e.value()):u(t,e.type(),e.value())},t=function(t){return{label:d(Kw+t+".label"),description:d(Kw+t+".description")}},c=t("red"),s=t("green"),l=t("blue"),f=t("hex");return Ct(Gw.sketch(function(t){return{dom:{tag:"form",classes:[m("rgb-form")],attributes:{"aria-label":d("aria.color.picker")}},components:[t.field("red",ey.sketch(h(xv,"red",c.label,c.description,255))),t.field("green",ey.sketch(h(xv,"green",s.label,s.description,255))),t.field("blue",ey.sketch(h(xv,"blue",l.label,l.description,255))),t.field("hex",ey.sketch(h(lv,"hex",f.label,f.description,"ffffff"))),b.asSpec()],formBehaviours:za([Ty.config({invalidClass:m("form-invalid")}),Lm("rgb-form-events",[Jo(Xw,a),Jo(Yw,n),Jo(qw,n)])])}}),{apis:{updateHex:function(t,n){var e,o;bl.setValue(t,{hex:n.value}),e=t,o=Sv(n),v(e,o),r(o),y(t,n)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(t,n,e){t.updateHex(n,e)}},extraApis:{}})},$w=function(t,o){var r=Lw.parts().spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[o("sv-palette-spectrum")]}}),i=Lw.parts().thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette-thumb")],innerHtml:"<div class="+o("sv-palette-inner-thumb")+' role="presentation"></div>'}}),u=function(t,n){var e=t.width,o=t.height,r=t.getContext("2d");if(null!==r){r.fillStyle=n,r.fillRect(0,0,e,o);var i=r.createLinearGradient(0,0,e,0);i.addColorStop(0,"rgba(255,255,255,1)"),i.addColorStop(1,"rgba(255,255,255,0)"),r.fillStyle=i,r.fillRect(0,0,e,o);var u=r.createLinearGradient(0,0,0,o);u.addColorStop(0,"rgba(0,0,0,0)"),u.addColorStop(1,"rgba(0,0,0,1)"),r.fillStyle=u,r.fillRect(0,0,e,o)}};return Of({factory:function(t){var n=at({x:at(0),y:at(0)}),e=za([Mf.config({find:st.some}),Xm.config({})]);return Lw.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette")]},model:{mode:"xy",getInitialValue:n},rounded:!1,components:[r,i],onChange:function(t,n,e){jo(t,ax,{value:e})},onInit:function(t,n,e,o){u(e.element().dom(),Cv(Ov))},sliderBehaviours:e})},name:"SaturationBrightnessPalette",configFields:[],apis:{setRgba:function(t,n,e){var o,r;o=e,r=n.components()[0].element().dom(),u(r,Cv(o))}},extraApis:{}})},Qw=function(f,d){return Of({name:"ColourPicker",configFields:[zn("dom"),Zn("onValidHex",Z),Zn("onInvalidHex",Z)],factory:function(t){var a,v,n,e,o,r=Jw(f,d,t.onValidHex,t.onInvalidHex),i=$w(0,d),b={paletteRgba:ce(Ov)},u=Ug(i.sketch({})),c=Ug(r.sketch({})),s=function(t,e){u.getOpt(t).each(function(t){var n=Sv(e);b.paletteRgba.set(n),i.setRgba(t,n)})},l=function(t,n){c.getOpt(t).each(function(t){r.updateHex(t,n)})},y=function(n,e,t){it(t,function(t){t(n,e)})};return{uid:t.uid,dom:t.dom,components:[u.asSpec(),(n=d,e=Lw.parts().spectrum({dom:{tag:"div",classes:[n("hue-slider-spectrum")],attributes:{role:"presentation"}}}),o=Lw.parts().thumb({dom:{tag:"div",classes:[n("hue-slider-thumb")],attributes:{role:"presentation"}}}),Lw.sketch({dom:{tag:"div",classes:[n("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:at({y:at(0)})},components:[e,o],sliderBehaviours:za([Xm.config({})]),onChange:function(t,n,e){jo(t,ux,{value:e})}})),c.asSpec()],behaviours:za([Lm("colour-picker-events",[Jo(ax,(v=[l],function(t,n){var e,o,r,i,u,a,c,s,l,f=n.event().value(),d=b.paletteRgba.get(),m=(i=r=0,u=(e=d).red/255,a=e.green/255,c=e.blue/255,s=Math.min(u,Math.min(a,c)),l=Math.max(u,Math.max(a,c)),s===l?rx(0,0,100*(i=s)):(r=60*((r=u===s?3:c===s?1:5)-(u===s?a-c:c===s?u-a:c-u)/(l-s)),o=(l-s)/l,i=l,rx(Math.round(r),Math.round(100*o),Math.round(100*i)))),g=rx(m.hue,f.x(),100-f.y()),p=wv(g),h=mv(p);y(t,h,v)})),Jo(ux,(a=[s,l],function(t,n){var e,o,r,i=n.event().value(),u=(e=i.y(),o=rx((100-e)/100*360,100,100),r=wv(o),mv(r));y(t,u,a)}))]),Mf.config({find:function(t){return c.getOpt(t)}}),Rm.config({mode:"acyclic"})])}}})},Zw=function(){return Mf.config({find:st.some})},tS=function(t){return Mf.config({find:t.getOpt})},nS=function(t){return Mf.config({find:function(n){return mr(n.element(),t).bind(function(t){return n.getSystem().getByDom(t).toOption()})}})},eS={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},oS=function(t){return eS[t]},rS=tinymce.util.Tools.resolve("tinymce.Resource"),iS=ln([Zn("preprocess",ct),Zn("postprocess",ct)]),uS=function(t,n,e){return bl.config(Ct({store:{mode:"manual",getValue:n,setValue:e}},t.map(function(t){return{store:{initialValue:t}}}).getOr({})))},aS=function(t,n,e){return uS(t,function(t){return n(t.element())},function(t,n){return e(t.element(),n)})},cS=function(r,t){var i=_n("RepresentingConfigs.memento processors",iS,t);return bl.config({store:{mode:"manual",getValue:function(t){var n=r.get(t),e=bl.getValue(n);return i.postprocess(e)},setValue:function(t,n){var e=i.preprocess(n),o=r.get(t);bl.setValue(o,e)}}})},sS=uS,lS=function(t){return aS(t,Sr,kr)},fS=function(t){return bl.config({store:{mode:"memory",initialValue:t}})},dS=function(r,n){var e=function(t,n){n.stop()},o=function(t){return function(n,e){it(t,function(t){t(n,e)})}},i=function(t,n){if(!ph.isDisabled(t)){var e=n.event().raw();a(t,e.dataTransfer.files)}},u=function(t,n){var e=n.event().raw().target.files;a(t,e)},a=function(t,n){var e,o;bl.setValue(t,(e=n,o=new RegExp("("+".jpg,.jpeg,.png,.gif".split(/\s*,\s*/).join("|")+")$","i"),P($(e),function(t){return o.test(t.name)}))),jo(t,Gb,{name:r.name})},c=Ug({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:za([Lm("input-file-events",[nr(fo()),nr(So())])])}),t=r.label.map(function(t){return Iy(t,n)}),s=ey.parts().field({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:za([fS([]),Zw(),ph.config({}),rg.config({toggleClass:"dragenter",toggleOnExecute:!1}),Lm("dropzone-events",[Jo("dragenter",o([e,rg.toggle])),Jo("dragleave",o([e,rg.toggle])),Jo("dragover",e),Jo("drop",o([e,i])),Jo(lo(),u)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:n.translate("Drop an image here")}},jg.sketch({dom:{tag:"button",innerHtml:n.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(t){c.get(t).element().dom().click()},buttonBehaviours:za([Dy.config({}),Gh(n.isReadOnly),Uh()])})]}]}}}});return My(t,s,["tox-form__group--stretched"],[])},mS=Nr("alloy-fake-before-tabstop"),gS=Nr("alloy-fake-after-tabstop"),pS=function(t){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:t},behaviours:za([Xm.config({ignore:!0}),Dy.config({})])}},hS=function(t){return{dom:{tag:"div",classes:["tox-navobj"]},components:[pS([mS]),t,pS([gS])],behaviours:za([nS(1)])}},vS=function(t,n){jo(t,ao(),{raw:{which:9,shiftKey:n}})},bS=function(t,n){var e=n.element();wi(e,mS)?vS(t,!0):wi(e,gS)&&vS(t,!1)},yS=function(t){return Sb(t,["."+mS,"."+gS].join(","),at(!1))},xS=!(Pe().browser.isIE()||Pe().browser.isEdge()),wS=function(t,n){var o,r,e=xS&&t.sandboxed,i=et(et({},t.label.map(function(t){return{title:t}}).getOr({})),e?{sandbox:"allow-scripts allow-same-origin"}:{}),u=(o=e,r=ce(""),{getValue:function(t){return r.get()},setValue:function(t,n){if(o)Br(t.element(),"srcdoc",n);else{Br(t.element(),"src","javascript:''");var e=t.element().dom().contentWindow.document;e.open(),e.write(n),e.close()}r.set(n)}}),a=t.label.map(function(t){return Iy(t,n)}),c=ey.parts().field({factory:{sketch:function(t){return hS({uid:t.uid,dom:{tag:"iframe",attributes:i},behaviours:za([Dy.config({}),Xm.config({}),sS(st.none(),u.getValue,u.setValue)])})}}});return My(a,c,["tox-form__group--stretched"],[])};function SS(t,n){return OS(nt.document.createElement("canvas"),t,n)}function kS(t){var n=SS(t.width,t.height);return CS(n).drawImage(t,0,0),n}function CS(t){return t.getContext("2d")}function OS(t,n,e){return t.width=n,t.height=e,t}function _S(t){return t.naturalWidth||t.width}function TS(t){return t.naturalHeight||t.height}var ES,BS,DS,MS=window.Promise?window.Promise:(BS=(ES=function(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],PS(t,AS(IS,this),AS(RS,this))}).immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(t){nt.setTimeout(t,1)},DS=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},ES.prototype["catch"]=function(t){return this.then(null,t)},ES.prototype.then=function(e,o){var r=this;return new ES(function(t,n){FS.call(r,new HS(e,o,t,n))})},ES.all=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var c=Array.prototype.slice.call(1===t.length&&DS(t[0])?t[0]:t);return new ES(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(n,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if("function"==typeof e)return void e.call(t,function(t){a(n,t)},i)}c[n]=t,0==--u&&r(c)}catch(o){i(o)}}for(var t=0;t<c.length;t++)a(t,c[t])})},ES.resolve=function(n){return n&&"object"==typeof n&&n.constructor===ES?n:new ES(function(t){t(n)})},ES.reject=function(e){return new ES(function(t,n){n(e)})},ES.race=function(r){return new ES(function(t,n){for(var e=0,o=r;e<o.length;e++)o[e].then(t,n)})},ES);function AS(t,n){return function(){return t.apply(n,arguments)}}function FS(o){var r=this;null!==this._state?BS(function(){var t=r._state?o.onFulfilled:o.onRejected;if(null!==t){var n;try{n=t(r._value)}catch(e){return void o.reject(e)}o.resolve(n)}else(r._state?o.resolve:o.reject)(r._value)}):this._deferreds.push(o)}function IS(t){try{if(t===this)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if("function"==typeof n)return void PS(AS(n,t),AS(IS,this),AS(RS,this))}this._state=!0,this._value=t,VS.call(this)}catch(e){RS.call(this,e)}}function RS(t){this._state=!1,this._value=t,VS.call(this)}function VS(){for(var t=0,n=this._deferreds;t<n.length;t++){var e=n[t];FS.call(this,e)}this._deferreds=[]}function HS(t,n,e,o){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof n?n:null,this.resolve=e,this.reject=o}function PS(t,n,e){var o=!1;try{t(function(t){o||(o=!0,n(t))},function(t){o||(o=!0,e(t))})}catch(r){if(o)return;o=!0,e(r)}}function zS(e){return new MS(function(t,n){(function p(t){var n=t.split(","),e=/data:([^;]+)/.exec(n[0]);if(!e)return st.none();for(var o=e[1],r=n[1],i=nt.atob(r),u=i.length,a=Math.ceil(u/1024),c=new Array(a),s=0;s<a;++s){for(var l=1024*s,f=Math.min(1024+l,u),d=new Array(f-l),m=l,g=0;m<f;++g,++m)d[g]=i[m].charCodeAt(0);c[s]=new Uint8Array(d)}return st.some(new nt.Blob(c,{type:o}))})(e).fold(function(){n("uri is not base64: "+e)},t)})}function NS(t,o,r){return o=o||"image/png",nt.HTMLCanvasElement.prototype.toBlob?new MS(function(n,e){t.toBlob(function(t){t?n(t):e()},o,r)}):zS(t.toDataURL(o,r))}function LS(t){return function n(a){return new MS(function(t,n){var e=nt.URL.createObjectURL(a),o=new nt.Image,r=function(){o.removeEventListener("load",i),o.removeEventListener("error",u)};function i(){r(),t(o)}function u(){r(),n("Unable to load data of type "+a.type+": "+e)}o.addEventListener("load",i),o.addEventListener("error",u),o.src=e,o.complete&&i()})}(t).then(function(t){!function e(t){nt.URL.revokeObjectURL(t.src)}(t);var n=SS(_S(t),TS(t));return CS(n).drawImage(t,0,0),n})}function jS(t,n,e){var o=n.type;function r(n,e){return t.then(function(t){return function o(t,n,e){return n=n||"image/png",t.toDataURL(n,e)}(t,n,e)})}return{getType:at(o),toBlob:function i(){return MS.resolve(n)},toDataURL:at(e),toBase64:function u(){return e.split(",")[1]},toAdjustedBlob:function a(n,e){return t.then(function(t){return NS(t,n,e)})},toAdjustedDataURL:r,toAdjustedBase64:function c(t,n){return r(t,n).then(function(t){return t.split(",")[1]})},toCanvas:function s(){return t.then(kS)}}}function US(n){return function t(e){return new MS(function(t){var n=new nt.FileReader;n.onloadend=function(){t(n.result)},n.readAsDataURL(e)})}(n).then(function(t){return jS(LS(n),n,t)})}function WS(n,t){return NS(n,t).then(function(t){return jS(MS.resolve(n),t,n.toDataURL())})}function GS(t,n,e){var o="string"==typeof t?parseFloat(t):t;return e<o?o=e:o<n&&(o=n),o}function XS(){return[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1]}var YS=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10];function qS(t,n){for(var e,o=[],r=new Array(25),i=0;i<5;i++){for(var u=0;u<5;u++)o[u]=n[u+5*i];for(u=0;u<5;u++){for(var a=e=0;a<5;a++)e+=t[u+5*a]*o[a];r[u+5*i]=e}}return r}function KS(n,e){return n.toCanvas().then(function(t){return function i(t,n,e){var o=CS(t);var r=function E(t,n){for(var e,o,r,i,u=t.data,a=n[0],c=n[1],s=n[2],l=n[3],f=n[4],d=n[5],m=n[6],g=n[7],p=n[8],h=n[9],v=n[10],b=n[11],y=n[12],x=n[13],w=n[14],S=n[15],k=n[16],C=n[17],O=n[18],_=n[19],T=0;T<u.length;T+=4)e=u[T],o=u[T+1],r=u[T+2],i=u[T+3],u[T]=e*a+o*c+r*s+i*l+f,u[T+1]=e*d+o*m+r*g+i*p+h,u[T+2]=e*v+o*b+r*y+i*x+w,u[T+3]=e*S+o*k+r*C+i*O+_;return t}(o.getImageData(0,0,t.width,t.height),e);return o.putImageData(r,0,0),WS(t,n)}(t,n.getType(),e)})}function JS(n,e){return n.toCanvas().then(function(t){return function u(t,n,e){var o=CS(t);var r=o.getImageData(0,0,t.width,t.height),i=o.getImageData(0,0,t.width,t.height);return i=function w(t,n,e){function o(t,n,e){return e<t?t=e:t<n&&(t=n),t}for(var r=Math.round(Math.sqrt(e.length)),i=Math.floor(r/2),u=t.data,a=n.data,c=t.width,s=t.height,l=0;l<s;l++)for(var f=0;f<c;f++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(f+h-i,0,c-1),b=4*(o(l+p-i,0,s-1)*c+v),y=e[p*r+h];d+=u[b]*y,m+=u[1+b]*y,g+=u[2+b]*y}var x=4*(l*c+f);a[x]=o(d,0,255),a[1+x]=o(m,0,255),a[2+x]=o(g,0,255)}return n}(r,i,e),o.putImageData(i,0,0),WS(t,n)}(t,n.getType(),e)})}function $S(e){return function(t,n){return KS(t,e(XS(),n))}}var QS=function LF(n){return function(t){return KS(t,n)}}([-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1]),ZS=$S(function jF(t,n){return qS(t,[1,0,0,0,n=GS(255*n,-255,255),0,1,0,0,n,0,0,1,0,n,0,0,0,1,0,0,0,0,0,1])}),tk=$S(function UF(t,n){var e;return n=GS(n,-1,1),qS(t,[(e=(n*=100)<0?127+n/100*127:127*(e=0===(e=n%1)?YS[n]:YS[Math.floor(n)]*(1-e)+YS[Math.floor(n)+1]*e)+127)/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}),nk=function(t,n,e,o){return KS(t,function r(t,n,e,o){return qS(t,[n=GS(n,0,2),0,0,0,0,0,e=GS(e,0,2),0,0,0,0,0,o=GS(o,0,2),0,0,0,0,0,1,0,0,0,0,0,1])}(XS(),n,e,o))},ek=function WF(n){return function(t){return JS(t,n)}}([0,-1,0,-1,5,-1,0,-1,0]),ok=function GF(c){var o=function(t,n,e){var o=CS(t),r=new Array(256);for(var i=0;i<r.length;i++)r[i]=c(i,e);var u=function a(t,n){for(var e=t.data,o=0;o<e.length;o+=4)e[o]=n[e[o]],e[o+1]=n[e[o+1]],e[o+2]=n[e[o+2]];return t}(o.getImageData(0,0,t.width,t.height),r);return o.putImageData(u,0,0),WS(t,n)};return function(n,e){return n.toCanvas().then(function(t){return o(t,n.getType(),e)})}}(function(t,n){return 255*Math.pow(t/255,1-n)});function rk(t,n,e){var o=_S(t),r=TS(t),i=n/o,u=e/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c=function s(u,a,c){return new MS(function(t){var n=_S(u),e=TS(u),o=Math.floor(n*a),r=Math.floor(e*c),i=SS(o,r);CS(i).drawImage(u,0,0,n,e,0,0,o,r),t(i)})}(t,i,u);return a?c.then(function(t){return rk(t,n,e)}):c}function ik(n,e){return n.toCanvas().then(function(t){return function a(t,n,e){var o=SS(t.width,t.height),r=CS(o),i=0,u=0;90!==(e=e<0?360+e:e)&&270!==e||OS(o,o.height,o.width);90!==e&&180!==e||(i=o.width);270!==e&&180!==e||(u=o.height);return r.translate(i,u),r.rotate(e*Math.PI/180),r.drawImage(t,0,0),WS(o,n)}(t,n.getType(),e)})}function uk(n,e){return n.toCanvas().then(function(t){return function i(t,n,e){var o=SS(t.width,t.height),r=CS(o);"v"===e?(r.scale(1,-1),r.drawImage(t,0,-o.height)):(r.scale(-1,1),r.drawImage(t,-o.width,0));return WS(o,n)}(t,n.getType(),e)})}function ak(n,e,o,r,i){return n.toCanvas().then(function(t){return function a(t,n,e,o,r,i){var u=SS(r,i);return CS(u).drawImage(t,-e,-o),WS(u,n)}(t,n.getType(),e,o,r,i)})}var ck=function(t){return QS(t)},sk=function(t){return ek(t)},lk=function(t,n){return ok(t,n)},fk=function(t,n){return ZS(t,n)},dk=function(t,n){return tk(t,n)},mk=function(t,n){return uk(t,n)},gk=function(t,n,e){return function r(n,e,o){return n.toCanvas().then(function(t){return rk(t,e,o).then(function(t){return WS(t,n.getType())})})}(t,n,e)},pk=function(t,n){return ik(t,n)},hk=function(t,n){return et({dom:{tag:"span",innerHtml:t,classes:["tox-icon","tox-tbtn__icon-wrap"]}},n)},vk=function(t,n){return hk(Gg(t,n),{})},bk=function(t,n){return hk(Gg(t,n),{behaviours:za([Nm.config({})])})},yk=function(t,n,e){return{dom:{tag:"span",innerHtml:e.translate(t),classes:[n+"__select-label"]},behaviours:za([Nm.config({})])}},xk=Nr("toolbar.button.execute"),wk={"alloy.execute":["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},Sk=Nr("update-menu-text"),kk=Nr("update-menu-icon"),Ck=function(t,n,o){var e=ce(Z),r=t.text.map(function(t){return Ug(yk(t,n,o.providers))}),i=t.icon.map(function(t){return Ug(bk(t,o.providers.icons))}),u=function(t,n){var e=bl.getValue(t);return Xm.focus(e),jo(e,"keydown",{raw:n.event().raw()}),Qy.close(e),st.some(!0)},a=t.role.fold(function(){return{}},function(t){return{role:t}}),c=t.tooltip.fold(function(){return{}},function(t){var n=o.providers.translate(t);return{title:n,"aria-label":n}});return Ug(Qy.sketch(et(et({},a),{dom:{tag:"button",classes:[n,n+"--select"].concat(V(t.classes,function(t){return n+"--"+t})),attributes:et({},c)},components:Zh([i.map(function(t){return t.asSpec()}),r.map(function(t){return t.asSpec()}),st.some({dom:{tag:"div",classes:[n+"__select-chevron"],innerHtml:Gg("chevron-down",o.providers.icons)}})]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:za(b(t.dropdownBehaviours,[Gh(function(){return t.disabled||o.providers.isReadOnly()}),Uh(),Zy.config({}),Nm.config({}),Lm("dropdown-events",[Kh(t,e),Jh(t,e)]),Lm("menubutton-update-display-text",[Jo(Sk,function(n,e){r.bind(function(t){return t.getOpt(n)}).each(function(t){Nm.set(t,[Yi(o.providers.translate(e.event().text()))])})}),Jo(kk,function(n,e){i.bind(function(t){return t.getOpt(n)}).each(function(t){Nm.set(t,[bk(e.event().icon(),o.providers.icons)])})})])])),eventOrder:Ct(wk,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:za([Rm.config({mode:"special",onLeft:u,onRight:u})]),lazySink:o.getSink,toggleClass:n+"--active",parts:{menu:Yv(0,t.columns,t.presets)},fetch:function(){return hy(t.fetch)}}))).asSpec()},Ok=function(t){return"separator"===t.type},_k={type:"separator"},Tk=function(t,e){var n=N(t,function(t,n){return S(n)?""===n?t:"|"===n?0<t.length&&!Ok(t[t.length-1])?t.concat([_k]):t:yt(e,n.toLowerCase())?t.concat([e[n.toLowerCase()]]):t:t.concat([n])},[]);return 0<n.length&&Ok(n[n.length-1])&&n.pop(),n},Ek=function(t,n){return yt(t,"getSubmenuItems")?(o=n,r=(e=t).getSubmenuItems(),i=Bk(r,o),{item:e,menus:Ct(i.menus,Kt(e.value,i.items)),expansions:Ct(i.expansions,Kt(e.value,e.value))}):{item:t,menus:{},expansions:{}};var e,o,r,i},Bk=function(t,r){var n=Tk(S(t)?t.split(" "):t,r);return z(n,function(t,n){var e=function(t){if(Ok(t))return t;var n=bt(t,"value").getOrThunk(function(){return Nr("generated-menu-item")});return Ct({value:n},t)}(n),o=Ek(e,r);return{menus:Ct(t.menus,o.menus),items:[o.item].concat(t.items),expansions:Ct(t.expansions,o.expansions)}},{menus:{},expansions:{},items:[]})},Dk=function(t,e,o,n){var r=Nr("primary-menu"),i=Bk(t,o.shared.providers.menuItems());if(0===i.items.length)return st.none();var u=pb(r,i.items,e,o,n),a=dt(i.menus,function(t,n){return pb(n,t,e,o,!1)}),c=Ct(a,Kt(r,u));return st.from(Eg.tieredData(r,c,i.expansions))},Mk=function(e){return{isDisabled:function(){return ph.isDisabled(e)},setDisabled:function(t){return ph.set(e,t)},setActive:function(t){var n=e.element();t?(bi(n,"tox-tbtn--enabled"),Br(n,"aria-pressed",!0)):(xi(n,"tox-tbtn--enabled"),Fr(n,"aria-pressed"))},isActive:function(){return wi(e.element(),"tox-tbtn--enabled")}}},Ak=function(t,n,e,o){return Ck({text:t.text,icon:t.icon,tooltip:t.tooltip,role:o,fetch:function(n){t.fetch(function(t){n(Dk(t,$h.CLOSE_ON_EXECUTE,e,!1))})},onSetup:t.onSetup,getApi:Mk,columns:1,presets:"normal",classes:[],dropdownBehaviours:[Dy.config({})]},n,e.shared)},Fk=function(n,r,i){return function(t){t(V(n,function(t){var n,e,o=t.text.fold(function(){return{}},function(t){return{text:t}});return et(et({type:t.type,active:!1},o),{onAction:function(t){var n=!t.isActive();t.setActive(n),e.storage.set(n),i.shared.getSink().each(function(t){r().getOpt(t).each(function(t){Ka(t.element()),jo(t,qb,{name:e.name,value:e.storage.get()})})})},onSetup:(n=e=t,function(t){t.setActive(n.storage.get())})})}))}},Ik=function(t,n,e,o,r,i){void 0===e&&(e=[]);var u=n.fold(function(){return{}},function(t){return{action:t}}),a=et({buttonBehaviours:za([Gh(function(){return t.disabled||i.isReadOnly()}),Uh(),Dy.config({}),Lm("button press",[Ko("click"),Ko("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},u),c=Ct(a,{dom:o});return Ct(c,{components:r})},Rk=function(t,n,e,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:t.tooltip.map(function(t){return{"aria-label":e.translate(t),title:e.translate(t)}}).getOr({})},i=t.icon.map(function(t){return vk(t,e.icons)}),u=Zh([i]);return Ik(t,n,o,r,u,e)},Vk=function(t,n,e,o){void 0===o&&(o=[]);var r=Rk(t,st.some(n),e,o);return jg.sketch(r)},Hk=function(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=e.translate(t.text),u=t.icon?t.icon.map(function(t){return vk(t,e.icons)}):st.none(),a=u.isSome()?Zh([u]):[],c=u.isSome()?{}:{innerHtml:i},s=b(t.primary||t.borderless?["tox-button"]:["tox-button","tox-button--secondary"],u.isSome()?["tox-button--icon"]:[],t.borderless?["tox-button--naked"]:[],r),l=et(et({tag:"button",classes:s},c),{attributes:{title:i}});return Ik(t,n,o,l,a,e)},Pk=function(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=Hk(t,st.some(n),e,o,r);return jg.sketch(i)},zk=function(n,e){return function(t){"custom"===e?jo(t,qb,{name:n,value:{}}):"submit"===e?Lo(t,Kb):"cancel"===e?Lo(t,Yb):nt.console.error("Unknown button type: ",e)}},Nk=function(n,t,e){if("menu"===t){var o=n,r=et(et({},n),{onSetup:function(t){return t.setDisabled(n.disabled),Z},fetch:Fk(o.items,function(){return i},e)}),i=Ug(Ak(r,"tox-tbtn",e,st.none()));return i.asSpec()}if("custom"===(c=t)||"cancel"===c||"submit"===c){var u=zk(n.name,t),a=et(et({},n),{borderless:!1});return Pk(a,u,e.shared.providers,[])}var c;nt.console.error("Unknown footer button type: ",t)},Lk=function(t,n){var e,o,r=zk(t.name,"custom");return e=st.none(),o=ey.parts().field(et({factory:jg},Hk(t,st.some(r),n,[fS(""),Zw()]))),My(e,o,[],[])},jk=at([Zn("field1Name","field1"),Zn("field2Name","field2"),Zu("onLockedChange"),Ku(["lockClass"]),Zn("locked",!1),Sl("coupledFieldBehaviours",[Mf,bl])]),Uk=function(t,n){return ql({factory:ey,name:t,overrides:function(o){return{fieldBehaviours:za([Lm("coupled-input-behaviour",[Jo(so(),function(e){sf(e,o,n).bind(Mf.getCurrent).each(function(n){sf(e,o,"lock").each(function(t){rg.isOn(t)&&o.onLockedChange(e,n,t)})})})])])}}})},Wk=at([Uk("field1","field2"),Uk("field2","field1"),ql({factory:jg,schema:[zn("dom")],name:"lock",overrides:function(t){return{buttonBehaviours:za([rg.config({selected:t.locked,toggleClass:t.markers.lockClass,aria:{mode:"pressed"}})])}}})]),Gk=_f({name:"FormCoupledInputs",configFields:jk(),partFields:Wk(),factory:function(o,t,n,e){return{uid:o.uid,dom:o.dom,components:t,behaviours:kl(o.coupledFieldBehaviours,[Mf.config({find:st.some}),bl.config({store:{mode:"manual",getValue:function(t){var n,e=gf(t,o,["field1","field2"]);return(n={})[o.field1Name]=bl.getValue(e.field1()),n[o.field2Name]=bl.getValue(e.field2()),n},setValue:function(t,n){var e=gf(t,o,["field1","field2"]);xt(n,o.field1Name)&&bl.setValue(e.field1(),n[o.field1Name]),xt(n,o.field2Name)&&bl.setValue(e.field2(),n[o.field2Name])}}})]),apis:{getField1:function(t){return sf(t,o,"field1")},getField2:function(t){return sf(t,o,"field2")},getLock:function(t){return sf(t,o,"lock")}}}},apis:{getField1:function(t,n){return t.getField1(n)},getField2:function(t,n){return t.getField2(n)},getLock:function(t,n){return t.getLock(n)}}}),Xk=function(t){var n=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(t);if(null===n)return ot.error(t);var e=parseFloat(n[1]),o=n[2];return ot.value({value:e,unit:o})},Yk=function(t,n){var e={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,"in":1},o=function(t){return Object.prototype.hasOwnProperty.call(e,t)};return t.unit===n?st.some(t.value):o(t.unit)&&o(n)?e[t.unit]===e[n]?st.some(t.value):st.some(t.value/e[t.unit]*e[n]):st.none()},qk=function(t){return st.none()},Kk=function(t,n){var e,o,r,i=Xk(t).toOption(),u=Xk(n).toOption();return o=u,r=function(t,o){return Yk(t,o.unit).map(function(t){return o.value/t}).map(function(t){return n=t,e=o.unit,function(t){return Yk(t,e).map(function(t){return{value:t*n,unit:e}})};var n,e}).getOr(qk)},((e=i).isSome()&&o.isSome()?st.some(r(e.getOrDie(),o.getOrDie())):st.none()).getOr(qk)},Jk=function(o,n){var a=qk,r=Nr("ratio-event"),t=Gk.parts().lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:n.translate(o.label.getOr("Constrain proportions"))}},components:[{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__lock"],innerHtml:Gg("lock",n.icons)}},{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__unlock"],innerHtml:Gg("unlock",n.icons)}}],buttonBehaviours:za([ph.config({disabled:function(){return o.disabled||n.isReadOnly()}}),Uh(),Dy.config({})])}),e=function(t){return{dom:{tag:"div",classes:["tox-form__group"]},components:t}},i=function(e){return ey.parts().field({factory:uy,inputClasses:["tox-textfield"],inputBehaviours:za([ph.config({disabled:function(){return o.disabled||n.isReadOnly()}}),Uh(),Dy.config({}),Lm("size-input-events",[Jo(io(),function(t,n){jo(t,r,{isField1:e})}),Jo(lo(),function(t,n){jo(t,Gb,{name:o.name})})])]),selectOnFocus:!1})},u=function(t){return{dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}}},c=Gk.parts().field1(e([ey.parts().label(u("Width")),i(!0)])),s=Gk.parts().field2(e([ey.parts().label(u("Height")),i(!1)]));return Gk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,e([u("&nbsp;"),t])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(t,i,n){Xk(bl.getValue(t)).each(function(t){a(t).each(function(t){var n,e,o,r;bl.setValue(i,(o={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,"in":4,"%":4},-1!==(r=(n=t).value.toFixed((e=n.unit)in o?o[e]:1)).indexOf(".")&&(r=r.replace(/\.?0*$/,"")),r+n.unit))})})},coupledFieldBehaviours:za([ph.config({disabled:function(){return o.disabled||n.isReadOnly()},onDisabled:function(t){Gk.getField1(t).bind(ey.getField).each(ph.disable),Gk.getField2(t).bind(ey.getField).each(ph.disable),Gk.getLock(t).each(ph.disable)},onEnabled:function(t){Gk.getField1(t).bind(ey.getField).each(ph.enable),Gk.getField2(t).bind(ey.getField).each(ph.enable),Gk.getLock(t).each(ph.enable)}}),Uh(),Lm("size-input-events2",[Jo(r,function(t,n){var e=n.event().isField1(),o=e?Gk.getField1(t):Gk.getField2(t),r=e?Gk.getField2(t):Gk.getField1(t),i=o.map(bl.getValue).getOr(""),u=r.map(bl.getValue).getOr("");a=Kk(i,u)})])])})},$k={undo:at(Nr("undo")),redo:at(Nr("redo")),zoom:at(Nr("zoom")),back:at(Nr("back")),apply:at(Nr("apply")),swap:at(Nr("swap")),transform:at(Nr("transform")),tempTransform:at(Nr("temp-transform")),transformApply:at(Nr("transform-apply"))},Qk=at("save-state"),Zk=at("disable"),tC=at("enable"),nC={formActionEvent:qb,saveState:Qk,disable:Zk,enable:tC},eC=function(a,c){var t=function(t,n,e,o){return Ug(Pk({name:t,text:t,disabled:e,primary:o,icon:st.none(),borderless:!1},n,c))},n=function(t,n,e,o){return Ug(Vk({name:t,icon:st.some(t),tooltip:st.some(n),disabled:o,primary:!1,borderless:!1},e,c))},u=function(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(ph)&&ph.disable(n)})},s=function(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(ph)&&ph.enable(n)})},l={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},e=Z,r=function(t,n,e){jo(t,n,e)},i=function(t){return Lo(t,nC.disable())},f=function(t){return Lo(t,nC.enable())},d=function(t,n){i(t),r(t,$k.transform(),{transform:n}),f(t)},o=function(t){return function(){Q.getOpt(t).each(function(t){Nm.set(t,[J])})}},m=function(t,n){i(t),r(t,$k.transformApply(),{transform:n,swap:o(t)}),f(t)},g=function(){return t("Back",function(t){return r(t,$k.back(),{swap:o(t)})},!1,!1)},p=function(){return Ug({dom:{tag:"div",classes:["tox-spacer"]},behaviours:za([ph.config({})])})},h=function(){return t("Apply",function(t){return r(t,$k.apply(),{swap:o(t)})},!0,!0)},v=function(){return function(t){var n,e,o,r,i,u=a.getRect();return n=t,e=u.x,o=u.y,r=u.w,i=u.h,ak(n,e,o,r,i)}},b=[g(),p(),t("Apply",function(t){var n=v();m(t,n),a.hideCrop()},!1,!0)],y=Ub.sketch({dom:l,components:b.map(function(t){return t.asSpec()}),containerBehaviours:za([Lm("image-tools-crop-buttons-events",[Jo(nC.disable(),function(t,n){u(b,t)}),Jo(nC.enable(),function(t,n){s(b,t)})])])}),x=Ug(Jk({name:"size",label:st.none(),constrain:!0,disabled:!1},c)),w=[g(),p(),x,p(),t("Apply",function(a){x.getOpt(a).each(function(t){var n,e,o=bl.getValue(t),r=parseInt(o.width,10),i=parseInt(o.height,10),u=(n=r,e=i,function(t){return gk(t,n,e)});m(a,u)})},!1,!0)],S=Ub.sketch({dom:l,components:w.map(function(t){return t.asSpec()}),containerBehaviours:za([Lm("image-tools-resize-buttons-events",[Jo(nC.disable(),function(t,n){u(w,t)}),Jo(nC.enable(),function(t,n){s(w,t)})])])}),k=function(n,e){return function(t){return n(t,e)}},C=k(mk,"h"),O=k(mk,"v"),_=k(pk,-90),T=k(pk,90),E=function(t,n){var e,o;o=n,i(e=t),r(e,$k.tempTransform(),{transform:o}),f(e)},B=[g(),p(),n("flip-horizontally","Flip horizontally",function(t){E(t,C)},!1),n("flip-vertically","Flip vertically",function(t){E(t,O)},!1),n("rotate-left","Rotate counterclockwise",function(t){E(t,_)},!1),n("rotate-right","Rotate clockwise",function(t){E(t,T)},!1),p(),h()],D=Ub.sketch({dom:l,components:B.map(function(t){return t.asSpec()}),containerBehaviours:za([Lm("image-tools-fliprotate-buttons-events",[Jo(nC.disable(),function(t,n){u(B,t)}),Jo(nC.enable(),function(t,n){s(B,t)})])])}),M=function(t,n,e,o,r){var i=Lw.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(t)}}),u=Lw.parts().spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=Lw.parts().thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return Ug(Lw.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e,maxX:r,getInitialValue:at({x:at(o)})},components:[i,u,a],sliderBehaviours:za([Xm.config({})]),onChoose:n}))},A=function(t,n,e,o,r){return[g(),(i=n,M(t,function(t,n,e){var o=k(i,e.x()/100);d(t,o)},e,o,r)),h()];var i},F=function(t,n,e,o,r){var i=A(t,n,e,o,r);return Ub.sketch({dom:l,components:i.map(function(t){return t.asSpec()}),containerBehaviours:za([Lm("image-tools-filter-panel-buttons-events",[Jo(nC.disable(),function(t,n){u(i,t)}),Jo(nC.enable(),function(t,n){s(i,t)})])])})},I=[g(),p(),h()],R=Ub.sketch({dom:l,components:I.map(function(t){return t.asSpec()})}),V=F("Brightness",fk,-100,0,100),H=F("Contrast",dk,-100,0,100),P=F("Gamma",lk,-100,0,100),z=function(n,e,o){return function(t){return nk(t,n,e,o)}},N=function(t){return M(t,function(a,t,n){var e=L.getOpt(a),o=U.getOpt(a),r=j.getOpt(a);e.each(function(u){o.each(function(i){r.each(function(t){var n=bl.getValue(u).x()/100,e=bl.getValue(t).x()/100,o=bl.getValue(i).x()/100,r=z(n,e,o);d(a,r)})})})},0,100,200)},L=N("R"),j=N("G"),U=N("B"),W=[g(),L,j,U,h()],G=Ub.sketch({dom:l,components:W.map(function(t){return t.asSpec()})}),X=function(n,e,o){return function(t){r(t,$k.swap(),{transform:e,swap:function(){Q.getOpt(t).each(function(t){Nm.set(t,[n]),o(t)})}})}},Y=st.some(sk),q=st.some(ck),K=[n("crop","Crop",X(y,st.none(),function(t){a.showCrop()}),!1),n("resize","Resize",X(S,st.none(),function(t){x.getOpt(t).each(function(t){var n=a.getMeasurements(),e=n.width,o=n.height;bl.setValue(t,{width:e,height:o})})}),!1),n("orientation","Orientation",X(D,st.none(),e),!1),n("brightness","Brightness",X(V,st.none(),e),!1),n("sharpen","Sharpen",X(R,Y,e),!1),n("contrast","Contrast",X(H,st.none(),e),!1),n("color-levels","Color levels",X(G,st.none(),e),!1),n("gamma","Gamma",X(P,st.none(),e),!1),n("invert","Invert",X(R,q,e),!1)],J=Ub.sketch({dom:l,components:K.map(function(t){return t.asSpec()})}),$=Ub.sketch({dom:{tag:"div"},components:[J],containerBehaviours:za([Nm.config({})])}),Q=Ug($);return{memContainer:Q,getApplyButton:function(t){return Q.getOpt(t).map(function(t){var n=t.components()[0];return n.components()[n.components().length-1]})}}},oC=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),rC=tinymce.util.Tools.resolve("tinymce.geom.Rect"),iC=tinymce.util.Tools.resolve("tinymce.util.Observable"),uC=tinymce.util.Tools.resolve("tinymce.util.Tools"),aC=tinymce.util.Tools.resolve("tinymce.util.VK");function cC(t){var n,e;if(t.changedTouches)for(n="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<n.length;e++)t[n[e]]=t.changedTouches[0][n[e]]}function sC(t,r){var i,u,n,a,c,l,f,d=r.document||nt.document;r=r||{};var m=d.getElementById(r.handle||t);n=function(t){var n,e,o=function s(t){var n,e,o,r,i,u,a,c=Math.max;return n=t.documentElement,e=t.body,o=c(n.scrollWidth,e.scrollWidth),r=c(n.clientWidth,e.clientWidth),i=c(n.offsetWidth,e.offsetWidth),u=c(n.scrollHeight,e.scrollHeight),a=c(n.clientHeight,e.clientHeight),{width:o<i?r:o,height:u<c(n.offsetHeight,e.offsetHeight)?a:u}}(d);cC(t),t.preventDefault(),u=t.button,n=m,l=t.screenX,f=t.screenY,e=nt.window.getComputedStyle?nt.window.getComputedStyle(n,null).getPropertyValue("cursor"):n.runtimeStyle.cursor,i=oC("<div></div>").css({position:"absolute",top:0,left:0,width:o.width,height:o.height,zIndex:2147483647,opacity:1e-4,cursor:e}).appendTo(d.body),oC(d).on("mousemove touchmove",c).on("mouseup touchend",a),r.start(t)},c=function(t){if(cC(t),t.button!==u)return a(t);t.deltaX=t.screenX-l,t.deltaY=t.screenY-f,t.preventDefault(),r.drag(t)},a=function(t){cC(t),oC(d).off("mousemove touchmove",c).off("mouseup touchend",a),i.remove(),r.stop&&r.stop(t)},this.destroy=function(){oC(m).off()},oC(m).on("mousedown touchstart",n)}var lC=0,fC=function(s,e,l,o,r){var f,n,i,u="tox-",a="tox-crid-"+lC++,c=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}];i=["top","right","bottom","left"];var d=function(t,n){return{x:n.x+t.x,y:n.y+t.y,w:n.w,h:n.h}},m=function(t,n){return{x:n.x-t.x,y:n.y-t.y,w:n.w,h:n.h}};function g(t,n,e,o){var r,i,u,a,c;r=n.x,i=n.y,u=n.w,a=n.h,r+=e*t.deltaX,i+=o*t.deltaY,(u+=e*t.deltaW)<20&&(u=20),(a+=o*t.deltaH)<20&&(a=20),c=s=rC.clamp({x:r,y:i,w:u,h:a},l,"move"===t.name),c=m(l,c),f.fire("updateRect",{rect:c}),v(c)}function p(n){function t(t,n){n.h<0&&(n.h=0),n.w<0&&(n.w=0),oC("#"+a+"-"+t,o).css({left:n.x,top:n.y,width:n.w,height:n.h})}uC.each(c,function(t){oC("#"+a+"-"+t.name,o).css({left:n.w*t.xMul+n.x,top:n.h*t.yMul+n.y})}),t("top",{x:e.x,y:e.y,w:e.w,h:n.y-e.y}),t("right",{x:n.x+n.w,y:n.y,w:e.w-n.x-n.w+e.x,h:n.h}),t("bottom",{x:e.x,y:n.y+n.h,w:e.w,h:e.h-n.y-n.h+e.y}),t("left",{x:e.x,y:n.y,w:n.x-e.x,h:n.h}),t("move",n)}function h(t){p(s=t)}function v(t){h(d(l,t))}return function b(){oC('<div id="'+a+'" class="'+u+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(o),uC.each(i,function(t){oC("#"+a,o).append('<div id="'+a+"-"+t+'"class="'+u+'croprect-block" style="display: none" data-mce-bogus="all">')}),uC.each(c,function(t){oC("#"+a,o).append('<div id="'+a+"-"+t.name+'" class="'+u+"croprect-handle "+u+"croprect-handle-"+t.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+t.label+'" aria-grabbed="false" title="'+t.label+'">')}),n=uC.map(c,function t(n){var e;return new sC(a,{document:o.ownerDocument,handle:a+"-"+n.name,start:function(){e=s},drag:function(t){g(n,e,t.deltaX,t.deltaY)}})}),p(s),oC(o).on("focusin focusout",function(t){oC(t.target).attr("aria-grabbed","focus"===t.type?"true":"false")}),oC(o).on("keydown",function(n){var i;function t(t,n,e,o,r){t.stopPropagation(),t.preventDefault(),g(i,e,o,r)}switch(uC.each(c,function(t){if(n.target.id===a+"-"+t.name)return i=t,!1}),n.keyCode){case aC.LEFT:t(n,0,s,-10,0);break;case aC.RIGHT:t(n,0,s,10,0);break;case aC.UP:t(n,0,s,0,-10);break;case aC.DOWN:t(n,0,s,0,10);break;case aC.ENTER:case aC.SPACEBAR:n.preventDefault(),r()}})}(),f=uC.extend({toggleVisibility:function y(t){var n;n=uC.map(c,function(t){return"#"+a+"-"+t.name}).concat(uC.map(i,function(t){return"#"+a+"-"+t})).join(","),t?oC(n,o).show():oC(n,o).hide()},setClampRect:function x(t){l=t,p(s)},setRect:h,getInnerRect:function(){return m(l,s)},setInnerRect:v,setViewPortRect:function w(t){e=t,p(s)},destroy:function t(){uC.each(n,function(t){t.destroy()}),n=[]}},iC)},dC=function(n){var l=Ug({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),f=ce(1),d=ce(st.none()),m=ce({x:0,y:0,w:1,h:1}),c=ce({x:0,y:0,w:1,h:1}),s=function(t,s){g.getOpt(t).each(function(t){var e=f.get(),o=cu(t.element()),r=tu(t.element()),i=s.dom().naturalWidth*e,u=s.dom().naturalHeight*e,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),n={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};Mi(s,n),l.getOpt(t).each(function(t){Mi(t.element(),n)}),d.get().each(function(t){var n=m.get();t.setRect({x:n.x*e+a,y:n.y*e+c,w:n.w*e,h:n.h*e}),t.setClampRect({x:a,y:c,w:i,h:u}),t.setViewPortRect({x:0,y:0,w:o,h:r})})})},e=function(t,n){var e,a=le.fromTag("img");return Br(a,"src",n),e=a.dom(),new cp(function(t){var n=function(){e.removeEventListener("load",n),t(e)};e.complete?t(e):e.addEventListener("load",n)}).then(function(){return g.getOpt(t).map(function(t){var n=qi({element:a});Nm.replaceAt(t,1,st.some(n));var e=c.get(),o={x:0,y:0,w:a.dom().naturalWidth,h:a.dom().naturalHeight};c.set(o);var r,u,i=rC.inflate(o,-20,-20);return m.set(i),e.w===o.w&&e.h===o.h||(r=t,u=a,g.getOpt(r).each(function(t){var n=cu(t.element()),e=tu(t.element()),o=u.dom().naturalWidth,r=u.dom().naturalHeight,i=Math.min(n/o,e/r);1<=i?f.set(1):f.set(i)})),s(t,a),a})})},t=Ub.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[l.asSpec(),{dom:{tag:"img",attributes:{src:n}}},{dom:{tag:"div"},behaviours:za([Lm("image-panel-crop-events",[or(function(t){g.getOpt(t).each(function(t){var n=t.element().dom(),e=fC({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},n,function(){});e.toggleVisibility(!1),e.on("updateRect",function(t){var n=t.rect,e=f.get(),o={x:Math.round(n.x/e),y:Math.round(n.y/e),w:Math.round(n.w/e),h:Math.round(n.h/e)};m.set(o)}),d.set(st.some(e))})})])])}],containerBehaviours:za([Nm.config({}),Lm("image-panel-events",[or(function(t){e(t,n)})])])}),g=Ug(t);return{memContainer:g,updateSrc:e,zoom:function(t,n){var e=f.get(),o=0<n?Math.min(2,e+.1):Math.max(.1,e-.1);f.set(o),g.getOpt(t).each(function(t){var n=t.components()[1].element();s(t,n)})},showCrop:function(){d.get().each(function(t){t.toggleVisibility(!0)})},hideCrop:function(){d.get().each(function(t){t.toggleVisibility(!1)})},getRect:function(){return m.get()},getMeasurements:function(){var t=c.get();return{width:t.w,height:t.h}}}},mC=function(t,n,e,o,r){return Vk({name:t,icon:st.some(n),disabled:e,tooltip:st.some(t),primary:!1,borderless:!1},o,r)},gC=function(t,n){n?ph.enable(t):ph.disable(t)};function pC(){var e=[],o=-1;function t(){return 0<o}function n(){return-1!==o&&o<e.length-1}return{data:e,add:function r(t){var n;return n=e.splice(++o),e.push(t),{state:t,removed:n}},undo:function i(){if(t())return e[--o]},redo:function u(){if(n())return e[++o]},canUndo:t,canRedo:n}}var hC,vC,bC,yC,xC=function(t){var n=ce(t),e=ce(st.none()),r=pC();r.add(t);var i=function(t){n.set(t)},u=function(t){return{blob:t,url:nt.URL.createObjectURL(t)}},a=function(t){nt.URL.revokeObjectURL(t.url)},o=function(){e.get().each(a),e.set(st.none())},c=function(t){var n=u(t);i(n);var e,o=r.add(n).removed;return e=o,uC.each(e,a),n.url};return{getBlobState:function(){return n.get()},setBlobState:i,addBlobState:c,getTempState:function(){return e.get().fold(function(){return n.get()},function(t){return t})},updateTempState:function(t){var n=u(t);return o(),e.set(st.some(n)),n.url},addTempState:function(t){var n=u(t);return e.set(st.some(n)),n.url},applyTempState:function(n){return e.get().fold(function(){},function(t){c(t.blob),n()})},destroyTempState:o,undo:function(){var t=r.undo();return i(t),t.url},redo:function(){var t=r.redo();return i(t),t.url},getHistoryStates:function(){return{undoEnabled:r.canUndo(),redoEnabled:r.canRedo()}}}},wC=function(t,n){var e,o,r,u=xC(t.currentState),i=function(t){var n=u.getHistoryStates();p.updateButtonUndoStates(t,n.undoEnabled,n.redoEnabled),jo(t,nC.formActionEvent,{name:nC.saveState(),value:n.undoEnabled})},a=function(t){return t.toBlob()},c=function(t){jo(t,nC.formActionEvent,{name:nC.disable(),value:{}})},s=function(t){h.getApplyButton(t).each(function(t){ph.enable(t)}),jo(t,nC.formActionEvent,{name:nC.enable(),value:{}})},l=function(t,n){return c(t),g.updateSrc(t,n)},f=function(n,t,e,o,r){return c(n),US(t).then(e).then(a).then(o).then(function(t){return l(n,t).then(function(t){return i(n),r(),s(n),t})})["catch"](function(t){return nt.console.log(t),s(n),t})},d=function(t,n,e){var o=u.getBlobState().blob;f(t,o,n,function(t){return u.updateTempState(t)},e)},m=function(t){var n=u.getBlobState().url;return u.destroyTempState(),i(t),n},g=dC(t.currentState.url),p=(o=Ug(mC("Undo","undo",!0,function(t){jo(t,$k.undo(),{direction:1})},e=n)),r=Ug(mC("Redo","redo",!0,function(t){jo(t,$k.redo(),{direction:1})},e)),{container:Ub.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),r.asSpec(),mC("Zoom in","zoom-in",!1,function(t){jo(t,$k.zoom(),{direction:1})},e),mC("Zoom out","zoom-out",!1,function(t){jo(t,$k.zoom(),{direction:-1})},e)]}),updateButtonUndoStates:function(t,n,e){o.getOpt(t).each(function(t){gC(t,n)}),r.getOpt(t).each(function(t){gC(t,e)})}}),h=eC(g,n);return{dom:{tag:"div",attributes:{role:"presentation"}},components:[h.memContainer.asSpec(),g.memContainer.asSpec(),p.container],behaviours:za([bl.config({store:{mode:"manual",getValue:function(){return u.getBlobState()}}}),Lm("image-tools-events",[Jo($k.undo(),function(n,t){var e=u.undo();l(n,e).then(function(t){s(n),i(n)})}),Jo($k.redo(),function(n,t){var e=u.redo();l(n,e).then(function(t){s(n),i(n)})}),Jo($k.zoom(),function(t,n){var e=n.event().direction();g.zoom(t,e)}),Jo($k.back(),function(t,n){var e,o;o=m(e=t),l(e,o).then(function(t){s(e)}),n.event().swap()(),g.hideCrop()}),Jo($k.apply(),function(t,n){u.applyTempState(function(){m(t),n.event().swap()()})}),Jo($k.transform(),function(t,n){return d(t,n.event().transform(),Z)}),Jo($k.tempTransform(),function(t,n){return e=t,o=n.event().transform(),r=u.getTempState().blob,void f(e,r,o,function(t){return u.addTempState(t)},Z);var e,o,r}),Jo($k.transformApply(),function(t,n){return e=t,o=n.event().transform(),r=n.event().swap(),i=u.getBlobState().blob,void f(e,i,o,function(t){var n=u.addBlobState(t);return m(e),n},r);var e,o,r,i}),Jo($k.swap(),function(n,t){var e;e=n,p.updateButtonUndoStates(e,!1,!1);var o=t.event().transform(),r=t.event().swap();o.fold(function(){r()},function(t){d(n,t,r)})})]),Zw()])}},SC=Of({name:"HtmlSelect",configFields:[zn("options"),yl("selectBehaviours",[Xm,bl]),Zn("selectClasses",[]),Zn("selectAttributes",{}),Yn("data")],factory:function(e,t){var n=V(e.options,function(t){return{dom:{tag:"option",value:t.value,innerHtml:t.text}}}),o=e.data.map(function(t){return Kt("initialValue",t)}).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:n,behaviours:wl(e.selectBehaviours,[Xm.config({}),bl.config({store:et({mode:"manual",getValue:function(t){return zi(t.element())},setValue:function(t,n){L(e.options,function(t){return t.value===n}).isSome()&&Ni(t.element(),n)}},o)})])}}}),kC=function(e,n){var t=e.label.map(function(t){return Iy(t,n)}),o=[ph.config({disabled:function(){return e.disabled||n.isReadOnly()}}),Uh(),Rm.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:function(t){return Lo(t,Kb),st.some(!0)}}),Lm("textfield-change",[Jo(so(),function(t,n){jo(t,Gb,{name:e.name})}),Jo(bo(),function(t,n){jo(t,Gb,{name:e.name})})]),Dy.config({})],r=e.validation.map(function(o){return Ty.config({getRoot:function(t){return lr(t.element())},invalidClass:"tox-invalid",validator:{validate:function(t){var n=bl.getValue(t),e=o.validator(n);return vy(!0===e?ot.value(n):ot.error(e))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=e.placeholder.fold(at({}),function(t){return{placeholder:n.translate(t)}}),u=e.inputMode.fold(at({}),function(t){return{inputmode:t}}),a=et(et({},i),u),c=ey.parts().field({tag:!0===e.multiline?"textarea":"input",inputAttributes:a,inputClasses:[e.classname],inputBehaviours:za(ut([o,r])),selectOnFocus:!1,factory:uy}),s=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),l=[ph.config({disabled:function(){return e.disabled||n.isReadOnly()},onDisabled:function(t){ey.getField(t).each(ph.disable)},onEnabled:function(t){ey.getField(t).each(ph.enable)}}),Uh()];return My(t,c,s,l)},CC=/* */Object.freeze({__proto__:null,events:function(t,n){var e=t.stream.streams.setup(t,n);return Yo([Jo(t.event,e),rr(function(){return n.cancel()})].concat(t.cancelEvent.map(function(t){return[Jo(t,function(){return n.cancel()})]}).getOr([])))}}),OC=function(t){var n=ce(null);return oi({readState:function(){return{timer:null!==n.get()?"set":"unset"}},setTimer:function(t){n.set(t)},cancel:function(){var t=n.get();null!==t&&t.cancel()}})},_C=/* */Object.freeze({__proto__:null,throttle:OC,init:function(t){return t.stream.streams.state(t)}}),TC=[Nn("stream",Bn("mode",{throttle:[zn("delay"),Zn("stopEvent",!0),na("streams",{setup:function(t,n){var e=t.stream,o=$g(t.onStream,e.delay);return n.setTimer(o),function(t,n){o.throttle(t,n),e.stopEvent&&n.stop()}},state:OC})]})),Zn("event","input"),Yn("cancelEvent"),Zu("onStream")],EC=La({fields:TC,name:"streaming",active:CC,state:_C}),BC=function(t,n,e){var o=bl.getValue(e);bl.setValue(n,o),MC(n)},DC=function(t,n){var e=t.element(),o=zi(e),r=e.dom();"number"!==Dr(e,"type")&&n(r,o)},MC=function(t){DC(t,function(t,n){return t.setSelectionRange(n.length,n.length)})},AC=function(t,n,o){if(t.selectsOver){var e=bl.getValue(n),r=t.getDisplayText(e),i=bl.getValue(o);return 0===t.getDisplayText(i).indexOf(r)?st.some(function(){var t,e;BC(0,n,o),t=n,e=r.length,DC(t,function(t,n){return t.setSelectionRange(e,n.length)})}):st.none()}return st.none()},FC=at("alloy.typeahead.itemexecute"),IC=at([Yn("lazySink"),zn("fetch"),Zn("minChars",5),Zn("responseTime",1e3),$u("onOpen"),Zn("getHotspot",st.some),Zn("getAnchorOverrides",at({})),Zn("layouts",st.none()),Zn("eventOrder",{}),ue("model",{},[Zn("getDisplayText",function(t){return t.meta!==undefined&&t.meta.text!==undefined?t.meta.text:t.value}),Zn("selectsOver",!0),Zn("populateFromBrowse",!0)]),$u("onSetValue"),Qu("onExecute"),$u("onItemExecute"),Zn("inputClasses",[]),Zn("inputAttributes",{}),Zn("inputStyles",{}),Zn("matchWidth",!0),Zn("useMinWidth",!1),Zn("dismissOnBlur",!0),Ku(["openClass"]),Yn("initialData"),yl("typeaheadBehaviours",[Xm,bl,EC,Rm,rg,Hy]),ae("previewing",function(){return ce(!0)})].concat(oy()).concat(Ky())),RC=at([Kl({schema:[qu()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(n,e){o.previewing.get()?n.getSystem().getByUid(o.uid).each(function(t){AC(o.model,t,e).fold(function(){return jf.dehighlight(n,e)},function(t){return t()})}):n.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&BC(o.model,t,e)}),o.previewing.set(!1)},onExecute:function(t,n){return t.getSystem().getByUid(o.uid).toOption().map(function(t){return jo(t,FC(),{item:n}),!0})},onHover:function(t,n){o.previewing.set(!1),t.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&BC(o.model,t,n)})}}}})]),VC=_f({name:"Typeahead",configFields:IC(),partFields:RC(),factory:function(r,t,n,i){var e=function(t,n,e){r.previewing.set(!1);var o=Hy.getCoupled(t,"sandbox");if(Qs.isOpen(o))Mf.getCurrent(o).each(function(t){jf.getHighlighted(t).fold(function(){e(t)},function(){Xo(o,t.element(),"keydown",n)})});else{Ly(r,u(t),t,o,i,function(t){Mf.getCurrent(t).each(e)},sy.HighlightFirst).get(Z)}},o=ry(r),u=function(o){return function(t){return t.map(function(t){var n=vt(t.menus),e=U(n,function(t){return P(t.items,function(t){return"item"===t.type})});return bl.getState(o).update(V(e,function(t){return t.data})),t})}},a=[Xm.config({}),bl.config({onSetValue:r.onSetValue,store:et({mode:"dataset",getDataKey:function(t){return zi(t.element())},getFallbackEntry:function(t){return{value:t,meta:{}}},setValue:function(t,n){Ni(t.element(),r.model.getDisplayText(n))}},r.initialData.map(function(t){return Kt("initialValue",t)}).getOr({}))}),EC.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(t,n){var e=Hy.getCoupled(t,"sandbox");if(Xm.isFocused(t)&&zi(t.element()).length>=r.minChars){var o=Mf.getCurrent(e).bind(function(t){return jf.getHighlighted(t).map(bl.getValue)});r.previewing.set(!0);Ly(r,u(t),t,e,i,function(t){Mf.getCurrent(e).each(function(t){o.fold(function(){r.model.selectsOver&&jf.highlightFirst(t)},function(n){jf.highlightBy(t,function(t){return bl.getValue(t).value===n.value}),jf.getHighlighted(t).orThunk(function(){return jf.highlightFirst(t),st.none()})})})},sy.HighlightFirst).get(Z)}},cancelEvent:Oo()}),Rm.config({mode:"special",onDown:function(t,n){return e(t,n,jf.highlightFirst),st.some(!0)},onEscape:function(t){var n=Hy.getCoupled(t,"sandbox");return Qs.isOpen(n)?(Qs.close(n),st.some(!0)):st.none()},onUp:function(t,n){return e(t,n,jf.highlightLast),st.some(!0)},onEnter:function(n){var t=Hy.getCoupled(n,"sandbox"),e=Qs.isOpen(t);if(e&&!r.previewing.get())return Mf.getCurrent(t).bind(function(t){return jf.getHighlighted(t)}).map(function(t){return jo(n,FC(),{item:t}),!0});var o=bl.getValue(n);return Lo(n,Oo()),r.onExecute(t,n,o),e&&Qs.close(t),st.some(!0)}}),rg.config({toggleClass:r.markers.openClass,aria:{mode:"expanded"}}),Hy.config({others:{sandbox:function(t){return Yy(r,t,{onOpen:function(){return rg.on(t)},onClose:function(){return rg.off(t)}})}}}),Lm("typeaheadevents",[ur(function(t){var n=Z;Uy(r,u(t),t,i,n,sy.HighlightFirst).get(Z)}),Jo(FC(),function(t,n){var e=Hy.getCoupled(t,"sandbox");BC(r.model,t,n.event().item()),Lo(t,Oo()),r.onItemExecute(t,e,n.event().item(),bl.getValue(t)),Qs.close(e),MC(t)})].concat(r.dismissOnBlur?[Jo(vo(),function(t){var n=Hy.getCoupled(t,"sandbox");$a(n.element()).isNone()&&Qs.close(n)})]:[]))];return{uid:r.uid,dom:iy(Ct(r,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:et(et({},o),wl(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),HC=function(i){return et(et({},i),{toCached:function(){return HC(i.toCached())},bindFuture:function(n){return HC(i.bind(function(t){return t.fold(function(t){return vy(ot.error(t))},function(t){return n(t)})}))},bindResult:function(n){return HC(i.map(function(t){return t.bind(n)}))},mapResult:function(n){return HC(i.map(function(t){return t.map(n)}))},mapError:function(n){return HC(i.map(function(t){return t.mapError(n)}))},foldResult:function(n,e){return i.map(function(t){return t.fold(n,e)})},withTimeout:function(t,r){return HC(hy(function(n){var e=!1,o=nt.setTimeout(function(){e=!0,n(ot.error(r()))},t);i.get(function(t){e||(nt.clearTimeout(o),n(t))})}))}})},PC=function(t){return HC(hy(t))},zC=PC,NC={type:"separator"},LC=function(t){return{type:"menuitem",value:t.url,text:t.title,meta:{attach:t.attach},onAction:function(){}}},jC=function(t,n){return{type:"menuitem",value:n,text:t,meta:{attach:undefined},onAction:function(){}}},UC=function(t,n){return o=t,e=P(n,function(t){return t.type===o}),V(e,LC);var e,o},WC=function(t,n){var e=t.toLowerCase();return P(n,function(t){var n=t.meta!==undefined&&t.meta.text!==undefined?t.meta.text:t.text;return Ee(n.toLowerCase(),e)||Ee(t.value.toLowerCase(),e)})},GC=function(u,t,a){var n=bl.getValue(t),c=n.meta.text!==undefined?n.meta.text:n.value;return a.getLinkInformation().fold(function(){return[]},function(t){var n,e,o,r,i=WC(c,(n=a.getHistory(u),V(n,function(t){return jC(t,t)})));return"file"===u?(e=[i,WC(c,UC("header",t.targets)),WC(c,ut([(r=t,st.from(r.anchorTop).map(function(t){return jC("<top>",t)}).toArray()),UC("anchor",t.targets),(o=t,st.from(o.anchorBottom).map(function(t){return jC("<bottom>",t)}).toArray())]))],N(e,function(t,n){return 0===t.length||0===n.length?t.concat(n):t.concat(NC,n)},[])):i})},XC=Nr("aria-invalid"),YC=function(r,o,i){var t,n,e,u,a,c=o.shared.providers,s=function(t){var n=bl.getValue(t);i.addToHistory(n.value,r.filetype)},l=ey.parts().field({factory:VC,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":XC,type:"url"},minChars:0,responseTime:0,fetch:function(t){var n=GC(r.filetype,t,i),e=Dk(n,$h.BUBBLE_TO_SANDBOX,o,!1);return vy(e)},getHotspot:function(t){return h.getOpt(t)},onSetValue:function(t,n){t.hasConfigured(Ty)&&Ty.run(t).get(Z)},typeaheadBehaviours:za(ut([i.getValidationHandler().map(function(e){return Ty.config({getRoot:function(t){return lr(t.element())},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(t,n){d.getOpt(t).each(function(t){Br(t.element(),"title",c.translate(n))})}},validator:{validate:function(t){var n=bl.getValue(t);return zC(function(o){e({type:r.filetype,url:n.value},function(t){if("invalid"===t.status){var n=ot.error(t.message);o(n)}else{var e=ot.value(t.message);o(e)}})})},validateOnLoad:!1}})}).toArray(),[ph.config({disabled:function(){return r.disabled||c.isReadOnly()}}),Dy.config({}),Lm("urlinput-events",ut(["file"===r.filetype?[Jo(so(),function(t){jo(t,Gb,{name:r.name})})]:[],[Jo(lo(),function(t){jo(t,Gb,{name:r.name}),s(t)}),Jo(bo(),function(t){jo(t,Gb,{name:r.name}),s(t)})]]))]])),eventOrder:((t={})[so()]=["streaming","urlinput-events","invalidating"],t),model:{getDisplayText:function(t){return t.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:o.shared.getSink,parts:{menu:Yv(0,0,"normal")},onExecute:function(t,n,e){jo(n,Kb,{})},onItemExecute:function(t,n,e,o){s(t),jo(t,Gb,{name:r.name})}}),f=r.label.map(function(t){return Iy(t,c)}),d=Ug((n="invalid",e=st.some(XC),void 0===(u="warning")&&(u=n),void 0===a&&(a=n),{dom:{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+n],innerHtml:Gg(u,c.icons),attributes:et({title:c.translate(a),"aria-live":"polite"},e.fold(function(){return{}},function(t){return{id:t}}))}})),m=Ug({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[d.asSpec()]}),g=i.getUrlPicker(r.filetype),p=Nr("browser.url.event"),h=Ug({dom:{tag:"div",classes:["tox-control-wrap"]},components:[l,m.asSpec()],behaviours:za([ph.config({disabled:function(){return r.disabled||c.isReadOnly()}})])}),v=Ug(Pk({name:r.name,icon:st.some("browse"),text:r.label.getOr(""),disabled:r.disabled,primary:!1,borderless:!0},function(t){return Lo(t,p)},c,[],["tox-browse-url"]));return ey.sketch({dom:Fy([]),components:f.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:ut([[h.asSpec()],g.map(function(){return v.asSpec()}).toArray()])}]),fieldBehaviours:za([ph.config({disabled:function(){return r.disabled||c.isReadOnly()},onDisabled:function(t){ey.getField(t).each(ph.disable),v.getOpt(t).each(ph.disable)},onEnabled:function(t){ey.getField(t).each(ph.enable),v.getOpt(t).each(ph.enable)}}),Uh(),Lm("url-input-events",[Jo(p,function(o){Mf.getCurrent(o).each(function(n){var t=bl.getValue(n),e=et({fieldname:r.name},t);g.each(function(t){t(e).get(function(t){bl.setValue(n,t),jo(o,Gb,{name:r.name})})})})})])])})},qC=function(a,c){var t,n,e=a.label.map(function(t){return Iy(t,c)}),o=function(o){return function(n,e){Au(e.event().target(),"[data-collection-item-value]").each(function(t){o(n,e,t,Dr(t,"data-collection-item-value"))})}},r=function(t,n){var e=V(n,function(t){var n,e=Vp.translate(t.text),o=1===a.columns?'<div class="tox-collection__item-label">'+e+"</div>":"",r='<div class="tox-collection__item-icon">'+t.icon+"</div>",i={_:" "," - ":" ","-":" "},u=e.replace(/\_| \- |\-/g,function(t){return i[t]});return'<div class="tox-collection__item'+(c.isReadOnly()?" tox-collection__item--state-disabled":"")+'" tabindex="-1" data-collection-item-value="'+('"'===(n=t.value)?"&quot;":n)+'" title="'+u+'" aria-label="'+u+'">'+r+o+"</div>"}),o=1<a.columns&&"auto"!==a.columns?R(e,a.columns):[e],r=V(o,function(t){return'<div class="tox-collection__group">'+t.join("")+"</div>"});kr(t.element(),r.join(""))},i=o(function(t,n,e,o){n.stop(),c.isReadOnly()||jo(t,qb,{name:a.name,value:o})}),u=[Jo(ro(),o(function(t,n,e){Ka(e)})),Jo(fo(),i),Jo(So(),i),Jo(io(),o(function(t,n,e){Mu(t.element(),"."+Wp).each(function(t){xi(t,Wp)}),bi(e,Wp)})),Jo(uo(),o(function(t){Mu(t.element(),"."+Wp).each(function(t){xi(t,Wp)})})),ur(o(function(t,n,e,o){jo(t,qb,{name:a.name,value:o})}))],s=function(t,n){return V(Yc(t.element(),".tox-collection__item"),n)},l=ey.parts().field({dom:{tag:"div",classes:["tox-collection"].concat(1!==a.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:ct},behaviours:za([ph.config({disabled:c.isReadOnly,onDisabled:function(t){s(t,function(t){bi(t,"tox-collection__item--state-disabled"),Br(t,"aria-disabled",!0)})},onEnabled:function(t){s(t,function(t){xi(t,"tox-collection__item--state-disabled"),Fr(t,"aria-disabled")})}}),Uh(),Nm.config({}),bl.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,t){r(o,t),"auto"===a.columns&&Op(o,5,"tox-collection__item").each(function(t){var n=t.numRows,e=t.numColumns;Rm.setGridSize(o,n,e)}),Lo(o,Zb)}}),Dy.config({}),Rm.config((t=a.columns,n="normal",1===t?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===t?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:"color"===n?".tox-swatches__row":".tox-collection__group",cell:"color"===n?"."+zp:"."+Pp}})),Lm("collection-events",u)]),eventOrder:{"alloy.execute":["disabling","alloy.base.behaviour","collection-events"]}});return My(e,l,["tox-form__group--collection"],[])},KC=function(r){return function(n,e,o){return bt(e,"name").fold(function(){return r(e,o)},function(t){return n.field(t,r(e,o))})}},JC={bar:KC(function(t,n){return e=t,o=n.shared,{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:V(e.items,o.interpreter)};var e,o}),collection:KC(function(t,n){return qC(t,n.shared.providers)}),alertbanner:KC(function(t,n){return e=t,o=n.shared.providers,Ub.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+e.level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[jg.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:Gg(e.icon,o.icons),attributes:{title:o.translate(e.iconTooltip)}},action:function(t){jo(t,qb,{name:"alert-banner",value:e.url})}})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:o.translate(e.text)}}]});var e,o}),input:KC(function(t,n){return e=t,o=n.shared.providers,kC({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:e.disabled,classname:"tox-textfield",validation:st.none(),maximized:e.maximized},o);var e,o}),textarea:KC(function(t,n){return e=t,o=n.shared.providers,kC({name:e.name,multiline:!0,label:e.label,inputMode:st.none(),placeholder:e.placeholder,flex:!0,disabled:e.disabled,classname:"tox-textarea",validation:st.none(),maximized:e.maximized},o);var e,o}),label:KC(function(t,n){return e=t,o=n.shared,r={dom:{tag:"label",innerHtml:o.providers.translate(e.label),classes:["tox-label"]}},i=V(e.items,o.interpreter),{dom:{tag:"div",classes:["tox-form__group"]},components:[r].concat(i),behaviours:za([Zw(),Nm.config({}),lS(st.none()),Rm.config({mode:"acyclic"})])};var e,o,r,i}),iframe:(hC=function(t,n){return wS(t,n.shared.providers)},function(t,n,e){var o=Ct(n,{source:"dynamic"});return KC(hC)(t,o,e)}),button:KC(function(t,n){return Lk(t,n.shared.providers)}),checkbox:KC(function(t,n){return e=t,o=n.shared.providers,r=bl.config({store:{mode:"manual",getValue:function(t){return t.element().dom().checked},setValue:function(t,n){t.element().dom().checked=n}}}),i=function(t){return t.element().dom().click(),st.some(!0)},u=ey.parts().field({factory:{sketch:ct},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:za([Zw(),ph.config({disabled:function(){return e.disabled||o.isReadOnly()}}),Dy.config({}),Xm.config({}),r,Rm.config({mode:"special",onEnter:i,onSpace:i,stopSpaceKeyup:!0}),Lm("checkbox-events",[Jo(lo(),function(t,n){jo(t,Gb,{name:e.name})})])])}),a=ey.parts().label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:o.translate(e.label)},behaviours:za([Zy.config({})])}),s=Ug({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[(c=function(t){return{dom:{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+t],innerHtml:Gg("checked"===t?"selected":"unselected",o.icons)}}})("checked"),c("unchecked")]}),ey.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[u,s.asSpec(),a],fieldBehaviours:za([ph.config({disabled:function(){return e.disabled||o.isReadOnly()},disableClass:"tox-checkbox--disabled",onDisabled:function(t){ey.getField(t).each(ph.disable)},onEnabled:function(t){ey.getField(t).each(ph.enable)}}),Uh()])});var e,o,r,i,u,a,c,s}),colorinput:KC(function(t,n){return ox(t,n.shared,n.colorinput)}),colorpicker:KC(function(t){var n=function(t){return"tox-"+t},e=Qw(oS,n),r=Ug(e.sketch({dom:{tag:"div",classes:["tox-color-picker-container"],attributes:{role:"presentation"}},onValidHex:function(t){jo(t,qb,{name:"hex-valid",value:!0})},onInvalidHex:function(t){jo(t,qb,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:za([bl.config({store:{mode:"manual",getValue:function(t){var n=r.get(t);return Mf.getCurrent(n).bind(function(t){return bl.getValue(t).hex}).map(function(t){return"#"+t}).getOr("")},setValue:function(t,n){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(n),o=r.get(t);Mf.getCurrent(o).fold(function(){nt.console.log("Can not find form")},function(t){bl.setValue(t,{hex:st.from(e[1]).getOr("")}),Gw.getField(t,"hex").each(function(t){Lo(t,so())})})}}}),Zw()])}}),dropzone:KC(function(t,n){return dS(t,n.shared.providers)}),grid:KC(function(t,n){return e=t,o=n.shared,{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+e.columns+"col"]},components:V(e.items,o.interpreter)};var e,o}),selectbox:KC(function(t,n){return e=t,o=n.shared.providers,r=V(e.items,function(t){return{text:o.translate(t.text),value:t.value}}),i=e.label.map(function(t){return Iy(t,o)}),u=ey.parts().field({dom:{},selectAttributes:{size:e.size},options:r,factory:SC,selectBehaviours:za([ph.config({disabled:function(){return e.disabled||o.isReadOnly()}}),Dy.config({}),Lm("selectbox-change",[Jo(lo(),function(t,n){jo(t,Gb,{name:e.name})})])])}),a=1<e.size?st.none():st.some({dom:{tag:"div",classes:["tox-selectfield__icon-js"],innerHtml:Gg("chevron-down",o.icons)}}),c={dom:{tag:"div",classes:["tox-selectfield"]},components:ut([[u],a.toArray()])},ey.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:ut([i.toArray(),[c]]),fieldBehaviours:za([ph.config({disabled:function(){return e.disabled||o.isReadOnly()},onDisabled:function(t){ey.getField(t).each(ph.disable)},onEnabled:function(t){ey.getField(t).each(ph.enable)}}),Uh()])});var e,o,r,i,u,a,c}),sizeinput:KC(function(t,n){return Jk(t,n.shared.providers)}),urlinput:KC(function(t,n){return YC(t,n,n.urlinput)}),customeditor:KC(function(e){var o=ce(st.none()),n=Ug({dom:{tag:e.tag}}),r=ce(st.none());return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:za([Lm("editor-foo-events",[or(function(t){n.getOpt(t).each(function(n){var t;t=e,(Object.prototype.hasOwnProperty.call(t,"init")?e.init(n.element().dom()):rS.load(e.scriptId,e.scriptUrl).then(function(t){return t(n.element().dom(),e.settings)})).then(function(n){r.get().each(function(t){n.setValue(t)}),r.set(st.none()),o.set(st.some(n))})})})]),bl.config({store:{mode:"manual",getValue:function(){return o.get().fold(function(){return r.get().getOr("")},function(t){return t.getValue()})},setValue:function(t,n){o.get().fold(function(){r.set(st.some(n))},function(t){return t.setValue(n)})}}}),Zw()]),components:[n.asSpec()]}}),htmlpanel:KC(function(t){return"presentation"===t.presets?Ub.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html}}):Ub.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html,attributes:{role:"document"}},containerBehaviours:za([Dy.config({}),Xm.config({})])})}),imagetools:KC(function(t,n){return wC(t,n.shared.providers)}),table:KC(function(t,n){return e=t,o=n.shared.providers,u=function(t){return{dom:{tag:"th",innerHtml:o.translate(t)}}},a=function(t){return{dom:{tag:"td",innerHtml:o.translate(t)}}},c=function(t){return{dom:{tag:"tr"},components:V(t,a)}},{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(i=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:V(i,u)}]}),(r=e.cells,{dom:{tag:"tbody"},components:V(r,c)})],behaviours:za([Dy.config({}),Xm.config({})])};var e,o,r,i,u,a,c}),panel:KC(function(t,n){return o=n,{dom:{tag:"div",classes:(e=t).classes},components:V(e.items,o.shared.interpreter)};var e,o})},$C={field:function(t,n){return n}},QC=function(n,t,e){var o=Ct(e,{shared:{interpreter:function(t){return ZC(n,t,o)}}});return ZC(n,t,o)},ZC=function(n,e,o){return bt(JC,e.type).fold(function(){return nt.console.error('Unknown factory type "'+e.type+'", defaulting to container: ',e),e},function(t){return t(n,e,o)})},tO={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},nO=function(t,n,e){var o=wc(-12,12,tO),r={maxHeightFunction:hc()};return function(){return e()?{anchor:"node",root:Ti(ar(t())),node:st.from(t()),bubble:o,layouts:{onRtl:function(){return[Pg]},onLtr:function(){return[Hg]}},overrides:r}:{anchor:"hotspot",hotspot:n(),bubble:o,layouts:{onRtl:function(){return[wa]},onLtr:function(){return[Sa]}},overrides:r}}},eO=function(t,n,e){return function(){return e()?{anchor:"node",root:Ti(ar(t())),node:st.from(t()),layouts:{onRtl:function(){return[zg]},onLtr:function(){return[zg]}}}:{anchor:"hotspot",hotspot:n(),layouts:{onRtl:function(){return[_a]},onLtr:function(){return[_a]}}}}},oO=function(t,n,e){var o,r,i,u=Vh(t),a=function(){return le.fromDom(t.getBody())},c=function(){return le.fromDom(t.getContentAreaContainer())},s=function(){return u||!e()};return{inlineDialog:nO(c,n,s),banner:eO(c,n,s),cursor:(r=t,function(){return{anchor:"selection",root:i(),getSelection:function(){var t=r.selection.getRng();return st.some(Rc.range(le.fromDom(t.startContainer),t.startOffset,le.fromDom(t.endContainer),t.endOffset))}}}),node:(o=i=a,function(t){return{anchor:"node",root:o(),node:t}})}},rO=function(t){return{colorPicker:function(t,n){Uv(r)(t,n)},hasCustomColors:function(){return Mv(o)},getColors:function(){return Av(e)},getColorCols:(n=e=o=r=t,function(){return Vv(n)})};var n,e,o,r},iO=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],uO=function(t){return N(t,function(t,n){if(yt(n,"items")){var e=uO(n.items);return{customFormats:t.customFormats.concat(e.customFormats),formats:t.formats.concat([{title:n.title,items:e.formats}])}}if(yt(n,"inline")||yt(n,"block")||yt(n,"selector")){var o="custom-"+n.title.toLowerCase();return{customFormats:t.customFormats.concat([{name:o,format:n}]),formats:t.formats.concat([{title:n.title,format:o,icon:n.icon}])}}return et(et({},t),{formats:t.formats.concat(n)})},{customFormats:[],formats:[]})},aO=function(i){return t=i,st.from(t.getParam("style_formats")).filter(C).map(function(t){var n,e,o,r=(n=i,e=uO(t),o=function(t){it(t,function(t){n.formatter.has(t.name)||n.formatter.register(t.name,t.format)})},n.formatter?o(e.customFormats):n.on("init",function(){o(e.customFormats)}),e.formats);return i.getParam("style_formats_merge",!1,"boolean")?iO.concat(r):r}).getOr(iO);var t},cO=function(t,n,e){var o={type:"formatter",isSelected:n(t.format),getStylePreview:e(t.format)};return Ct(t,o)},sO=function(a,t,c,s){var l=function(t){return V(t,function(t){var n,e,o,r,i=lt(t);if(xt(t,"items")){var u=l(t.items);return Ct(Ct(t,{type:"submenu"}),{getStyleItems:function(){return u}})}return xt(t,"format")?cO(t,c,s):1===i.length&&F(i,"title")?Ct(t,{type:"separator"}):(e=Nr((n=t).title),o={type:"formatter",format:e,isSelected:c(e),getStylePreview:s(e)},r=Ct(n,o),a.formatter.register(e,r),r)})};return l(t)},lO=uC.trim,fO=function(n){return function(t){if(t&&1===t.nodeType){if(t.contentEditable===n)return!0;if(t.getAttribute("data-mce-contenteditable")===n)return!0}return!1}},dO=fO("true"),mO=fO("false"),gO=function(t,n,e,o,r){return{type:t,title:n,url:e,level:o,attach:r}},pO=function(t){return t.innerText||t.textContent},hO=function(t){return(n=t)&&"A"===n.nodeName&&(n.id||n.name)!==undefined&&bO(t);var n},vO=function(t){return t&&/^(H[1-6])$/.test(t.nodeName)},bO=function(t){return function(t){for(;t=t.parentNode;){var n=t.contentEditable;if(n&&"inherit"!==n)return dO(t)}return!1}(t)&&!mO(t)},yO=function(t){return vO(t)&&bO(t)},xO=function(t){var n,e,o=(n=t).id?n.id:Nr("h");return gO("header",pO(t),"#"+o,vO(e=t)?parseInt(e.nodeName.substr(1),10):0,function(){t.id=o})},wO=function(t){var n=t.id||t.name,e=pO(t);return gO("anchor",e||"#"+n,"#"+n,0,Z)},SO=function(t){var n,e;return n="h1,h2,h3,h4,h5,h6,a:not([href])",e=t,V(Yc(le.fromDom(e),n),function(t){return t.dom()})},kO=function(t){return 0<lO(t.title).length},CO=function(t){var n=SO(t);return P(V(P(n,yO),xO).concat(V(P(n,hO),wO)),kO)},OO="tinymce-url-history",_O=function(t){return S(t)&&/^https?/.test(t)},TO=function(t){return k(t)&&ht(t,function(t){return!(C(n=t)&&n.length<=5&&W(n,_O));var n}).isNone()},EO=function(){var t,n=_v.getItem(OO);if(null===n)return{};try{t=JSON.parse(n)}catch(e){if(e instanceof SyntaxError)return nt.console.log("Local storage "+OO+" was not valid JSON",e),{};throw e}return TO(t)?t:(nt.console.log("Local storage "+OO+" was not valid format",t),{})},BO=function(t){var n=EO();return Object.prototype.hasOwnProperty.call(n,t)?n[t]:[]},DO=function(n,t){if(_O(n)){var e=EO(),o=Object.prototype.hasOwnProperty.call(e,t)?e[t]:[],r=P(o,function(t){return t!==n});e[t]=[n].concat(r).slice(0,5),function(t){if(!TO(t))throw new Error("Bad format for history:\n"+JSON.stringify(t));_v.setItem(OO,JSON.stringify(t))}(e)}},MO=Object.prototype.hasOwnProperty,AO=function(t){return!!t},FO=function(t){return dt(uC.makeMap(t,/[, ]/),AO)},IO=function(t,n,e){var o,r,i=(o=t,r=n,(MO.call(o,r)?st.some(o[r]):st.none()).getOr(e));return S(i)?st.some(i):st.none()},RO=function(t){return st.some(t.file_picker_callback).filter(T)},VO=function(t,n){var e,o,r,i,u=(e=t,o=st.some(e.file_picker_types).filter(AO),r=st.some(e.file_browser_callback_types).filter(AO),i=o.or(r).map(FO),RO(e).fold(function(){return!1},function(t){return i.fold(function(){return!0},function(t){return 0<lt(t).length&&t})}));return O(u)?u?RO(t):st.none():u[n]?RO(t):st.none()},HO=function(n){return{getHistory:BO,addToHistory:DO,getLinkInformation:function(){return!1===(t=n).settings.typeahead_urls?st.none():st.some({targets:CO(t.getBody()),anchorTop:IO(t.settings,"anchor_top","#top").getOrUndefined(),anchorBottom:IO(t.settings,"anchor_bottom","#bottom").getOrUndefined()});var t},getValidationHandler:function(){return t=n,st.from(t.settings.file_picker_validator_handler).filter(T).orThunk(function(){return st.from(t.settings.filepicker_validator_handler).filter(T)});var t},getUrlPicker:function(t){return i=t,VO((r=n).settings,i).map(function(o){return function(n){return hy(function(e){var t=et({filetype:i,fieldname:n.fieldname},st.from(n.meta).getOr({}));o.call(r,function(t,n){if(!S(t))throw new Error("Expected value to be string");if(n!==undefined&&!k(n))throw new Error("Expected meta to be a object");e({value:t,meta:n})},n.value,t)})}});var r,i}}},PO=function(t,n,e){var o,r,i=ce(!1),u={isPositionedAtTop:function(){return"top"===o.get()},getDockingMode:(o=ce(Ih(n)?"bottom":"top")).get,setDockingMode:o.set},a={shared:{providers:{icons:function(){return n.ui.registry.getAll().icons},menuItems:function(){return n.ui.registry.getAll().menuItems},translate:Vp.translate,isReadOnly:function(){return n.mode.isReadOnly()}},interpreter:function(t){return ZC($C,t,a)},anchors:oO(n,e,u.isPositionedAtTop),header:u,getSink:function(){return ot.value(t)}},urlinput:HO(n),styleselect:function(o){var r=function(t){return function(){return o.formatter.match(t)}},i=function(n){return function(){var t=o.formatter.get(n);return t!==undefined?st.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:o.dom.parseStyle(o.formatter.getCssText(n))}):st.none()}},u=function(t){var n=t.items;return n!==undefined&&0<n.length?U(n,u):[t.format]},a=ce([]),c=ce([]),e=ce([]),s=ce([]),l=ce(!1);o.on("PreInit",function(t){var n=aO(o),e=sO(o,n,r,i);a.set(e),c.set(U(e,u))}),o.on("addStyleModifications",function(t){var n=sO(o,t.items,r,i);e.set(n),l.set(t.replace),s.set(U(n,u))});return{getData:function(){var t=l.get()?[]:a.get(),n=e.get();return t.concat(n)},getFlattenedKeys:function(){var t=l.get()?[]:c.get(),n=s.get();return t.concat(n)}}}(n),colorinput:rO(n),dialog:{isDraggableModal:(r=n,function(){return r.getParam("draggable_modal",!1,"boolean")})},isContextMenuOpen:function(){return i.get()},setContextMenuState:function(t){return i.set(t)}};return a},zO=at(function(t,n){var e,o,r;e=t,o=Math.floor(n),r=au.max(e,o,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]),Di(e,"max-width",r+"px")}),NO="contexttoolbar-hide",LO=at([zn("items"),Ku(["itemSelector"]),yl("tgroupBehaviours",[Rm])]),jO=at([$l({name:"items",unit:"item"})]),UO=_f({name:"ToolbarGroup",configFields:LO(),partFields:jO(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,behaviours:wl(t.tgroupBehaviours,[Rm.config({mode:"flow",selector:t.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),WO=at([zn("dom"),Zn("shell",!0),yl("toolbarBehaviours",[Nm])]),GO=at([Jl({name:"groups",overrides:function(){return{behaviours:za([Nm.config({})])}}})]),XO=_f({name:"Toolbar",configFields:WO(),partFields:GO(),factory:function(n,t,e,o){var r=function(t){return n.shell?st.some(t):sf(t,n,"groups")},i=n.shell?{behaviours:[Nm.config({})],components:[]}:{behaviours:[],components:t};return{uid:n.uid,dom:n.dom,components:i.components,behaviours:wl(n.toolbarBehaviours,i.behaviours),apis:{setGroups:function(t,n){r(t).fold(function(){throw nt.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(t){Nm.set(t,n)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)}}}),YO=function(t,n,e){return{within:at(t),extra:at(n),withinWidth:at(e)}},qO=function(t,n,o){var e,r=(e=function(t,n){var e=o(t);return st.some({element:at(t),start:at(n),finish:at(n+e),width:at(e)})},N(t,function(n,t){return e(t,n.len).fold(at(n),function(t){return{len:t.finish(),list:n.list.concat([t])}})},{len:0,list:[]}).list),i=P(r,function(t){return t.finish()<=n}),u=z(i,function(t,n){return t+n.width()},0),a=r.slice(i.length);return{within:at(i),extra:at(a),withinWidth:at(u)}},KO=function(t){return V(t,function(t){return t.element()})},JO=function(t,n,e,o){var r,i,u,a,c,s,l,f,d,m=(0===(r=qO(n,t,e)).extra().length?st.some(r):st.none()).getOrThunk(function(){return qO(n,t-e(o),e)}),g=m.within(),p=m.extra(),h=m.withinWidth();return 1===p.length&&p[0].width()<=e(o)?(l=p,f=h,d=KO(g.concat(l)),YO(d,[],f)):1<=p.length?(u=p,a=o,c=h,s=KO(g).concat([a]),YO(s,KO(u),c)):(i=h,YO(KO(g),[],i))},$O=function(t,n){var e=V(n,function(t){return $i(t)});XO.setGroups(t,e)},QO=function(t,n,e){var o=lf(t,n,"primary"),r=Hy.getCoupled(t,"overflowGroup");Di(o.element(),"visibility","hidden");var i=n.builtGroups.get().concat([r]),u=Q(i,function(n){return $a(n.element()).bind(function(t){return n.getSystem().getByDom(t).toOption()})});e([]),$O(o,i);var a=cu(o.element()),c=JO(a,n.builtGroups.get(),function(t){return cu(t.element())},r);0===c.extra().length?(Nm.remove(o,r),e([])):($O(o,c.within()),e(c.extra())),Hi(o.element(),"visibility"),Pi(o.element()),u.each(Xm.focus)},ZO=at([yl("splitToolbarBehaviours",[Hy]),ae("builtGroups",function(){return ce([])})]),t_=at([Ku(["overflowToggledClass"]),$n("getOverflowBounds"),zn("lazySink"),ae("overflowGroups",function(){return ce([])})].concat(ZO())),n_=at([ql({factory:XO,schema:WO(),name:"primary"}),Kl({schema:WO(),name:"overflow"}),Kl({name:"overflow-button"}),Kl({name:"overflow-group"})]),e_=at([Ku(["toggledClass"]),zn("lazySink"),Un("fetch"),$n("getBounds"),Qn("fireDismissalEventInstead",[Zn("event",Fo())]),Tc()]),o_=at([Kl({name:"button",overrides:function(t){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:za([rg.config({toggleClass:t.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),Kl({factory:XO,schema:WO(),name:"toolbar",overrides:function(n){return{toolbarBehaviours:za([Rm.config({mode:"cyclic",onEscape:function(t){return sf(t,n,"button").each(Xm.focus),st.none()}})])}}})]),r_=function(t,n){var e=Hy.getCoupled(t,"toolbarSandbox");Qs.isOpen(e)?Qs.close(e):Qs.open(e,n.toolbar())},i_=function(t,n,e,o){var r=e.getBounds.map(function(t){return t()}),i=e.lazySink(t).getOrDie();Ds.positionWithinBounds(i,{anchor:"hotspot",hotspot:t,layouts:o,overrides:{maxWidthFunction:zO()}},n,r)},u_=function(t,n,e,o,r){XO.setGroups(n,r),i_(t,n,e,o),rg.on(t)},a_=_f({name:"FloatingToolbarButton",factory:function(u,t,a,n){return et(et({},jg.sketch(et(et({},n.button()),{action:function(t){r_(t,n)},buttonBehaviours:kl({dump:n.button().buttonBehaviours},[Hy.config({others:{toolbarSandbox:function(t){return o=t,e=a,r=u,{dom:{tag:"div",attributes:{id:(i=Fu()).id}},behaviours:za([Rm.config({mode:"special",onEscape:function(t){return Qs.close(t),st.some(!0)}}),Qs.config({onOpen:function(t,n){r.fetch().get(function(t){u_(o,n,r,e.layouts,t),i.link(o.element()),Rm.focusIn(n)})},onClose:function(){rg.off(o),Xm.focus(o),i.unlink(o.element())},isPartOf:function(t,n,e){return Ru(n,e)||Ru(o,e)},getAttachPoint:function(){return r.lazySink(o).getOrDie()}}),Ya.config({channels:et(et({},ol(et({isExtraPart:c},r.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),il({doReposition:function(){Qs.getState(Hy.getCoupled(o,"toolbarSandbox")).each(function(t){i_(o,t,r,e.layouts)})}}))})])};var o,e,r,i}}})])}))),{apis:{setGroups:function(n,e){Qs.getState(Hy.getCoupled(n,"toolbarSandbox")).each(function(t){u_(n,t,u,a.layouts,e)})},reposition:function(n){Qs.getState(Hy.getCoupled(n,"toolbarSandbox")).each(function(t){i_(n,t,u,a.layouts)})},toggle:function(t){r_(t,n)},getToolbar:function(t){return Qs.getState(Hy.getCoupled(t,"toolbarSandbox"))}}})},configFields:e_(),partFields:o_(),apis:{setGroups:function(t,n,e){t.setGroups(n,e)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},getToolbar:function(t,n){return t.getToolbar(n)}}}),c_=function(t){return V(t,function(t){return $i(t)})},s_=function(t,e,o){QO(t,o,function(n){o.overflowGroups.set(n),e.getOpt(t).each(function(t){a_.setGroups(t,c_(n))})})},l_=_f({name:"SplitFloatingToolbar",configFields:t_(),partFields:n_(),factory:function(e,t,n,o){var r=Ug(a_.sketch({fetch:function(){return hy(function(t){t(c_(e.overflowGroups.get()))})},layouts:{onLtr:function(){return[Sa,wa]},onRtl:function(){return[wa,Sa]},onBottomLtr:function(){return[Ca,ka]},onBottomRtl:function(){return[ka,Ca]}},getBounds:n.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:o["overflow-button"](),toolbar:o.overflow()}}));return{uid:e.uid,dom:e.dom,components:t,behaviours:wl(e.splitToolbarBehaviours,[Hy.config({others:{overflowGroup:function(){return UO.sketch(et(et({},o["overflow-group"]()),{items:[r.asSpec()]}))}}})]),apis:{setGroups:function(t,n){e.builtGroups.set(V(n,t.getSystem().build)),s_(t,r,e)},refresh:function(t){return s_(t,r,e)},toggle:function(t){r.getOpt(t).each(function(t){a_.toggle(t)})},reposition:function(t){r.getOpt(t).each(function(t){a_.reposition(t)})},getOverflow:function(t){return r.getOpt(t).bind(function(t){return a_.getToolbar(t)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},getOverflow:function(t,n){return t.getOverflow(n)}}}),f_=function(n,t){return t.getAnimationRoot.fold(function(){return n.element()},function(t){return t(n)})},d_=function(t){return t.dimension.property},m_=function(t,n){return t.dimension.getDimension(n)},g_=function(t,n){var e=f_(t,n);ki(e,[n.shrinkingClass,n.growingClass])},p_=function(t,n){xi(t.element(),n.openClass),bi(t.element(),n.closedClass),Di(t.element(),d_(n),"0px"),Pi(t.element())},h_=function(t,n){xi(t.element(),n.closedClass),bi(t.element(),n.openClass),Hi(t.element(),d_(n))},v_=function(t,n,e,o){e.setCollapsed(),Di(t.element(),d_(n),m_(n,t.element())),Pi(t.element()),g_(t,n),p_(t,n),n.onStartShrink(t),n.onShrunk(t)},b_=function(t,n,e,o){var r=o.getOrThunk(function(){return m_(n,t.element())});e.setCollapsed(),Di(t.element(),d_(n),r),Pi(t.element());var i=f_(t,n);xi(i,n.growingClass),bi(i,n.shrinkingClass),p_(t,n),n.onStartShrink(t)},y_=function(t,n,e){var o=m_(n,t.element());("0px"===o?v_:b_)(t,n,e,st.some(o))},x_=function(t,n,e){var o=f_(t,n),r=wi(o,n.shrinkingClass),i=m_(n,t.element());h_(t,n);var u=m_(n,t.element());(r?function(){Di(t.element(),d_(n),i),Pi(t.element())}:function(){p_(t,n)})(),xi(o,n.shrinkingClass),bi(o,n.growingClass),h_(t,n),Di(t.element(),d_(n),u),e.setExpanded(),n.onStartGrow(t)},w_=function(t,n,e){var o=f_(t,n);return!0===wi(o,n.growingClass)},S_=function(t,n,e){var o=f_(t,n);return!0===wi(o,n.shrinkingClass)},k_=/* */Object.freeze({__proto__:null,refresh:function(t,n,e){if(e.isExpanded()){Hi(t.element(),d_(n));var o=m_(n,t.element());Di(t.element(),d_(n),o)}},grow:function(t,n,e){e.isExpanded()||x_(t,n,e)},shrink:function(t,n,e){e.isExpanded()&&y_(t,n,e)},immediateShrink:function(t,n,e){e.isExpanded()&&v_(t,n,e,st.none())},hasGrown:function(t,n,e){return e.isExpanded()},hasShrunk:function(t,n,e){return e.isCollapsed()},isGrowing:w_,isShrinking:S_,isTransitioning:function(t,n,e){return!0===w_(t,n)||!0===S_(t,n)},toggleGrow:function(t,n,e){(e.isExpanded()?y_:x_)(t,n,e)},disableTransitions:g_}),C_=/* */Object.freeze({__proto__:null,exhibit:function(t,n,e){var o=n.expanded;return ii(o?{classes:[n.openClass],styles:{}}:{classes:[n.closedClass],styles:Kt(n.dimension.property,"0px")})},events:function(e,o){return Yo([er(mo(),function(t,n){n.event().raw().propertyName===e.dimension.property&&(g_(t,e),o.isExpanded()&&Hi(t.element(),e.dimension.property),(o.isExpanded()?e.onGrown:e.onShrunk)(t))})])}}),O_=[zn("closedClass"),zn("openClass"),zn("shrinkingClass"),zn("growingClass"),Yn("getAnimationRoot"),$u("onShrunk"),$u("onStartShrink"),$u("onGrown"),$u("onStartGrow"),Zn("expanded",!1),Nn("dimension",Bn("property",{width:[na("property","width"),na("getDimension",function(t){return cu(t)+"px"})],height:[na("property","height"),na("getDimension",function(t){return tu(t)+"px"})]}))],__=La({fields:O_,name:"sliding",active:C_,apis:k_,state:/* */Object.freeze({__proto__:null,init:function(t){var n=ce(t.expanded);return oi({isExpanded:function(){return!0===n.get()},isCollapsed:function(){return!1===n.get()},setCollapsed:g(n.set,!1),setExpanded:g(n.set,!0),readState:function(){return"expanded: "+n.get()}})}})}),T_=at([Ku(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),$u("onOpened"),$u("onClosed")].concat(ZO())),E_=at([ql({factory:XO,schema:WO(),name:"primary"}),ql({factory:XO,schema:WO(),name:"overflow",overrides:function(n){return{toolbarBehaviours:za([__.config({dimension:{property:"height"},closedClass:n.markers.closedClass,openClass:n.markers.openClass,shrinkingClass:n.markers.shrinkingClass,growingClass:n.markers.growingClass,onShrunk:function(t){sf(t,n,"overflow-button").each(function(t){rg.off(t),Xm.focus(t)}),n.onClosed(t)},onGrown:function(t){Rm.focusIn(t),n.onOpened(t)},onStartGrow:function(t){sf(t,n,"overflow-button").each(rg.on)}}),Rm.config({mode:"acyclic",onEscape:function(t){return sf(t,n,"overflow-button").each(Xm.focus),st.some(!0)}})])}}}),Kl({name:"overflow-button",overrides:function(t){return{buttonBehaviours:za([rg.config({toggleClass:t.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),Kl({name:"overflow-group"})]),B_=function(t,n){sf(t,n,"overflow").each(function(e){QO(t,n,function(t){var n=V(t,function(t){return $i(t)});XO.setGroups(e,n)}),sf(t,n,"overflow-button").each(function(t){__.hasGrown(e)&&rg.on(t)}),__.refresh(e)})},D_=_f({name:"SplitSlidingToolbar",configFields:T_(),partFields:E_(),factory:function(o,t,n,e){var r="alloy.toolbar.toggle";return{uid:o.uid,dom:o.dom,components:t,behaviours:wl(o.splitToolbarBehaviours,[Hy.config({others:{overflowGroup:function(n){return UO.sketch(et(et({},e["overflow-group"]()),{items:[jg.sketch(et(et({},e["overflow-button"]()),{action:function(t){Lo(n,r)}}))]}))}}}),Lm("toolbar-toggle-events",[Jo(r,function(n){sf(n,o,"overflow").each(function(t){B_(n,o),__.toggleGrow(t)})})])]),apis:{setGroups:function(t,n){var e;e=V(n,t.getSystem().build),o.builtGroups.set(e),B_(t,o)},refresh:function(t){return B_(t,o)},toggle:function(t){var n,e;sf(n=t,e=o,"overflow").each(function(t){B_(n,e),__.toggleGrow(t)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},toggle:function(t,n){t.toggle(n)}}}),M_=at(Nr("toolbar-height-change")),A_=function(t){var n=t.title.fold(function(){return{}},function(t){return{attributes:{title:t}}});return{dom:et({tag:"div",classes:["tox-toolbar__group"]},n),components:[UO.parts().items({})],items:t.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:za([Dy.config({}),Xm.config({})])}},F_=function(t){return UO.sketch(A_(t))},I_=function(e,t){var n=or(function(t){var n=V(e.initGroups,F_);XO.setGroups(t,n)});return za([Yh(e.providers.isReadOnly),Uh(),Rm.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Lm("toolbar-events",[n])])},R_=function(t){var n=t.cyclicKeying?"cyclic":"acyclic";return{uid:t.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":A_({title:st.none(),items:[]}),"overflow-button":Rk({name:"more",icon:st.some("more-drawer"),disabled:!1,tooltip:st.some("More..."),primary:!1,borderless:!1},st.none(),t.providers)},splitToolbarBehaviours:I_(t,n)}},V_=function(i){var t=R_(i),n=l_.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return l_.sketch(et(et({},t),{lazySink:i.getSink,getOverflowBounds:function(){var t=i.moreDrawerData.lazyHeader().element(),n=Su(t),e=cr(t),o=Su(e),r=Math.max(e.dom().scrollHeight,o.height);return xu(n.x+4,o.y,n.width-8,r)},parts:et(et({},t.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:i.attributes}}}),components:[n],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))},H_=function(t){var n=D_.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),e=D_.parts().overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=R_(t);return D_.sketch(et(et({},o),{components:[n,e],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(t){t.getSystem().broadcastOn([M_()],{type:"opened"})},onClosed:function(t){t.getSystem().broadcastOn([M_()],{type:"closed"})}}))},P_=function(t){var n=t.cyclicKeying?"cyclic":"acyclic";return XO.sketch({uid:t.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(t.type===Kg.scrolling?["tox-toolbar--scrolling"]:[])},components:[XO.parts().groups({})],toolbarBehaviours:I_(t,n)})},z_=[re("disabled",!1),Jn("tooltip"),Jn("icon"),Jn("text"),ie("onSetup",function(){return Z})],N_=ln([Ln("type"),Un("onAction")].concat(z_)),L_=function(t){return Cn("toolbarbutton",N_,t)},j_=[Jn("text"),Jn("tooltip"),Jn("icon"),Un("fetch"),ie("onSetup",function(){return Z})],U_=ln(b([Ln("type")],j_)),W_=function(t){return Cn("menubutton",U_,t)},G_=ln([Ln("type"),Jn("tooltip"),Jn("icon"),Jn("text"),$n("select"),Un("fetch"),ie("onSetup",function(){return Z}),oe("presets","normal",["normal","color","listpreview"]),Zn("columns",1),Un("onAction"),Un("onItemAction")]),X_=[re("active",!1)].concat(z_),Y_=ln(X_.concat([Ln("type"),Un("onAction")])),q_=function(t){return Cn("ToggleButton",Y_,t)},K_=ln([Ln("type"),Nn("items",(vC=[xn([Ln("name"),Xn("items",Fn)]),Fn],{extract:function(t,n,e){for(var o=[],r=0,i=vC;r<i.length;r++){var u=i[r].extract(t,n,e);if(u.stype===a.Value)return u;o.push(u)}return tn(o)},toString:function(){return"oneOf("+V(vC,function(t){return t.toString()}).join(", ")+")"}}))].concat(z_)),J_=[ie("predicate",function(){return!1}),oe("scope","node",["node","editor"]),oe("position","selection",["node","selection","line"])],$_=z_.concat([Zn("type","contextformbutton"),Zn("primary",!1),Un("onAction"),ae("original",ct)]),Q_=X_.concat([Zn("type","contextformbutton"),Zn("primary",!1),Un("onAction"),ae("original",ct)]),Z_=z_.concat([Zn("type","contextformbutton")]),tT=X_.concat([Zn("type","contextformtogglebutton")]),nT=Bn("type",{contextformbutton:$_,contextformtogglebutton:Q_}),eT=ln([Zn("type","contextform"),ie("initValue",function(){return""}),Jn("label"),Xn("commands",nT),qn("launch",Bn("type",{contextformbutton:Z_,contextformtogglebutton:tT}))].concat(J_)),oT=ln([Zn("type","contexttoolbar"),Ln("items")].concat(J_)),rT=/* */Object.freeze({__proto__:null,getState:function(t,n,e){return e}}),iT=/* */Object.freeze({__proto__:null,events:function(i,u){var r=function(o,r){i.updateState.each(function(t){var n=t(o,r);u.set(n)}),i.renderComponents.each(function(t){var n=t(r,u.get()),e=V(n,o.getSystem().build);Is(o,e)})};return Yo([Jo(yo(),function(t,n){var e=n,o=i.channel;F(e.channels(),o)&&r(t,e.data())}),or(function(n,t){i.initialData.each(function(t){r(n,t)})})])}}),uT=/* */Object.freeze({__proto__:null,init:function(){var n=ce(st.none());return{readState:function(){return n.get().fold(function(){return"none"},function(t){return t})},get:function(){return n.get()},set:function(t){return n.set(t)},clear:function(){return n.set(st.none())}}}}),aT=[zn("channel"),Yn("renderComponents"),Yn("updateState"),Yn("initialData")],cT=La({fields:aT,name:"reflecting",active:iT,apis:rT,state:uT}),sT=at([zn("toggleClass"),zn("fetch"),Zu("onExecute"),Zn("getHotspot",st.some),Zn("getAnchorOverrides",at({})),Tc(),Zu("onItemExecute"),Yn("lazySink"),zn("dom"),$u("onOpen"),yl("splitDropdownBehaviours",[Hy,Rm,Xm]),Zn("matchWidth",!1),Zn("useMinWidth",!1),Zn("eventOrder",{}),Yn("role")].concat(Ky())),lT=ql({factory:jg,schema:[zn("dom")],name:"arrow",defaults:function(){return{buttonBehaviours:za([Xm.revoke()])}},overrides:function(n){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(n.uid).each(Uo)},buttonBehaviours:za([rg.config({toggleOnExecute:!1,toggleClass:n.toggleClass})])}}}),fT=ql({factory:jg,schema:[zn("dom")],name:"button",defaults:function(){return{buttonBehaviours:za([Xm.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(e.uid).each(function(t){e.onExecute(t,n)})}}}}),dT=at([lT,fT,Jl({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[zn("text")],name:"aria-descriptor"}),Kl({schema:[qu()],name:"menu",defaults:function(o){return{onExecute:function(n,e){n.getSystem().getByUid(o.uid).each(function(t){o.onItemExecute(t,n,e)})}}}}),zy()]),mT=_f({name:"SplitDropdown",configFields:sT(),partFields:dT(),factory:function(o,t,n,e){var r=function(t){Mf.getCurrent(t).each(function(t){jf.highlightFirst(t),Rm.focusIn(t)})},i=function(t){Uy(o,function(t){return t},t,e,r,sy.HighlightFirst).get(Z)},u=function(t){var n=lf(t,o,"button");return Uo(n),st.some(!0)},a=et(et({},Yo([or(function(e,t){sf(e,o,"aria-descriptor").each(function(t){var n=Nr("aria");Br(t.element(),"id",n),Br(e.element(),"aria-describedby",n)})})])),ug(st.some(i))),c={repositionMenus:function(t){rg.isOn(t)&&qy(t)}};return{uid:o.uid,dom:o.dom,components:t,apis:c,eventOrder:et(et({},o.eventOrder),{"alloy.execute":["disabling","toggling","alloy.base.behaviour"]}),events:a,behaviours:wl(o.splitDropdownBehaviours,[Hy.config({others:{sandbox:function(t){var n=lf(t,o,"arrow");return Yy(o,t,{onOpen:function(){rg.on(n),rg.on(t)},onClose:function(){rg.off(n),rg.off(t)}})}}}),Rm.config({mode:"special",onSpace:u,onEnter:u,onDown:function(t){return i(t),st.some(!0)}}),Xm.config({}),rg.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:function(t,n){return t.repositionMenus(n)}}}),gT=function(n){return{isDisabled:function(){return ph.isDisabled(n)},setDisabled:function(t){return ph.set(n,t)}}},pT=function(n){return{setActive:function(t){rg.set(n,t)},isActive:function(){return rg.isOn(n)},isDisabled:function(){return ph.isDisabled(n)},setDisabled:function(t){return ph.set(n,t)}}},hT=function(t,n){return t.map(function(t){return{"aria-label":n.translate(t),title:n.translate(t)}}).getOr({})},vT=Nr("focus-button"),bT=["checklist","ordered-list"],yT=["indent","outdent","table-insert-column-after","table-insert-column-before","unordered-list"],xT=function(n,e,t,o,r,i){var u,a=function(t){return Vp.isRtl()&&F(bT,t)?t+"-rtl":t},c=Vp.isRtl()&&n.exists(function(t){return F(yT,t)});return{dom:{tag:"button",classes:["tox-tbtn"].concat(e.isSome()?["tox-tbtn--select"]:[]).concat(c?["tox-tbtn__icon-rtl"]:[]),attributes:hT(t,i)},components:Zh([n.map(function(t){return vk(a(t),i.icons)}),e.map(function(t){return yk(t,"tox-tbtn",i)})]),eventOrder:((u={})[to()]=["focusing","alloy.base.behaviour","common-button-display-events"],u),buttonBehaviours:za([Yh(i.isReadOnly),Uh(),Lm("common-button-display-events",[Jo(to(),function(t,n){n.event().prevent(),Lo(t,vT)})])].concat(o.map(function(t){return cT.config({channel:t,initialData:{icon:n,text:e},renderComponents:function(t,n){return Zh([t.icon.map(function(t){return vk(a(t),i.icons)}),t.text.map(function(t){return yk(t,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}},wT=function(t,n,e){var o,r=ce(Z),i=xT(t.icon,t.text,t.tooltip,st.none(),st.none(),e);return jg.sketch({dom:i.dom,components:i.components,eventOrder:wk,buttonBehaviours:za([Lm("toolbar-button-events",[(o={onAction:t.onAction,getApi:n.getApi},ur(function(n,t){qh(o,n)(function(t){jo(n,xk,{buttonApi:t}),o.onAction(t)})})),Kh(n,r),Jh(n,r)]),Yh(function(){return t.disabled||e.isReadOnly()}),Uh()].concat(n.toolbarButtonBehaviours))})},ST=function(t,n,e){return wT(t,{toolbarButtonBehaviours:[].concat(0<e.length?[Lm("toolbarButtonWith",e)]:[]),getApi:gT,onSetup:t.onSetup},n)},kT=function(t,n,e){return Ct(wT(t,{toolbarButtonBehaviours:[Nm.config({}),rg.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<e.length?[Lm("toolbarToggleButtonWith",e)]:[]),getApi:pT,onSetup:t.onSetup},n))},CT=function(n,t){var e,o,r,i,u=Nr("channel-update-split-dropdown-display"),a=function(e){return{isDisabled:function(){return ph.isDisabled(e)},setDisabled:function(t){return ph.set(e,t)},setIconFill:function(t,n){Mu(e.element(),'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){Br(t,"fill",n)})},setIconStroke:function(t,n){Mu(e.element(),'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){Br(t,"stroke",n)})},setActive:function(n){Br(e.element(),"aria-pressed",n),Mu(e.element(),"span").each(function(t){e.getSystem().getByDom(t).each(function(t){return rg.set(t,n)})})},isActive:function(){return Mu(e.element(),"span").exists(function(t){return e.getSystem().getByDom(t).exists(rg.isOn)})}}},c=ce(Z),s={getApi:a,onSetup:n.onSetup};return mT.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:et({"aria-pressed":!1},hT(n.tooltip,t.providers))},onExecute:function(t){n.onAction(a(t))},onItemExecute:function(t,n,e){},splitDropdownBehaviours:za([Xh(t.providers.isReadOnly),Uh(),Lm("split-dropdown-events",[Jo(vT,Xm.focus),Kh(s,c),Jh(s,c)]),Zy.config({})]),eventOrder:((e={})[Mo()]=["alloy.base.behaviour","split-dropdown-events"],e),toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:(o=a,r=n,i=t.providers,function(n){return hy(function(t){return r.fetch(t)}).map(function(t){return st.from(hb(Ct(nb(Nr("menu-value"),t,function(t){r.onItemAction(o(n),t)},r.columns,r.presets,$h.CLOSE_ON_EXECUTE,r.select.getOr(function(){return!1}),i),{movement:ob(r.columns,r.presets),menuBehaviours:Tp("auto"!==r.columns?[]:[or(function(o,t){Op(o,4,Gp(r.presets)).each(function(t){var n=t.numRows,e=t.numColumns;Rm.setGridSize(o,n,e)})})])})))})}),parts:{menu:Yv(0,n.columns,n.presets)},components:[mT.parts().button(xT(n.icon,n.text,st.none(),st.some(u),st.some([rg.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),mT.parts().arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:Gg("chevron-down",t.providers.icons)},buttonBehaviours:za([Xh(t.providers.isReadOnly),Uh()])}),mT.parts()["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})},OT=function(i,u){return Jo(xk,function(t,n){var e,o=i.get(t),r=(e=o,{hide:function(){return Lo(e,Co())},getValue:function(){return bl.getValue(e)}});u.onAction(r,n.event().buttonApi())})},_T=function(t,n,e){var o,r,i,u,a,c,s,l,f,d,m,g,p={backstage:{shared:{providers:e}}};return"contextformtogglebutton"===n.type?(s=t,f=p,(d=(l=n).original).primary,m=y(d,["primary"]),g=On(q_(et(et({},m),{type:"togglebutton",onAction:function(){}}))),kT(g,f.backstage.shared.providers,[OT(s,l)])):(o=t,i=p,(u=(r=n).original).primary,a=y(u,["primary"]),c=On(L_(et(et({},a),{type:"button",onAction:function(){}}))),ST(c,i.backstage.shared.providers,[OT(o,r)]))},TT=function(t,n){var e,o,r,i,u=t.label.fold(function(){return{}},function(t){return{"aria-label":t}}),a=Ug(uy.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:u,selectOnFocus:!0,inputBehaviours:za([Rm.config({mode:"special",onEnter:function(t){return c.findPrimary(t).map(function(t){return Uo(t),!0})},onLeft:function(t,n){return n.cut(),st.none()},onRight:function(t,n){return n.cut(),st.none()}})])})),c=(e=a,o=t.commands,r=n,i=V(o,function(t){return Ug(_T(e,t,r))}),{asSpecs:function(){return V(i,function(t){return t.asSpec()})},findPrimary:function(e){return Q(o,function(t,n){return t.primary?st.from(i[n]).bind(function(t){return t.getOpt(e)}).filter(x(ph.isDisabled)):st.none()})}});return[{title:st.none(),items:[a.asSpec()]},{title:st.none(),items:c.asSpecs()}]},ET=TT,BT=function(t,n){var e,o,r,i,u,a=hu(nt.window),c=wu(le.fromDom(t.getContentAreaContainer())),s=Ch(t)||Oh(t)||Th(t),l=(e=c,o=a,r=Math.max(o.x,e.x),i=e.right-r,u=o.width-(r-o.x),{x:r,width:Math.min(i,u)}),f=l.x,d=l.width;if(t.inline&&!s)return xu(f,a.y,d,a.height);var m=function(t,n,e,o){var r=le.fromDom(t.getContainer()),i=Mu(r,".tox-editor-header").getOr(r),u=wu(i),a=u.y>=n.bottom,c=o&&!a;if(t.inline&&c)return{y:Math.max(u.bottom,e.y),bottom:e.bottom};if(t.inline&&!c)return{y:e.y,bottom:Math.min(u.y,e.bottom)};var s=wu(r);return c?{y:Math.max(u.bottom,e.y),bottom:Math.min(s.bottom,e.bottom)}:{y:Math.max(s.y,e.y),bottom:Math.min(u.y,e.bottom)}}(t,c,a,n.header.isPositionedAtTop()),g=m.y,p=m.bottom;return xu(f,g,d,p-g)},DT=function(n,t){var e=P(t,function(t){return t.predicate(n.dom())}),o=H(e,function(t){return"contexttoolbar"===t.type});return{contextToolbars:o.pass,contextForms:o.fail}},MT=function(t,n,e){var o=DT(t,n);if(0<o.contextForms.length)return st.some({elem:t,toolbars:[o.contextForms[0]]});var r=DT(t,e);if(0<r.contextForms.length)return st.some({elem:t,toolbars:[r.contextForms[0]]});if(0<o.contextToolbars.length||0<r.contextToolbars.length){var i=function(t){if(t.length<=1)return t;var n=function(n){return I(t,function(t){return t.position===n})},e=function(n){return P(t,function(t){return t.position===n})},o=n("selection"),r=n("node");if(o||r){if(r&&o){var i=e("node"),u=V(e("selection"),function(t){return et(et({},t),{position:"node"})});return i.concat(u)}return e(o?"selection":"node")}return e("line")}(o.contextToolbars.concat(r.contextToolbars));return st.some({elem:t,toolbars:i})}return st.none()},AT=function(t,n,i){return t(n)?st.none():Ge(n,function(t){var n=DT(t,i.inNodeScope),e=n.contextToolbars,o=n.contextForms,r=0<o.length?o:function(t){if(t.length<=1)return t;var n=function(n){return L(t,function(t){return t.position===n})};return n("selection").orThunk(function(){return n("node")}).orThunk(function(){return n("line")}).map(function(t){return t.position}).fold(function(){return[]},function(n){return P(t,function(t){return t.position===n})})}(e);return 0<r.length?st.some({elem:t,toolbars:r}):st.none()},t)},FT=function(e,r){var t={},i=[],u=[],a={},c={},o=function(n,e){var o=On(Cn("ContextForm",eT,e));(t[n]=o).launch.map(function(t){a["form:"+n]=et(et({},e.launch),{type:"contextformtogglebutton"===t.type?"togglebutton":"button",onAction:function(){r(o)}})}),"editor"===o.scope?u.push(o):i.push(o),c[n]=o},s=function(n,e){Cn("ContextToolbar",oT,e).each(function(t){"editor"===e.scope?u.push(t):i.push(t),c[n]=t})},n=lt(e);return it(n,function(t){var n=e[t];"contextform"===n.type?o(t,n):"contexttoolbar"===n.type&&s(t,n)}),{forms:t,inNodeScope:i,inEditorScope:u,lookupTable:c,formNavigators:a}},IT=Nr("forward-slide"),RT=Nr("backward-slide"),VT=Nr("change-slide-event"),HT="tox-pop--resizing",PT=function(t,n,e){var u,a,r,c,i,o=e.dataset,s="basic"===o.type?function(){return V(o.data,function(t){return cO(t,e.isSelectedFor,e.getPreviewFor)})}:o.getData;return{items:(u=n,a=e,r=function(t,n,e,o){var r=u.shared.providers.translate(t.title);if("separator"===t.type)return st.some({type:"separator",text:r});if("submenu"!==t.type)return st.some(et({type:"togglemenuitem",text:r,icon:t.icon,active:t.isSelected(o),disabled:e,onAction:a.onAction(t)},t.getStylePreview().fold(function(){return{}},function(t){return{meta:{style:t}}})));var i=U(t.getStyleItems(),function(t){return c(t,n,o)});return 0===n&&i.length<=0?st.none():st.some({type:"nestedmenuitem",text:r,disabled:i.length<=0,getSubmenuItems:function(){return U(t.getStyleItems(),function(t){return c(t,n,o)})}})},c=function(t,n,e){var o="formatter"===t.type&&a.isInvalid(t);return 0===n?o?[]:r(t,n,!1,e).toArray():r(t,n,o,e).toArray()},{validateItems:i=function(t){var n=a.getCurrentValue(),e=a.shouldHide?0:1;return U(t,function(t){return c(t,e,n)})},getFetch:function(o,r){return function(t){var n=r(),e=i(n);t(Dk(e,$h.CLOSE_ON_EXECUTE,o,!1))}}}),getStyleItems:s}},zT=function(o,t,n){var e=PT(0,t,n),r=e.items,i=e.getStyleItems;return Ck({text:n.icon.isSome()?st.none():st.some(""),icon:n.icon,tooltip:st.from(n.tooltip),role:st.none(),fetch:r.getFetch(t,i),onSetup:function(e){return n.setInitialValue.each(function(t){return t(e.getComponent())}),n.nodeChangeHandler.map(function(t){var n=t(e.getComponent());return o.on("NodeChange",n),function(){o.off("NodeChange",n)}}).getOr(Z)},getApi:function(t){return{getComponent:function(){return t}}},columns:1,presets:"normal",classes:n.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};(yC=bC=bC||{})[yC.SemiColon=0]="SemiColon",yC[yC.Space=1]="Space";var NT,LT,jT=function(t,n,e,o){var r,i,u=bt(t.settings,n).getOr(e);return{type:"basic",data:(i=u,r=o===bC.SemiColon?i.replace(/;$/,"").split(";"):i.split(" "),V(r,function(t){var n=t,e=t,o=t.split("=");return 1<o.length&&(n=o[0],e=o[1]),{title:n,format:e}}))}},UT=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],WT=function(e){var n=function(t){var n=L(UT,function(t){return e.formatter.match(t.format)}).fold(function(){return"left"},function(t){return t.title.toLowerCase()});jo(t,kk,{icon:"align-"+n})},t=st.some(function(t){return function(){return n(t)}}),o=st.some(function(t){return n(t)}),r={type:"basic",data:UT};return{tooltip:"Align",icon:st.some("align-left"),isSelectedFor:function(t){return function(){return e.formatter.match(t)}},getCurrentValue:at(st.none()),getPreviewFor:function(t){return function(){return st.none()}},onAction:function(n){return function(){return L(UT,function(t){return t.format===n.format}).each(function(t){return e.execCommand(t.command)})}},setInitialValue:o,nodeChangeHandler:t,dataset:r,shouldHide:!1,isInvalid:function(t){return!e.formatter.canApply(t.format)}}},GT=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],XT=function(t){var n=t.split(/\s*,\s*/);return V(n,function(t){return t.replace(/^['"]+|['"]+$/g,"")})},YT=function(t){var n;return 0===t.indexOf("-apple-system")&&(n=XT(t.toLowerCase()),W(GT,function(t){return-1<n.indexOf(t.toLowerCase())}))},qT=function(r){var i=function(){var e=function(t){return t?XT(t)[0]:""},t=r.queryCommandValue("FontName"),n=u.data,o=t?t.toLowerCase():"";return{matchOpt:L(n,function(t){var n=t.format;return n.toLowerCase()===o||e(n).toLowerCase()===e(o).toLowerCase()}).orThunk(function(){return YT(o)?st.from({title:"System Font",format:o}):st.none()}),font:t}},n=function(t){var n=i(),e=n.matchOpt,o=n.font,r=e.fold(function(){return o},function(t){return t.title});jo(t,Sk,{text:r})},t=st.some(function(t){return function(){return n(t)}}),e=st.some(function(t){return n(t)}),u=jT(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",bC.SemiColon);return{tooltip:"Fonts",icon:st.none(),isSelectedFor:function(n){return function(t){return t.exists(function(t){return t.format===n})}},getCurrentValue:function(){return i().matchOpt},getPreviewFor:function(t){return function(){return st.some({tag:"div",styles:-1===t.indexOf("dings")?{"font-family":t}:{}})}},onAction:function(t){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,t.format)})}},setInitialValue:e,nodeChangeHandler:t,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}},KT={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},JT=function(t,n){return/[0-9.]+px$/.test(t)?(e=72*parseInt(t,10)/96,o=n||0,r=Math.pow(10,o),Math.round(e*r)/r+"pt"):t;var e,o,r},$T=function(e){var i=function(){var o=st.none(),r=u.data,i=e.queryCommandValue("FontSize");if(i)for(var t=function(t){var n=JT(i,t),e=bt(KT,n).getOr("");o=L(r,function(t){return t.format===i||t.format===n||t.format===e})},n=3;o.isNone()&&0<=n;n--)t(n);return{matchOpt:o,size:i}},t=at(at(st.none())),n=function(t){var n=i(),e=n.matchOpt,o=n.size,r=e.fold(function(){return o},function(t){return t.title});jo(t,Sk,{text:r})},o=st.some(function(t){return function(){return n(t)}}),r=st.some(function(t){return n(t)}),u=jT(e,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",bC.Space);return{tooltip:"Font sizes",icon:st.none(),isSelectedFor:function(n){return function(t){return t.exists(function(t){return t.format===n})}},getPreviewFor:t,getCurrentValue:function(){return i().matchOpt},onAction:function(t){return function(){e.undoManager.transact(function(){e.focus(),e.execCommand("FontSize",!1,t.format)})}},setInitialValue:r,nodeChangeHandler:o,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}},QT=function(e,t,n){var o=t();return Q(n,function(n){return L(o,function(t){return e.formatter.matchNode(n,t.format)})}).orThunk(function(){return e.formatter.match("p")?st.some({title:"Paragraph",format:"p"}):st.none()})},ZT=function(t){var n=t.selection.getStart(!0)||t.getBody();return t.dom.getParents(n,function(){return!0},t.getBody())},tE=function(o,r){return function(n){var e=ce(st.none()),t=function(){n.setActive(o.formatter.match(r));var t=o.formatter.formatChanged(r,n.setActive).unbind;e.set(st.some(t))};return o.initialized?t():o.on("init",t),function(){return e.get().each(function(t){return t()})}}},nE=function(n){return function(t){return function(){n.undoManager.transact(function(){n.focus(),n.execCommand("mceToggleFormat",!1,t.format)})}}},eE=function(o){var e=function(t,n){var e=QT(o,function(){return r.data},t).fold(function(){return"Paragraph"},function(t){return t.title});jo(n,Sk,{text:e})},t=st.some(function(n){return function(t){return e(t.parents,n)}}),n=st.some(function(t){var n=ZT(o);e(n,t)}),r=jT(o,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",bC.SemiColon);return{tooltip:"Blocks",icon:st.none(),isSelectedFor:function(t){return function(){return o.formatter.match(t)}},getCurrentValue:at(st.none()),getPreviewFor:function(n){return function(){var t=o.formatter.get(n);return st.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:o.dom.parseStyle(o.formatter.getCssText(n))})}},onAction:nE(o),setInitialValue:n,nodeChangeHandler:t,dataset:r,shouldHide:!1,isInvalid:function(t){return!o.formatter.canApply(t.format)}}},oE=function(i,t){var e=function(t,n){var e=function(t){var n=t.items;return n!==undefined&&0<n.length?U(n,e):[{title:t.title,format:t.format}]},o=U(aO(i),e),r=QT(i,function(){return o},t).fold(function(){return"Paragraph"},function(t){return t.title});jo(n,Sk,{text:r})},n=st.some(function(n){return function(t){return e(t.parents,n)}}),o=st.some(function(t){var n=ZT(i);e(n,t)});return{tooltip:"Formats",icon:st.none(),isSelectedFor:function(t){return function(){return i.formatter.match(t)}},getCurrentValue:at(st.none()),getPreviewFor:function(n){return function(){var t=i.formatter.get(n);return t!==undefined?st.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:i.dom.parseStyle(i.formatter.getCssText(n))}):st.none()}},onAction:nE(i),setInitialValue:o,nodeChangeHandler:n,shouldHide:i.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(t){return!i.formatter.canApply(t.format)},dataset:t}},rE=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],iE=function(r,i){return function(t,n,e){var o=r(t).mapError(function(t){return Tn(t)}).getOrDie();return i(o,n,e)}},uE={button:iE(L_,function(t,n){return e=t,o=n.backstage.shared.providers,ST(e,o,[]);var e,o}),togglebutton:iE(q_,function(t,n){return e=t,o=n.backstage.shared.providers,kT(e,o,[]);var e,o}),menubutton:iE(W_,function(t,n){return Ak(t,"tox-tbtn",n.backstage,st.none())}),splitbutton:iE(function(t){return Cn("SplitButton",G_,t)},function(t,n){return CT(t,n.backstage.shared)}),grouptoolbarbutton:iE(function(t){return Cn("GroupToolbarButton",K_,t)},function(t,n,e){var o,r,i,u,a,c,s=e.ui.registry.getAll().buttons,l=((o={})[Oc]=n.backstage.shared.header.isPositionedAtTop()?Hu.TopToBottom:Hu.BottomToTop,o);switch(Dh(e)){case Kg.floating:return r=t,i=n.backstage,u=function(t){return lE(e,{buttons:s,toolbar:t,allowToolbarGroups:!1},n,st.none())},a=l,c=i.shared,a_.sketch({lazySink:c.getSink,fetch:function(){return hy(function(t){t(V(u(r.items),F_))})},markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:xT(r.icon,r.text,r.tooltip,st.none(),st.none(),c.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:a}}}});default:throw new Error("Toolbar groups are only supported when using floating toolbar mode")}}),styleSelectButton:function(t,n){return e=t,o=n.backstage,r=et({type:"advanced"},o.styleselect),zT(e,o,oE(e,r));var e,o,r},fontsizeSelectButton:function(t,n){return e=t,o=n.backstage,zT(e,o,$T(e));var e,o},fontSelectButton:function(t,n){return e=t,o=n.backstage,zT(e,o,qT(e));var e,o},formatButton:function(t,n){return e=t,o=n.backstage,zT(e,o,eE(e));var e,o},alignMenuButton:function(t,n){return e=t,o=n.backstage,zT(e,o,WT(e));var e,o}},aE={styleselect:uE.styleSelectButton,fontsizeselect:uE.fontsizeSelectButton,fontselect:uE.fontSelectButton,formatselect:uE.formatButton,align:uE.alignMenuButton},cE=function(t){var n,e,o,r=t.toolbar,i=t.buttons;return!1===r?[]:r===undefined||!0===r?(e=i,o=V(rE,function(t){var n=P(t.items,function(t){return yt(e,t)||yt(aE,t)});return{name:t.name,items:n}}),P(o,function(t){return 0<t.items.length})):S(r)?(n=r.split("|"),V(n,function(t){return{items:t.trim().split(" ")}})):E(r,function(t){return yt(t,"name")&&yt(t,"items")})?r:(nt.console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])},sE=function(r,n,i,u,a,t){return bt(n,i.toLowerCase()).orThunk(function(){return t.bind(function(t){return Q(t,function(t){return bt(n,t+i.toLowerCase())})})}).fold(function(){return bt(aE,i.toLowerCase()).map(function(t){return t(r,a)}).orThunk(function(){return st.none()})},function(t){return"grouptoolbarbutton"!==t.type||u?(e=a,o=r,bt(uE,(n=t).type).fold(function(){return nt.console.error("skipping button defined by",n),st.none()},function(t){return st.some(t(n,e,o))})):(nt.console.warn("Ignoring the '"+i+"' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested."),st.none());var n,e,o})},lE=function(e,o,r,i){var t=cE(o),n=V(t,function(t){var n=U(t.items,function(t){return 0===t.trim().length?[]:sE(e,o.buttons,t,o.allowToolbarGroups,r,i).toArray()});return{title:st.from(e.translate(t.name)),items:n}});return P(n,function(t){return 0<t.items.length})},fE={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},dE={maxHeightFunction:hc(),maxWidthFunction:zO()},mE={onLtr:function(){return[Oa,_a,ka,wa,Ca,Sa,zg,Ng,Hg,Rg,Pg,Vg]},onRtl:function(){return[Oa,_a,Ca,Sa,ka,wa,zg,Ng,Pg,Vg,Hg,Rg]}},gE={onLtr:function(){return[_a,wa,Sa,ka,Ca,Oa,zg,Ng,Hg,Rg,Pg,Vg]},onRtl:function(){return[_a,Sa,wa,Ca,ka,Oa,zg,Ng,Pg,Vg,Hg,Rg]}},pE=function(c,t,e,s){var o,r,l=Pe().deviceType.isTouch,a=Ji((o={sink:e,onEscape:function(){return c.focus(),st.some(!0)}},r=ce([]),Bg.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(t){r.set([]),Bg.getContent(t).each(function(t){Hi(t.element(),"visibility")}),xi(t.element(),HT),Hi(t.element(),"width")},inlineBehaviours:za([Lm("context-toolbar-events",[er(mo(),function(t,n){xi(t.element(),HT),Hi(t.element(),"width")}),Jo(VT,function(n,e){Hi(n.element(),"width");var t=cu(n.element());Bg.setContent(n,e.event().contents()),bi(n.element(),HT);var o=cu(n.element());Di(n.element(),"width",t+"px"),Bg.getContent(n).each(function(t){e.event().focus().bind(function(t){return Ka(t),$a(n.element())}).orThunk(function(){return Rm.focusIn(t),Ja()})}),Lg.setTimeout(function(){Di(n.element(),"width",o+"px")},0)}),Jo(IT,function(t,n){Bg.getContent(t).each(function(t){r.set(r.get().concat([{bar:t,focus:Ja()}]))}),jo(t,VT,{contents:n.event().forwardContents(),focus:st.none()})}),Jo(RT,function(n,t){J(r.get()).each(function(t){r.set(r.get().slice(0,r.get().length-1)),jo(n,VT,{contents:$i(t.bar),focus:t.focus})})})]),Rm.config({mode:"special",onEscape:function(n){return J(r.get()).fold(function(){return o.onEscape()},function(t){return Lo(n,RT),st.some(!0)})}})]),lazySink:function(){return ot.value(o.sink)}}))),f=function(){return BT(c,s.backstage.shared)},u=function(){if(l()&&s.backstage.isContextMenuOpen())return!0;var t,n,e,o,r,i,u=(t=m.get().map(function(t){return t.getBoundingClientRect()}).getOrThunk(function(){return c.selection.getRng().getBoundingClientRect()}),n=c.inline?mu().top():Su(le.fromDom(c.getBody())).y,{y:t.top+n,bottom:t.bottom+n}),a=f();return e=u.y,o=u.bottom,r=a.y,i=a.bottom,!(Math.max(e,r)<=Math.min(o,i))},n=function(){Bg.hide(a)},i=function(){d.get().each(function(t){var n=a.element();Hi(n,"display"),u()?Di(n,"display","none"):Ds.positionWithinBounds(e,t,a,st.some(f()))})},d=ce(st.none()),m=ce(st.none()),g=ce(null),p=function(t){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:za([Rm.config({mode:"acyclic"}),Lm("pop-dialog-wrap-events",[or(function(t){c.shortcuts.add("ctrl+F9","focus statusbar",function(){return Rm.focusIn(t)})}),rr(function(t){c.shortcuts.remove("ctrl+F9")})])])}},h=_t(function(){return FT(t,function(t){var n=v([t]);jo(a,IT,{forwardContents:p(n)})})}),v=function(t){var n=c.ui.registry.getAll().buttons,e=h(),o=et(et({},n),e.formNavigators),r=Dh(c)===Kg.scrolling?Kg.scrolling:Kg["default"],i=ut(V(t,function(t){return"contexttoolbar"===t.type?lE(c,{buttons:o,toolbar:t.items,allowToolbarGroups:!1},s,st.some(["form:"])):(n=t,e=s.backstage.shared.providers,ET(n,e));var n,e}));return P_({type:r,uid:Nr("context-toolbar"),initGroups:i,onEscape:st.none,cyclicKeying:!0,providers:s.backstage.shared.providers})};c.on("contexttoolbar-show",function(n){var t=h();bt(t.lookupTable,n.toolbarKey).each(function(t){y([t],n.target===c?st.none():st.some(n)),Bg.getContent(a).each(Rm.focusIn)})});var b=function(t,n){var e,o,r="node"===t?s.backstage.shared.anchors.node(n):s.backstage.shared.anchors.cursor();return Ct(r,(e=t,o=l(),"line"===e?{bubble:wc(12,0,fE),layouts:{onLtr:function(){return[Ta]},onRtl:function(){return[Ea]}},overrides:dE}:{bubble:wc(0,12,fE),layouts:o?gE:mE,overrides:dE}))},y=function(t,n){if(w(),!l()||!s.backstage.isContextMenuOpen()){var e=v(t),o=n.map(le.fromDom),r=b(t[0].position,o);d.set(st.some(r)),m.set(n);var i=a.element();Hi(i,"display"),Bg.showWithinBounds(a,r,p(e),function(){return st.some(f())}),u()&&Di(i,"display","none")}},x=function(){if(c.hasFocus()){var t,n,e,o,r,i,u=h();t=u,n=c,o=le.fromDom(n.getBody()),r=function(t){return Le(t,o)},i=le.fromDom(n.selection.getNode()),(r(e=i)||Ue(o,e)?MT(i,t.inNodeScope,t.inEditorScope).orThunk(function(){return AT(r,i,t)}):st.none()).fold(function(){d.set(st.none()),Bg.hide(a)},function(t){y(t.toolbars,st.some(t.elem.dom()))})}},w=function(){var t=g.get();null!==t&&(Lg.clearTimeout(t),g.set(null))},S=function(t){w(),g.set(t)};c.on("init",function(){c.on(NO,n),c.on("ScrollContent ScrollWindow longpress",i),c.on("click keyup focus SetContent ObjectResized ResizeEditor",function(){S(Lg.setEditorTimeout(c,x,0))}),c.on("focusout",function(t){Lg.setEditorTimeout(c,function(){$a(e.element()).isNone()&&$a(a.element()).isNone()&&(d.set(st.none()),Bg.hide(a))},0)}),c.on("SwitchMode",function(){c.mode.isReadOnly()&&(d.set(st.none()),Bg.hide(a))}),c.on("NodeChange",function(t){$a(a.element()).fold(function(){S(Lg.setEditorTimeout(c,x,0))},function(t){})})})},hE=vf,vE=Zl,bE=at([Zn("shell",!1),zn("makeItem"),Zn("setupItem",Z),Sl("listBehaviours",[Nm])]),yE=Jl({name:"items",overrides:function(){return{behaviours:za([Nm.config({})])}}}),xE=at([yE]),wE=_f({name:at("CustomList")(),configFields:bE(),partFields:xE(),factory:function(s,t,n,e){var o=s.shell?{behaviours:[Nm.config({})],components:[]}:{behaviours:[],components:t},r=function(t){return s.shell?st.some(t):sf(t,s,"items")};return{uid:s.uid,dom:s.dom,components:o.components,behaviours:wl(s.listBehaviours,o.behaviours),apis:{setItems:function(a,c){r(a).fold(function(){throw nt.console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")},function(n){var t=Nm.contents(n),e=c.length,o=e-t.length,r=0<o?function(t,n){for(var e=[],o=0;o<t;o++)e.push(n(o));return e}(o,function(){return s.makeItem()}):[],i=t.slice(e);it(i,function(t){return Nm.remove(n,t)}),it(r,function(t){return Nm.append(n,t)});var u=Nm.contents(n);it(u,function(t,n){s.setupItem(a,t,c[n],n)})})}}}},apis:{setItems:function(t,n,e){t.setItems(n,e)}}}),SE=function(t){return(Ri(t,"position").is("fixed")?st.none():fr(t)).orThunk(function(){var e=le.fromTag("span");return lr(t).bind(function(t){vr(t,e);var n=fr(e);return xr(e),n})})},kE=function(t){return SE(t).map(iu).getOrThunk(function(){return ou(0,0)})},CE=wt([{"static":[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),OE=function(t,n){var e=t.element();bi(e,n.transitionClass),xi(e,n.fadeOutClass),bi(e,n.fadeInClass),n.onShow(t)},_E=function(t,n){var e=t.element();bi(e,n.transitionClass),xi(e,n.fadeInClass),bi(e,n.fadeOutClass),n.onHide(t)},TE=function(t,o,r){return W(t,function(t){switch(t){case"bottom":return e=r,o.bottom<=e.bottom;case"top":return n=r,o.y>=n.y}var n,e})},EE=function(n,t){return t.getInitialPosition().map(function(t){return xu(t.bounds.x,t.bounds.y,cu(n),tu(n))})},BE=function(t,n,e){e.setInitialPosition(st.some({style:function(t){var n={},e=t.dom();if(Ci(e))for(var o=0;o<e.style.length;o++){var r=e.style.item(o);n[r]=e.style[r]}return n}(t),position:Fi(t,"position")||"static",bounds:n}))},DE=function(e,o,r){return r.getInitialPosition().bind(function(t){switch(r.setInitialPosition(st.none()),t.position){case"static":return st.some(CE["static"]());case"absolute":var n=SE(e).map(wu).getOrThunk(function(){return wu(_i())});return st.some(CE.absolute(Za("absolute",bt(t.style,"left").map(function(t){return o.x-n.x}),bt(t.style,"top").map(function(t){return o.y-n.y}),bt(t.style,"right").map(function(t){return n.right-o.right}),bt(t.style,"bottom").map(function(t){return n.bottom-o.bottom}))));default:return st.none()}})},ME=function(t,n,e){var o,r,i,u=t.element();return Ri(u,"position").is("fixed")?(r=n,EE(o=u,i=e).filter(function(t){return TE(i.getModes(),t,r)}).bind(function(t){return DE(o,t,i)})):function(t,n,e){var o=wu(t);if(TE(e.getModes(),o,n))return st.none();BE(t,o,e);var r=ku(),i=o.x-r.x,u=n.y-r.y,a=r.bottom-n.bottom,c=o.y<=n.y;return st.some(CE.fixed(Za("fixed",st.some(i),c?st.some(u):st.none(),st.none(),c?st.none():st.some(a))))}(u,n,e)},AE=function(n,t){it(["left","right","top","bottom","position"],function(t){return Hi(n.element(),t)}),t.onUndocked(n)},FE=function(t,n,e){tc(t.element(),e),("fixed"===e.position()?n.onDocked:n.onUndocked)(t)},IE=function(i,t,u,a,c){void 0===c&&(c=!1),t.contextual.each(function(r){r.lazyContext(i).each(function(t){var n,e,o=(e=a,(n=t).y<e.bottom&&n.bottom>e.y);o!==u.isVisible()&&(u.setVisible(o),c&&!o?(Si(i.element(),[r.fadeOutClass]),r.onHide(i)):(o?OE:_E)(i,r))})})},RE=function(n,e,t){var o,r,i=n.element();t.setDocked(!1),o=t,r=n.element(),EE(r,o).bind(function(t){return DE(r,t,o)}).each(function(t){t.fold(function(){return AE(n,e)},function(t){return FE(n,e,t)},Z)}),t.setVisible(!0),e.contextual.each(function(t){ki(i,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(n)}),VE(n,e,t)},VE=function(t,n,e){var o,r,i,u,a;t.getSystem().isConnected()&&(o=t,i=e,u=(r=n).lazyViewport(o),(a=i.isDocked())&&IE(o,r,i,u),ME(o,u,i).each(function(t){i.setDocked(!a),t.fold(function(){return AE(o,r)},function(t){return FE(o,r,t)},function(t){IE(o,r,i,u,!0),FE(o,r,t)})}))},HE=function(t,n,e){e.isDocked()&&RE(t,n,e)},PE=/* */Object.freeze({__proto__:null,refresh:VE,reset:HE,isDocked:function(t,n,e){return e.isDocked()},getModes:function(t,n,e){return e.getModes()},setModes:function(t,n,e,o){return e.setModes(o)}}),zE=/* */Object.freeze({__proto__:null,events:function(o,r){return Yo([er(mo(),function(n,e){o.contextual.each(function(t){wi(n.element(),t.transitionClass)&&(ki(n.element(),[t.transitionClass,t.fadeInClass]),(r.isVisible()?t.onShown:t.onHidden)(n));e.stop()})}),Jo(Bo(),function(t,n){VE(t,o,r)}),Jo(Do(),function(t,n){HE(t,o,r)})])}}),NE=[Qn("contextual",[Ln("fadeInClass"),Ln("fadeOutClass"),Ln("transitionClass"),Un("lazyContext"),$u("onShow"),$u("onShown"),$u("onHide"),$u("onHidden")]),ie("lazyViewport",ku),te("modes",["top","bottom"],fn(Fn)),$u("onDocked"),$u("onUndocked")],LE=La({fields:NE,name:"docking",active:zE,apis:PE,state:/* */Object.freeze({__proto__:null,init:function(t){var n=ce(!1),e=ce(!0),o=ce(st.none()),r=ce(t.modes);return oi({isDocked:n.get,setDocked:n.set,getInitialPosition:o.get,setInitialPosition:o.set,isVisible:e.get,setVisible:e.set,getModes:r.get,setModes:r.set,readState:function(){return"docked:  "+n.get()+", visible: "+e.get()+", modes: "+r.get().join(",")}})}})}),jE={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},UE="tox-tinymce--toolbar-sticky-on",WE="tox-tinymce--toolbar-sticky-off",GE=function(r){var i=r.element();lr(i).each(function(t){var n,e="padding-"+LE.getModes(r)[0];if(LE.isDocked(r)){var o=cu(t);Di(i,"width",o+"px"),Di(t,e,nu(n=i)+(parseInt(Fi(n,"margin-top"),10)||0)+(parseInt(Fi(n,"margin-bottom"),10)||0)+"px")}else Hi(i,"width"),Hi(t,e)})},XE=function(t,n){n?(xi(t,jE.fadeOutClass),Si(t,[jE.transitionClass,jE.fadeInClass])):(xi(t,jE.fadeInClass),Si(t,[jE.fadeOutClass,jE.transitionClass]))},YE=function(t,n){var e=le.fromDom(t.getContainer());n?(bi(e,UE),xi(e,WE)):(bi(e,WE),xi(e,UE))},qE=function(c,t){var n,i=ce(st.none()),o=t.getSink,u=function(n){o().each(function(t){return n(t.element())})},e=function(t){c.inline||GE(t),YE(c,LE.isDocked(t)),t.getSystem().broadcastOn([tl()],{}),o().each(function(t){return t.getSystem().broadcastOn([tl()],{})})},r=c.inline?[]:[Ya.config({channels:((n={})[M_()]={onReceive:GE},n)})];return b([Xm.config({}),LE.config({contextual:et({lazyContext:function(t){var n,e,o=nu(t.element()),r=c.inline?c.getContentAreaContainer():c.getContainer(),i=wu(le.fromDom(r)),u=i.height-o,a=i.y+(n=t,e="top",F(LE.getModes(n),e)?0:o);return st.some(xu(i.x,a,i.width,u))},onShow:function(){u(function(t){return XE(t,!0)})},onShown:function(r){u(function(t){return ki(t,[jE.transitionClass,jE.fadeInClass])}),i.get().each(function(t){var n,e,o;n=r.element(),o=ar(e=t),Ja(o).filter(function(t){return!Le(e,t)}).filter(function(t){return Le(t,le.fromDom(o.dom().body))||Ue(n,t)}).each(function(){return Ka(e)}),i.set(st.none())})},onHide:function(t){var n,e;i.set((n=t.element(),e=o,$a(n).orThunk(function(){return e().toOption().bind(function(t){return $a(t.element())})}))),u(function(t){return XE(t,!1)})},onHidden:function(){u(function(t){return ki(t,[jE.transitionClass])})}},jE),modes:[t.header.getDockingMode()],onDocked:e,onUndocked:e})],r)},KE=/* */Object.freeze({__proto__:null,setup:function(t,n,o){t.inline||(n.header.isPositionedAtTop()||t.on("ResizeEditor",function(){o().each(LE.reset)}),t.on("ResizeWindow ResizeEditor",function(){o().each(GE)}),t.on("SkinLoaded",function(){o().each(function(t){LE.isDocked(t)?LE.reset(t):LE.refresh(t)})}),t.on("FullscreenStateChanged",function(){o().each(LE.reset)})),t.on("AfterScrollIntoView",function(e){o().each(function(t){LE.refresh(t);var n=t.element();Bd(n)&&function(t,n){var e=ar(n),o=e.dom().defaultView.innerHeight,r=mu(e),i=le.fromDom(t.elm),u=Su(i),a=tu(i),c=u.y,s=c+a,l=iu(n),f=tu(n),d=l.top(),m=d+f,g=Math.abs(d-r.top())<2,p=Math.abs(m-(r.top()+o))<2;if(g&&c<m)gu(r.left(),c-f,e);else if(p&&d<s){var h=c-o+a+f;gu(r.left(),h,e)}}(e,n)})}),t.on("PostRender",function(){YE(t,!1)})},isDocked:function(t){return t().map(LE.isDocked).getOr(!1)},getBehaviours:qE}),JE=Z,$E=c,QE=at([]),ZE=/* */Object.freeze({__proto__:null,setup:JE,isDocked:$E,getBehaviours:QE}),tB=Of({factory:function(n,o){var t={focus:Rm.focusIn,setMenus:function(t,n){var e=V(n,function(n){var t={type:"menubutton",text:n.text,fetch:function(t){t(n.getItems())}},e=W_(t).mapError(function(t){return Tn(t)}).getOrDie();return Ak(e,"tox-mbtn",o.backstage,st.some("menuitem"))});Nm.set(t,e)}};return{uid:n.uid,dom:n.dom,components:[],behaviours:za([Nm.config({}),Lm("menubar-events",[or(function(t){n.onSetup(t)}),Jo(ro(),function(e,t){Mu(e.element(),".tox-mbtn--active").each(function(n){Au(t.event().target(),".tox-mbtn").each(function(t){Le(n,t)||e.getSystem().getByDom(n).each(function(n){e.getSystem().getByDom(t).each(function(t){Qy.expand(t),Qy.close(n),Xm.focus(t)})})})})}),Jo(Ro(),function(e,t){t.event().prevFocus().bind(function(t){return e.getSystem().getByDom(t).toOption()}).each(function(n){t.event().newFocus().bind(function(t){return e.getSystem().getByDom(t).toOption()}).each(function(t){Qy.isOpen(n)&&(Qy.expand(t),Qy.close(n))})})})]),Rm.config({mode:"flow",selector:".tox-mbtn",onEscape:function(t){return n.onEscape(t),st.some(!0)}}),Dy.config({})]),apis:t,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[zn("dom"),zn("uid"),zn("onEscape"),zn("backstage"),Zn("onSetup",Z)],apis:{focus:function(t,n){t.focus(n)},setMenus:function(t,n,e){t.setMenus(n,e)}}}),nB="container",eB=[yl("slotBehaviours",[])],oB=function(t){return"<alloy.field."+t+">"},rB=function(r,t){var e,n=function(t){return mf(r)},o=function(e,o){return function(t,n){return sf(t,r,n).map(function(t){return e(t,n)}).getOr(o)}},i=function(t,n){return"true"!==Dr(t.element(),"aria-hidden")},u=o(i,!1),a=o(function(t,n){if(i(t)){var e=t.element();Di(e,"display","none"),Br(e,"aria-hidden","true"),jo(t,Vo(),{name:n,visible:!1})}}),c=function(n,t){it(t,function(t){return e(n,t)})},s=o(function(t,n){if(!i(t)){var e=t.element();Hi(e,"display"),Fr(e,"aria-hidden"),jo(t,Vo(),{name:n,visible:!0})}}),l={getSlotNames:n,getSlot:function(t,n){return sf(t,r,n)},isShowing:u,hideSlot:e=a,hideAllSlots:function(t){return c(t,n())},showSlot:s};return{uid:r.uid,dom:r.dom,components:t,behaviours:xl(r.slotBehaviours),apis:l}},iB=dt({getSlotNames:function(t,n){return t.getSlotNames(n)},getSlot:function(t,n,e){return t.getSlot(n,e)},isShowing:function(t,n,e){return t.isShowing(n,e)},hideSlot:function(t,n,e){return t.hideSlot(n,e)},hideAllSlots:function(t,n){return t.hideAllSlots(n)},showSlot:function(t,n,e){return t.showSlot(n,e)}},function(t){return ni(t)}),uB=et(et({},iB),{sketch:function(t){var e,n=(e=[],{slot:function(t,n){return e.push(t),of(nB,oB(t),n)},record:function(){return e}}),o=t(n),r=n.record(),i=V(r,function(t){return ql({name:t,pname:oB(t)})});return yf(nB,eB,i,rB,o)}}),aB=ln([Jn("icon"),Jn("tooltip"),ie("onShow",Z),ie("onHide",Z),ie("onSetup",function(){return Z})]),cB=function(t){return{element:function(){return t.element().dom()}}},sB=function(e,o){var r=V(lt(o),function(t){var n=o[t],e=On(Cn("sidebar",aB,n));return{name:t,getApi:cB,onSetup:e.onSetup,onShow:e.onShow,onHide:e.onHide}});return V(r,function(t){var n=ce(Z);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:Tp([Kh(t,n),Jh(t,n),Jo(Vo(),function(n,t){var e=t.event();L(r,function(t){return t.name===e.name()}).each(function(t){(e.visible()?t.onShow:t.onHide)(t.getApi(n))})})])})})},lB=function(t,e){Mf.getCurrent(t).each(function(t){return Nm.set(t,[(n=e,uB.sketch(function(t){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:sB(t,n),slotBehaviours:Tp([or(function(t){return uB.hideAllSlots(t)})])}}))]);var n})},fB=function(t){return Mf.getCurrent(t).bind(function(t){return __.isGrowing(t)||__.hasGrown(t)?Mf.getCurrent(t).bind(function(n){return L(uB.getSlotNames(n),function(t){return uB.isShowing(n,t)})}):st.none()})},dB=Nr("FixSizeEvent"),mB=Nr("AutoSizeEvent"),gB=function(t){var n,e,o,r=le.fromHtml(t),i=dr(r),u=(e=(n=r).dom().attributes!==undefined?n.dom().attributes:[],N(e,function(t,n){var e;return"class"===n.name?t:et(et({},t),((e={})[n.name]=n.value,e))},{})),a=(o=r,Array.prototype.slice.call(o.dom().classList,0)),c=0===i.length?{}:{innerHtml:Sr(r)};return et({tag:Cr(r),classes:a,attributes:u},c)},pB=function(t,n,e){var o=t.element();!0===n?(Nm.set(t,[{dom:{tag:"div",attributes:{"aria-label":e.translate("Loading...")},classes:["tox-throbber__busy-spinner"]},components:[{dom:gB('<div class="tox-spinner"><div></div><div></div><div></div></div>')}],behaviours:za([Rm.config({mode:"special",onTab:function(){return st.some(!0)},onShiftTab:function(){return st.some(!0)}}),Xm.config({})])}]),Hi(o,"display"),Fr(o,"aria-hidden")):(Nm.set(t,[]),Di(o,"display","none"),Br(o,"aria-hidden","true"))},hB=vE.optional({factory:tB,name:"menubar",schema:[zn("backstage")]}),vB=vE.optional({factory:{sketch:function(t){return wE.sketch({uid:t.uid,dom:t.dom,listBehaviours:za([Rm.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return P_({type:t.type,uid:Nr("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:t.providers,onEscape:function(){return st.none()}})},setupItem:function(t,n,e,o){XO.setGroups(n,e)},shell:!0})}},name:"multiple-toolbar",schema:[zn("dom"),zn("onEscape")]}),bB=vE.optional({factory:{sketch:function(t){var n;return((n=t).type===Kg.sliding?H_:n.type===Kg.floating?V_:P_)({type:t.type,uid:t.uid,onEscape:function(){return t.onEscape(),st.some(!0)},cyclicKeying:!1,initGroups:[],getSink:t.getSink,providers:t.providers,moreDrawerData:{lazyToolbar:t.lazyToolbar,lazyMoreButton:t.lazyMoreButton,lazyHeader:t.lazyHeader},attributes:t.attributes})}},name:"toolbar",schema:[zn("dom"),zn("onEscape"),zn("getSink")]}),yB=vE.optional({factory:{sketch:function(t){var n=t.editor,e=t.sticky?qE:QE;return{uid:t.uid,dom:t.dom,components:t.components,behaviours:za(e(n,t.sharedBackstage))}}},name:"header",schema:[zn("dom")]}),xB=vE.optional({name:"socket",schema:[zn("dom")]}),wB=vE.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:za([Dy.config({}),Xm.config({}),__.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(t){Mf.getCurrent(t).each(uB.hideAllSlots),Lo(t,mB)},onGrown:function(t){Lo(t,mB)},onStartGrow:function(t){jo(t,dB,{width:Ri(t.element(),"width").getOr("")})},onStartShrink:function(t){jo(t,dB,{width:cu(t.element())+"px"})}}),Nm.config({}),Mf.config({find:function(t){var n=Nm.contents(t);return K(n)}})])}],behaviours:za([nS(0),Lm("sidebar-sliding-events",[Jo(dB,function(t,n){Di(t.element(),"width",n.event().width())}),Jo(mB,function(t,n){Hi(t.element(),"width")})])])}}},name:"sidebar",schema:[zn("dom")]}),SB=vE.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:za([Nm.config({})]),components:[]}}},name:"throbber",schema:[zn("dom")]}),kB=_f({name:"OuterContainer",factory:function(e,t,n){var o={getSocket:function(t){return hE.getPart(t,e,"socket")},setSidebar:function(t,n){hE.getPart(t,e,"sidebar").each(function(t){return lB(t,n)})},toggleSidebar:function(t,o){hE.getPart(t,e,"sidebar").each(function(t){return n=t,e=o,void Mf.getCurrent(n).each(function(n){Mf.getCurrent(n).each(function(t){__.hasGrown(n)?uB.isShowing(t,e)?__.shrink(n):(uB.hideAllSlots(t),uB.showSlot(t,e)):(uB.hideAllSlots(t),uB.showSlot(t,e),__.grow(n))})});var n,e})},whichSidebar:function(t){return hE.getPart(t,e,"sidebar").bind(fB).getOrNull()},getHeader:function(t){return hE.getPart(t,e,"header")},getToolbar:function(t){return hE.getPart(t,e,"toolbar")},setToolbar:function(t,n){hE.getPart(t,e,"toolbar").each(function(t){t.getApis().setGroups(t,n)})},setToolbars:function(t,n){hE.getPart(t,e,"multiple-toolbar").each(function(t){wE.setItems(t,n)})},refreshToolbar:function(t){hE.getPart(t,e,"toolbar").each(function(t){return t.getApis().refresh(t)})},getThrobber:function(t){return hE.getPart(t,e,"throbber")},focusToolbar:function(t){hE.getPart(t,e,"toolbar").orThunk(function(){return hE.getPart(t,e,"multiple-toolbar")}).each(function(t){Rm.focusIn(t)})},setMenubar:function(t,n){hE.getPart(t,e,"menubar").each(function(t){tB.setMenus(t,n)})},focusMenubar:function(t){hE.getPart(t,e,"menubar").each(function(t){tB.focus(t)})}};return{uid:e.uid,dom:e.dom,components:t,apis:o,behaviours:e.behaviours}},configFields:[zn("dom"),zn("behaviours")],partFields:[yB,hB,bB,vB,xB,wB,SB],apis:{getSocket:function(t,n){return t.getSocket(n)},setSidebar:function(t,n,e){t.setSidebar(n,e)},toggleSidebar:function(t,n,e){t.toggleSidebar(n,e)},whichSidebar:function(t,n){return t.whichSidebar(n)},getHeader:function(t,n){return t.getHeader(n)},getToolbar:function(t,n){return t.getToolbar(n)},setToolbar:function(t,n,e){var o=V(e,function(t){return F_(t)});t.setToolbar(n,o)},setToolbars:function(t,n,e){var o=V(e,function(t){return V(t,F_)});t.setToolbars(n,o)},refreshToolbar:function(t,n){return t.refreshToolbar(n)},getThrobber:function(t,n){return t.getThrobber(n)},setMenubar:function(t,n,e){t.setMenubar(n,e)},focusMenubar:function(t,n){t.focusMenubar(n)},focusToolbar:function(t,n){t.focusToolbar(n)}}}),CB={file:{title:"File",items:"newdocument restoredraft | preview | print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align | forecolor backcolor | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},OB=function(t){return"string"==typeof t?t.split(" "):t},_B=function(i,u){var a=et(et({},CB),u.menus),n=0<lt(u.menus).length,t=u.menubar===undefined||!0===u.menubar?OB("file edit view insert format tools table help"):OB(!1===u.menubar?"":u.menubar),e=P(t,function(t){return n&&u.menus.hasOwnProperty(t)&&u.menus[t].hasOwnProperty("items")||CB.hasOwnProperty(t)}),o=V(e,function(t){var n,e,o,r=a[t];return n={title:r.title,items:OB(r.items)},e=u,o=i.getParam("removed_menuitems","").split(/[ ,]/),{text:n.title,getItems:function(){return U(n.items,function(t){var n=t.toLowerCase();return 0===n.trim().length||I(o,function(t){return t===n})?[]:"separator"===n||"|"===n?[{type:"separator"}]:e.menuItems[n]?[e.menuItems[n]]:[]})}}});return P(o,function(t){return 0<t.getItems().length&&I(t.getItems(),function(t){return"separator"!==t.type})})},TB=function(t){var n=function(){t._skinLoaded=!0,t.fire("SkinLoaded")};return function(){t.initialized?n():t.on("init",n)}},EB=function(n,e){return function(){return t={message:e},n.fire("SkinLoadError",t);var t}},BB=function(t,n){var e,o=function(t){var n=t.settings,e=n.skin,o=n.skin_url;if(!1!==e){var r=e||"oxide";o=o?t.documentBaseURI.toAbsolute(o):vh.baseURL+"/skins/ui/"+r}return o}(n);o&&(e=o+"/skin.min.css",n.contentCSS.push(o+(t?"/content.inline":"/content")+".min.css")),!1==(!1===n.getParam("skin"))&&e?hh.DOM.styleSheetLoader.load(e,TB(n),EB(n,"Skin could not be loaded")):TB(n)()},DB=g(BB,!1),MB=g(BB,!0),AB=function(e,t,o,r){var n=t.outerContainer,i=o.toolbar,u=o.buttons;if(E(i,S)){var a=i.map(function(t){var n={toolbar:t,buttons:u,allowToolbarGroups:o.allowToolbarGroups};return lE(e,n,{backstage:r},st.none())});kB.setToolbars(n,a)}else kB.setToolbar(n,lE(e,o,{backstage:r},st.none()))},FB=hh.DOM,IB=Pe(),RB=IB.os.isiOS()&&IB.os.version.major<=12,VB=function(o){var e=o.getWin(),t=o.getDoc().documentElement,r=ce(ou(e.innerWidth,e.innerHeight)),i=ce(ou(t.offsetWidth,t.offsetHeight)),n=function(t){var n=r.get();n.left()===e.innerWidth&&n.top()===e.innerHeight||(r.set(ou(e.innerWidth,e.innerHeight)),Iv(o,t))},u=function(t){var n=o.getDoc().documentElement,e=i.get();e.left()===n.offsetWidth&&e.top()===n.offsetHeight||(i.set(ou(n.offsetWidth,n.offsetHeight)),Iv(o,t))},a=function(t){return n=t,o.fire("ScrollContent",n);var n};FB.bind(e,"resize",n),FB.bind(e,"scroll",a);var c=xb(le.fromDom(o.getBody()),"load",u);o.on("NodeChange",u),o.on("remove",function(){c.unbind(),FB.unbind(e,"resize",n),FB.unbind(e,"scroll",a)})},HB=/* */Object.freeze({__proto__:null,render:function(e,o,t,n,r){var i,u,a=ce(0);DB(e),i=le.fromDom(r.targetNode),u=o.mothership,Ls(i,u,pr),Ns(_i(),o.uiMothership),e.on("PostRender",function(){AB(e,o,t,n),a.set(e.getWin().innerWidth),kB.setMenubar(o.outerContainer,_B(e,t)),kB.setSidebar(o.outerContainer,t.sidebar),VB(e)});var c,s,l,f=kB.getSocket(o.outerContainer).getOrDie("Could not find expected socket element");if(!0===RB){Mi(f.element(),{overflow:"scroll","-webkit-overflow-scrolling":"touch"});var d=(c=function(){e.fire("ScrollContent")},s=20,l=null,{cancel:function(){null!==l&&(nt.clearTimeout(l),l=null)},throttle:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];null===l&&(l=nt.setTimeout(function(){c.apply(null,t),l=null},s))}});yb(f.element(),"scroll",d.throttle)}jh(e,o),e.addCommand("ToggleSidebar",function(t,n){kB.toggleSidebar(o.outerContainer,n),e.fire("ToggleSidebar")}),e.addQueryValueHandler("ToggleSidebar",function(){return kB.whichSidebar(o.outerContainer)});var m=Dh(e);return m!==Kg.sliding&&m!==Kg.floating||e.on("ResizeWindow ResizeEditor ResizeContent",function(){var t=e.getWin().innerWidth;t!==a.get()&&(kB.refreshToolbar(o.outerContainer),a.set(t))}),{iframeContainer:f.element().dom(),editorContainer:o.outerContainer.element().dom()}}}),PB=function(){return t=function(t){t.unbind()},n=ce(st.none()),e=function(){n.get().each(t)},{clear:function(){e(),n.set(st.none())},isSet:function(){return n.get().isSome()},set:function(t){e(),n.set(st.some(t))}};var t,n,e},zB=function(t){return/^[0-9\.]+(|px)$/i.test(""+t)?st.some(parseInt(""+t,10)):st.none()},NB=function(t){return rt(t)?t+"px":t},LB=function(n,t,e){var o=t.filter(function(t){return n<t}),r=e.filter(function(t){return t<n});return o.or(r).getOr(n)},jB=function(t){var n,e,o,r;return(e=bh(n=t),o=wh(n),r=kh(n),zB(e).map(function(t){return LB(t,o,r)})).getOr(bh(t))},UB=function(t){var n=yh(t),e=xh(t),o=Sh(t);return zB(n).map(function(t){return LB(t,e,o)})},WB=function(a,c,t,n,s){var e=t.uiMothership,l=t.outerContainer,o=hh.DOM,f=Vh(a),d=Ph(a),m=Sh(a).or(UB(a)),r=n.shared.header,g=r.isPositionedAtTop,i=Dh(a),p=i===Kg.sliding||i===Kg.floating,u=ce(!1),h=function(){return u.get()&&!a.removed},v=function(t){return p?t.fold(function(){return 0},function(t){return 1<t.components().length?tu(t.components()[1].element()):0}):0},b=function(){e.broadcastOn([tl()],{})},y=function(t){if(void 0===t&&(t=!1),h()){var n,e,o,r,i;if(f||(n=m.getOrThunk(function(){var t=zB(Fi(_i(),"margin-left")).getOr(0);return cu(_i())-iu(c).left()+t}),Di(s.get().element(),"max-width",n+"px")),p&&kB.refreshToolbar(l),f||(e=kB.getToolbar(l),o=v(e),r=wu(c),i=g()?Math.max(r.y-tu(s.get().element())+o,0):r.bottom,Mi(l.element(),{position:"absolute",top:Math.round(i)+"px",left:Math.round(r.x)+"px"})),d){var u=s.get();t?LE.reset(u):LE.refresh(u)}b()}},x=function(t){if(void 0===t&&(t=!0),!f&&d&&h()){var n=r.getDockingMode(),e=function(t){switch(Fh(a)){case Eh.auto:var n=kB.getToolbar(l),e=v(n),o=tu(t.element())-e,r=wu(c);if(r.y>o)return"top";var i=cr(c),u=Math.max(i.dom().scrollHeight,tu(i));return r.bottom<u-o||ku().bottom<r.bottom-o?"bottom":"top";case Eh.bottom:return"bottom";case Eh.top:default:return"top"}}(s.get());e!==n&&(function(t){var n=s.get();LE.setModes(n,[t]),r.setDockingMode(t);var e=g()?Hu.TopToBottom:Hu.BottomToTop;Br(n.element(),Oc,e)}(e),t&&y(!0))}};return{isVisible:h,isPositionedAtTop:g,show:function(){u.set(!0),Di(l.element(),"display","flex"),o.addClass(a.getBody(),"mce-edit-focus"),Hi(e.element(),"display"),x(!1),y()},hide:function(){u.set(!1),t.outerContainer&&(Di(l.element(),"display","none"),o.removeClass(a.getBody(),"mce-edit-focus")),Di(e.element(),"display","none")},update:y,updateMode:x,repositionPopups:b}},GB=function(t,n){var e=wu(t);return{pos:n?e.y:e.bottom,bounds:e}},XB=/* */Object.freeze({__proto__:null,render:function(n,e,o,r,t){var i=e.mothership,u=e.uiMothership,a=e.outerContainer,c=ce(null),s=le.fromDom(t.targetNode),l=WB(n,s,e,r,c);MB(n);var f=function(){if(c.get())l.show();else{c.set(kB.getHeader(a).getOrDie());var t=Rh(n).getOr(_i());Ns(t,i),Ns(t,u),AB(n,e,o,r),kB.setMenubar(a,_B(n,o)),l.show(),function(c,s,l){var f=ce(GB(s,l.isPositionedAtTop())),n=function(t){var n=GB(s,l.isPositionedAtTop()),e=n.pos,o=n.bounds,r=f.get(),i=r.pos,u=r.bounds,a=o.height!==u.height||o.width!==u.width;f.set({pos:e,bounds:o}),a&&Iv(c,t),l.isVisible()&&(i!==e?l.update(!0):a&&(l.updateMode(),l.repositionPopups()))};c.on("activate",l.show),c.on("deactivate",l.hide),c.on("SkinLoaded ResizeWindow",function(){return l.update(!0)}),c.on("NodeChange keydown",function(t){Lg.requestAnimationFrame(function(){return n(t)})}),c.on("ScrollWindow",function(){return l.updateMode()});var t=PB();t.set(xb(le.fromDom(c.getBody()),"load",n)),c.on("remove",function(){t.clear()})}(n,s,l),n.nodeChanged()}};return n.on("focus",f),n.on("blur hide",l.hide),n.on("init",function(){n.hasFocus()&&f()}),jh(n,e),{editorContainer:a.element().dom()}}}),YB=function(t,n){return function(){t.execCommand("mceToggleFormat",!1,n)}},qB=function(t){var n,e;!function(e){uC.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(t,n){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:tE(e,t.name),onAction:YB(e,t.name)})});for(var t=1;t<=6;t++){var n="h"+t;e.ui.registry.addToggleButton(n,{text:n.toUpperCase(),tooltip:"Heading "+t,onSetup:tE(e,n),onAction:YB(e,n)})}}(t),n=t,uC.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(t){n.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:function(){return n.execCommand(t.action)}})}),e=t,uC.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(t){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:function(){return e.execCommand(t.action)},onSetup:tE(e,t.name)})})},KB=function(t){var n;qB(t),n=t,uC.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(t){n.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:function(){return n.execCommand(t.action)}})}),n.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:YB(n,"code")})},JB=function(t,n,e){var o=function(){return!!n.undoManager&&n.undoManager[e]()},r=function(){t.setDisabled(n.mode.isReadOnly()||!o())};return t.setDisabled(!o()),n.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r),function(){return n.off("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r)}},$B=function(t){var n,e;(n=t).ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:function(t){return JB(t,n,"hasUndo")},onAction:function(){return n.execCommand("undo")}}),n.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:function(t){return JB(t,n,"hasRedo")},onAction:function(){return n.execCommand("redo")}}),(e=t).ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",onSetup:function(t){return JB(t,e,"hasUndo")},onAction:function(){return e.execCommand("undo")}}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",onSetup:function(t){return JB(t,e,"hasRedo")},onAction:function(){return e.execCommand("redo")}})},QB=function(t,n){var e,o,r,i,u,a,c,s,l,f,d,m,g,p;r=PT(0,o=n,WT(e=t)),e.ui.registry.addNestedMenuItem("align",{text:o.shared.providers.translate("Align"),getSubmenuItems:function(){return r.items.validateItems(r.getStyleItems())}}),a=PT(0,u=n,qT(i=t)),i.ui.registry.addNestedMenuItem("fontformats",{text:u.shared.providers.translate("Fonts"),getSubmenuItems:function(){return a.items.validateItems(a.getStyleItems())}}),c=t,l=et({type:"advanced"},(s=n).styleselect),f=PT(0,s,oE(c,l)),c.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return f.items.validateItems(f.getStyleItems())}}),m=PT(0,n,eE(d=t)),d.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return m.items.validateItems(m.getStyleItems())}}),p=PT(0,n,$T(g=t)),g.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return p.items.validateItems(p.getStyleItems())}})},ZB=function(t,n){var e,o,r,i;!function(n){uC.each([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(t){n.ui.registry.addToggleButton(t.name,{tooltip:t.text,onAction:function(){return n.execCommand(t.cmd)},icon:t.icon,onSetup:tE(n,t.name)})});var t="alignnone",e="No alignment",o="JustifyNone",r="align-none";n.ui.registry.addButton(t,{tooltip:e,onAction:function(){return n.execCommand(o)},icon:r})}(t),KB(t),QB(t,n),$B(t),function(t){Rv(t);var n=ce(null),e=ce(null);Lv(t,"forecolor","forecolor","Text color",n),Lv(t,"backcolor","hilitecolor","Background color",e),jv(t,"forecolor","forecolor","Text color"),jv(t,"backcolor","hilitecolor","Background color")}(t),(o=e=t).ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:function(){return o.execCommand("mceToggleVisualAid")}}),(r=e).ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:function(t){return function(n,t){n.setActive(t.hasVisual);var e=function(t){n.setActive(t.hasVisual)};return t.on("VisualAid",e),function(){return t.off("VisualAid",e)}}(t,r)},onAction:function(){r.execCommand("mceToggleVisualAid")}}),(i=t).ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:function(t){return function(t,n){t.setDisabled(!n.queryCommandState("outdent"));var e=function(){t.setDisabled(!n.queryCommandState("outdent"))};return n.on("NodeChange",e),function(){return n.off("NodeChange",e)}}(t,i)},onAction:function(){return i.execCommand("outdent")}}),i.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:function(){return i.execCommand("indent")}})},tD=function(t,n){return{anchor:"makeshift",x:t,y:n}},nD=function(t){return"longpress"===t.type||0===t.type.indexOf("touch")},eD=function(t,n){var e,o,r,i=hh.DOM.getPos(t);return e=n,o=i.x,r=i.y,tD(e.x+o,e.y+r)},oD=function(t,n){return"contextmenu"===n.type||"longpress"===n.type?t.inline?function(t){if(nD(t)){var n=t.touches[0];return tD(n.pageX,n.pageY)}return tD(t.pageX,t.pageY)}(n):eD(t.getContentAreaContainer(),function(t){if(nD(t)){var n=t.touches[0];return tD(n.clientX,n.clientY)}return tD(t.clientX,t.clientY)}(n)):rD(t)},rD=function(t){return{anchor:"selection",root:le.fromDom(t.selection.getNode())}},iD=function(t){return{anchor:"node",node:st.some(le.fromDom(t.selection.getNode())),root:le.fromDom(t.getBody())}},uD=function(t,n,e,o,r,i){var u,a,c=e(),s=(u=t,a=n,i?iD(u):oD(u,a));Dk(c,$h.CLOSE_ON_EXECUTE,o,!1).map(function(t){n.preventDefault(),Bg.showMenuAt(r,s,{menu:{markers:Gv("normal")},data:t})})},aD={onLtr:function(){return[_a,wa,Sa,ka,Ca,Oa,zg,Ng,Hg,Rg,Pg,Vg]},onRtl:function(){return[_a,Sa,wa,Ca,ka,Oa,zg,Ng,Pg,Vg,Hg,Rg]}},cD={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},sD=function(n,e,t,o,r,i,u){var a,c,s,l=(a=n,c=e,s=i?iD(a):oD(a,c),et({bubble:wc(0,12,cD),layouts:aD,overrides:{maxWidthFunction:zO(),maxHeightFunction:hc()}},s));Dk(t,$h.CLOSE_ON_EXECUTE,o,!0).map(function(t){e.preventDefault(),Bg.showMenuWithinBounds(r,l,{menu:{markers:Gv("normal"),highlightImmediately:u},data:t,type:"horizontal"},function(){return st.some(BT(n,o.shared))}),n.fire(NO)})},lD=function(n,e,o,r,i,u){var t=Pe(),a=t.os.isiOS(),c=t.os.isOSX(),s=t.os.isAndroid(),l=t.deviceType.isTouch(),f=function(){var t=o();sD(n,e,t,r,i,u,!(s||a||c&&l))};if(!c&&!a||u)s&&!u&&n.selection.setCursorLocation(e.target,0),f();else{var d=function(){!function(t){var n=t.selection.getRng(),e=function(){Lg.setEditorTimeout(t,function(){t.selection.setRng(n)},10),i()};t.once("touchend",e);var o=function(t){t.preventDefault(),t.stopImmediatePropagation()};t.on("mousedown",o,!0);var r=function(){return i()};t.once("longpresscancel",r);var i=function(){t.off("touchend",e),t.off("longpresscancel",r),t.off("mousedown",o)}}(n),f()};!function(t,n){var e=t.selection;if(e.isCollapsed()||n.touches.length<1)return!1;var o=n.touches[0],r=e.getRng();return Jc(t.getWin(),Rc.domRange(r)).exists(function(t){return t.left()<=o.clientX&&t.right()>=o.clientX&&t.top()<=o.clientY&&t.bottom()>=o.clientY})}(n,e)?(n.once("selectionchange",d),n.once("touchend",function(){return n.off("selectionchange",d)})):d()}},fD=function(t){return"string"==typeof t?t.split(/[ ,]/):t},dD=function(t){return t.settings.contextmenu_never_use_native||!1},mD=function(t){return e="contextmenu",o="link linkchecker image imagetools table spellchecker configurepermanentpen",r=(n=t).ui.registry.getAll().contextMenus,bt(n.settings,e).map(fD).getOrThunk(function(){return P(fD(o),function(t){return yt(r,t)})});var n,e,o,r},gD=function(t){return S(t)?"|"===t:"separator"===t.type},pD={type:"separator"},hD=function(n){if(S(n))return n;switch(n.type){case"separator":return pD;case"submenu":return{type:"nestedmenuitem",text:n.text,icon:n.icon,getSubmenuItems:function(){var t=n.getSubmenuItems();return S(t)?t:V(t,hD)}};default:return{type:"menuitem",text:n.text,icon:n.icon,onAction:(t=n.onAction,function(){return t()})}}var t},vD=function(t,n){if(0===n.length)return t;var e=J(t).filter(function(t){return!gD(t)}).fold(function(){return[]},function(t){return[pD]});return t.concat(e).concat(n).concat([pD])},bD=function(c,t,o){var r=Pe().deviceType.isTouch,i=Ji(Bg.sketch({dom:{tag:"div"},lazySink:t,onEscape:function(){return c.focus()},onShow:function(){return o.setContextMenuState(!0)},onHide:function(){return o.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:za([Lm("dismissContextMenu",[Jo(Fo(),function(t,n){Qs.close(t),c.focus()})])])})),n=function(t){return Bg.hide(i)},e=function(u){if(dD(c)&&u.preventDefault(),t=c,(!u.ctrlKey||dD(t))&&!1!==c.getParam("contextmenu")){var t,n,e,a=(n=c,"longpress"!==(e=u).type&&(2!==e.button||e.target===n.getBody()&&""===e.pointerType));(r()?lD:uD)(c,u,function(){var r,i,t,n=a?c.selection.getStart(!0):u.target,e=c.ui.registry.getAll(),o=mD(c);return r=e.contextMenus,i=n,0<(t=N(o,function(t,n){if(yt(r,n)){var e=r[n].update(i);if(S(e))return vD(t,e.split(" "));if(0<e.length){var o=V(e,hD);return vD(t,o)}return t}return t.concat([n])},[])).length&&gD(t[t.length-1])&&t.pop(),t},o,i,a)}};c.on("init",function(){var t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(r()?"":" ResizeWindow");c.on(t,n),c.on("longpress contextmenu",e)})},yD=wt([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),xD=function(n){return function(t){return t.translate(-n.left(),-n.top())}},wD=function(n){return function(t){return t.translate(n.left(),n.top())}},SD=function(e){return function(t,n){return N(e,function(t,n){return n(t)},ou(t,n))}},kD=function(t,n,e){return t.fold(SD([wD(e),xD(n)]),SD([xD(n)]),SD([]))},CD=function(t,n,e){return t.fold(SD([wD(e)]),SD([]),SD([wD(n)]))},OD=function(t,n,e){return t.fold(SD([]),SD([xD(e)]),SD([wD(n),xD(e)]))},_D=function(t,n,e){var o=t.fold(function(t,n){return{position:st.some("absolute"),left:st.some(t+"px"),top:st.some(n+"px")}},function(t,n){return{position:st.some("absolute"),left:st.some(t-e.left()+"px"),top:st.some(n-e.top()+"px")}},function(t,n){return{position:st.some("fixed"),left:st.some(t+"px"),top:st.some(n+"px")}});return et({right:st.none(),bottom:st.none()},o)},TD=function(t,i,u,a){var n=function(o,r){return function(t,n){var e=o(i,u,a);return r(t.getOr(e.left()),n.getOr(e.top()))}};return t.fold(n(OD,ED),n(CD,BD),n(kD,DD))},ED=yD.offset,BD=yD.absolute,DD=yD.fixed,MD=function(t,n){var e=Dr(t,n);return _(e)?NaN:parseInt(e,10)},AD=function(t,n,e,o){return r=n,i=t.element(),u=MD(i,r.leftAttr),a=MD(i,r.topAttr),(isNaN(u)||isNaN(a)?st.none():st.some(ou(u,a))).fold(function(){return e},function(t){return DD(t.left()+o.left(),t.top()+o.top())});var r,i,u,a},FD=function(t,n,e,o,r,i){var u,a,c,s=AD(t,n,e,o),l=(n.mustSnap?VD:HD)(t,n,s,r,i),f=kD(s,r,i);return u=n,a=f,c=t.element(),Br(c,u.leftAttr,a.left()+"px"),Br(c,u.topAttr,a.top()+"px"),l.fold(function(){return{coord:DD(f.left(),f.top()),extra:st.none()}},function(t){return{coord:t.output,extra:t.extra}})},ID=function(t,n){var e,o;e=n,o=t.element(),Fr(o,e.leftAttr),Fr(o,e.topAttr)},RD=function(t,l,f,d){return Q(t,function(t){var n,e,o,r,i,u,a,c,s=t.sensor;return(n=l,e=s,o=t.range.left(),r=t.range.top(),a=CD(n,i=f,u=d),c=CD(e,i,u),Math.abs(a.left()-c.left())<=o&&Math.abs(a.top()-c.top())<=r)?st.some({output:TD(t.output,l,f,d),extra:t.extra}):st.none()})},VD=function(t,n,d,m,g){var e=n.getSnapPoints(t);return RD(e,d,m,g).orThunk(function(){return N(e,function(n,e){var t,o,r,i,u,a,c,s,l=e.sensor,f=(t=d,o=l,e.range.left(),e.range.top(),u=CD(t,r=m,i=g),a=CD(o,r,i),c=Math.abs(u.left()-a.left()),s=Math.abs(u.top()-a.top()),ou(c,s));return n.deltas.fold(function(){return{deltas:st.some(f),snap:st.some(e)}},function(t){return(f.left()+f.top())/2<=(t.left()+t.top())/2?{deltas:st.some(f),snap:st.some(e)}:n})},{deltas:st.none(),snap:st.none()}).snap.map(function(t){return{output:TD(t.output,d,m,g),extra:t.extra}})})},HD=function(t,n,e,o,r){var i=n.getSnapPoints(t);return RD(i,e,o,r)},PD=/* */Object.freeze({__proto__:null,snapTo:function(t,n,e,o){var r,i,u,a=n.getTarget(t.element());if(n.repositionTarget){var c=ar(t.element()),s=mu(c),l=kE(a),f=(i=s,u=l,{coord:TD((r=o).output,r.output,i,u),extra:r.extra}),d=_D(f.coord,0,l);Ai(a,d)}}}),zD="data-initial-z-index",ND=function(t,n){var e;t.getSystem().addToGui(n),lr((e=n).element()).filter(_r).each(function(n){Ri(n,"z-index").each(function(t){Br(n,zD,t)}),Di(n,"z-index",Fi(e.element(),"z-index"))})},LD=function(t){lr(t.element()).filter(_r).each(function(n){Mr(n,zD).fold(function(){return Hi(n,"z-index")},function(t){return Di(n,"z-index",t)}),Fr(n,zD)}),t.getSystem().removeFromGui(t)},jD=function(t,n,e){return t.getSystem().build(Ub.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[n]},events:e}))},UD=Qn("snaps",[zn("getSnapPoints"),$u("onSensor"),zn("leftAttr"),zn("topAttr"),Zn("lazyViewport",ku),Zn("mustSnap",!1)]),WD=[Zn("useFixed",c),zn("blockerClass"),Zn("getTarget",ct),Zn("onDrag",Z),Zn("repositionTarget",!0),Zn("onDrop",Z),ie("getBounds",ku),UD],GD=function(n){return t=Ri(n,"left"),e=Ri(n,"top"),o=Ri(n,"position"),r=function(t,n,e){return("fixed"===e?DD:ED)(parseInt(t,10),parseInt(n,10))},(t.isSome()&&e.isSome()&&o.isSome()?st.some(r(t.getOrDie(),e.getOrDie(),o.getOrDie())):st.none()).getOrThunk(function(){var t=iu(n);return BD(t.left(),t.top())});var t,e,o,r},XD=function(e,t,i,u,a,c,n){var o,r,s,l,f,d,m,g,p,h=t.fold(function(){var t,e,o,n=(t=i,e=c.left(),o=c.top(),t.fold(function(t,n){return ED(t+e,n+o)},function(t,n){return BD(t+e,n+o)},function(t,n){return DD(t+e,n+o)})),r=kD(n,u,a);return DD(r.left(),r.top())},function(n){var t=FD(e,n,i,c,u,a);return t.extra.each(function(t){n.onSensor(e,t)}),t.coord});return o=h,r=u,s=a,f=(l=n).bounds,d=CD(o,r,s),m=sc(d.left(),f.x,f.x+f.width-l.width),g=sc(d.top(),f.y,f.y+f.height-l.height),p=BD(m,g),o.fold(function(){var t=OD(p,r,s);return ED(t.left(),t.top())},function(){return p},function(){var t=kD(p,r,s);return DD(t.left(),t.top())})},YD=function(t,n){return{bounds:t.getBounds(),height:nu(n.element()),width:su(n.element())}},qD=function(n,e,t,o,r){var i=t.update(o,r),u=t.getStartData().getOrThunk(function(){return YD(e,n)});i.each(function(t){!function(t,n,e,o){var r=n.getTarget(t.element());if(n.repositionTarget){var i=ar(t.element()),u=mu(i),a=kE(r),c=GD(r),s=XD(t,n.snaps,c,u,a,o,e),l=_D(s,0,a);Ai(r,l)}n.onDrag(t,r,o)}(n,e,u,t)})},KD=function(n,t,e,o){t.each(LD),e.snaps.each(function(t){ID(n,t)});var r=e.getTarget(n.element());o.reset(),e.onDrop(n,r)},JD=function(t){return function(n,e){var o=function(t){e.setStartData(YD(n,t))};return Yo(b([Jo(Bo(),function(t){e.getStartData().each(function(){return o(t)})})],t(n,e,o)))}},$D=/* */Object.freeze({__proto__:null,getData:function(t){return st.from(ou(t.x(),t.y()))},getDelta:function(t,n){return ou(n.left()-t.left(),n.top()-t.top())}}),QD=function(a,c,s){return[Jo(to(),function(n,t){if(0===t.event().raw().button){t.stop();var e,o=function(){return KD(n,st.some(u),a,c)},r=kb(o,200),i={drop:o,delayDrop:r.schedule,forceDrop:o,move:function(t){r.cancel(),qD(n,a,c,$D,t)}},u=jD(n,a.blockerClass,(e=i,Yo([Jo(to(),e.forceDrop),Jo(oo(),e.drop),Jo(no(),function(t,n){e.move(n.event())}),Jo(eo(),e.delayDrop)])));s(n),ND(n,u)}})]},ZD=b(WD,[na("dragger",{handlers:JD(QD)})]),tM=/* */Object.freeze({__proto__:null,getData:function(t){var n,e=t.raw().touches;return 1===e.length?(n=e[0],st.some(ou(n.clientX,n.clientY))):st.none()},getDelta:function(t,n){return ou(n.left()-t.left(),n.top()-t.top())}}),nM=function(u,a,c){var s=ce(st.none());return[Jo(Je(),function(n,t){t.stop();var e,o=function(){KD(n,s.get(),u,a),s.set(st.none())},r={drop:o,delayDrop:function(){},forceDrop:o,move:function(t){qD(n,u,a,tM,t)}},i=jD(n,u.blockerClass,(e=r,Yo([Jo(Je(),e.forceDrop),Jo(Qe(),e.drop),Jo(Ze(),e.drop),Jo($e(),function(t,n){e.move(n.event())})])));s.set(st.some(i));c(n),ND(n,i)}),Jo($e(),function(t,n){n.stop(),qD(t,u,a,tM,n.event())}),Jo(Qe(),function(t,n){n.stop(),KD(t,s.get(),u,a),s.set(st.none())}),Jo(Ze(),function(t){KD(t,s.get(),u,a),s.set(st.none())})]},eM=ZD,oM=b(WD,[na("dragger",{handlers:JD(nM)})]),rM=b(WD,[na("dragger",{handlers:JD(function(t,n,e){return b(QD(t,n,e),nM(t,n,e))})})]),iM=Ua({branchKey:"mode",branches:/* */Object.freeze({__proto__:null,mouse:eM,touch:oM,mouseOrTouch:rM}),name:"dragging",active:{events:function(t,n){return t.dragger.handlers(t,n)}},extra:{snap:function(t){return{sensor:t.sensor,range:t.range,output:t.output,extra:st.from(t.extra)}}},state:/* */Object.freeze({__proto__:null,init:function(){var i=st.none(),n=st.none(),t=at({});return oi({readState:t,reset:function(){i=st.none(),n=st.none()},update:function(r,t){return r.getData(t).bind(function(t){return n=r,e=t,o=i.map(function(t){return n.getDelta(t,e)}),i=st.some(e),o;var n,e,o})},getStartData:function(){return n},setStartData:function(t){n=st.some(t)}})}}),apis:PD}),uM=function(t,r,i,u,n,e){return t.fold(function(){return iM.snap({sensor:BD(i-20,u-20),range:ou(n,e),output:BD(st.some(i),st.some(u)),extra:{td:r}})},function(t){var n=i-20,e=u-20,o=t.element().dom().getBoundingClientRect();return iM.snap({sensor:BD(n,e),range:ou(40,40),output:BD(st.some(i-o.width/2),st.some(u-o.height/2)),extra:{td:r}})})},aM=function(t,i,u){return{getSnapPoints:t,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(t,n){var e,o,r=n.td;e=i.get(),o=r,e.exists(function(t){return Le(t,o)})||(i.set(st.some(r)),u(r))},mustSnap:!0}},cM=function(t){return Ug(jg.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:za([iM.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:t}),Zy.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}}))},sM=function(c,e){var o=ce([]),r=ce([]),t=ce(!1),i=ce(st.none()),u=ce(st.none()),n=function(t){var n=Su(t);return uM(f.getOpt(e),t,n.x,n.y,n.width,n.height)},a=function(t){var n=Su(t);return uM(d.getOpt(e),t,n.right,n.bottom,n.width,n.height)},s=aM(function(){return V(o.get(),function(t){return n(t)})},i,function(n){u.get().each(function(t){c.fire("TableSelectorChange",{start:n,finish:t})})}),l=aM(function(){return V(r.get(),function(t){return a(t)})},u,function(n){i.get().each(function(t){c.fire("TableSelectorChange",{start:t,finish:n})})}),f=cM(s),d=cM(l),m=Ji(f.asSpec()),g=Ji(d.asSpec()),p=function(t,n,e,o){var r=e(n);iM.snapTo(t,r);!function(t,n,e,o){var r=n.dom().getBoundingClientRect();Hi(t.element(),"display");var i=sr(le.fromDom(c.getBody())).dom().innerHeight,u=e(r),a=o(r,i);(u||a)&&Di(t.element(),"display","none")}(t,n,function(t){return t[o]<0},function(t,n){return t[o]>n})},h=function(t){return p(m,t,n,"top")},v=function(t){return p(g,t,a,"bottom")};Pe().deviceType.isTouch()&&(c.on("TableSelectionChange",function(n){t.get()||(Rs(e,m),Rs(e,g),t.set(!0)),i.set(st.some(n.start)),u.set(st.some(n.finish)),n.otherCells.each(function(t){o.set(t.upOrLeftCells),r.set(t.downOrRightCells),h(n.start),v(n.finish)})}),c.on("ResizeEditor ResizeWindow ScrollContent",function(){i.get().each(h),u.get().each(v)}),c.on("TableSelectionClear",function(){t.get()&&(Ps(m),Ps(g),t.set(!1)),i.set(st.none()),u.set(st.none())}))};(LT=NT=NT||{})[LT.None=0]="None",LT[LT.Both=1]="Both",LT[LT.Vertical=2]="Vertical";var lM,fM=function(t,n,e){var o,r,i,u,a,c,s=le.fromDom(t.getContainer()),l=(o=t,r=n,i=e,u=tu(s),a=cu(s),(c={}).height=LB(u+r.top(),wh(o),kh(o)),i===NT.Both&&(c.width=LB(a+r.left(),xh(o),Sh(o))),c);ft(l,function(t,n){return Di(s,n,NB(t))}),t.fire("ResizeEditor")},dM=function(t){if(1===t.nodeType){if("BR"===t.nodeName||t.getAttribute("data-mce-bogus"))return!0;if("bookmark"===t.getAttribute("data-mce-type"))return!0}return!1},mM=function(i,u,a){u.delimiter||(u.delimiter="\xbb");return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:za([Rm.config({mode:"flow",selector:"div[role=button]"}),ph.config({disabled:a.isReadOnly}),Uh(),Dy.config({}),Nm.config({}),Lm("elementPathEvents",[or(function(r,t){i.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return Rm.focusIn(r)}),i.on("NodeChange",function(t){var n,o,e=function(t){for(var n=[],e=t.length;0<e--;){var o=t[e];if(1===o.nodeType&&!dM(o)){var r=i.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||n.push({name:r.name,element:o}),r.isPropagationStopped())break}}return n}(t.parents);0<e.length?Nm.set(r,(n=V(e||[],function(n,t){return jg.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":t,"tab-index":-1,"aria-level":t+1},innerHtml:n.name},action:function(t){i.focus(),i.selection.select(n.element),i.nodeChanged()},buttonBehaviours:za([Gh(a.isReadOnly),Uh()])})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+u.delimiter+" "}},N(n.slice(1),function(t,n){var e=t;return e.push(o),e.push(n),e},[n[0]]))):Nm.set(r,[])})})])]),components:[]}},gM=function(i,u){var t,n,e,o,r,a;return{dom:{tag:"div",classes:["tox-statusbar"]},components:(r=function(){var t,o,r,n,e=[];return i.getParam("elementpath",!0,"boolean")&&e.push(mM(i,{},u)),Ee(i.settings.plugins,"wordcount")&&e.push((t=i,o=u,r=function(t,n,e){return Nm.set(t,[Yi(o.translate(["{0} "+e,n[e]]))])},jg.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:za([Gh(o.isReadOnly),Uh(),Dy.config({}),Nm.config({}),bl.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Lm("wordcount-events",[ur(function(t){var n=bl.getValue(t),e="words"===n.mode?"characters":"words";bl.setValue(t,{mode:e,count:n.count}),r(t,n.count,e)}),or(function(e){t.on("wordCountUpdate",function(t){var n=bl.getValue(e).mode;bl.setValue(e,{mode:n,count:t.wordCount}),r(e,t.wordCount,n)})})])]),eventOrder:{"alloy.execute":["disabling","alloy.base.behaviour","wordcount-events"]}}))),i.getParam("branding",!0,"boolean")&&e.push({dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:'<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+(n=Vp.translate(["Powered by {0}","Tiny"]))+'">'+n+"</a>"}}),0<e.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:e}]:[]}(),n=!Ee((t=i).settings.plugins,"autoresize"),(a=!1===(e=t.getParam("resize",n))?NT.None:"both"===e?NT.Both:NT.Vertical)!==NT.None&&r.push((o=a,{dom:{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:u.translate("Resize"),"aria-hidden":"true"},innerHtml:Gg("resize-handle",u.icons)},behaviours:za([iM.config({mode:"mouse",repositionTarget:!1,onDrag:function(t,n,e){fM(i,e,o)},blockerClass:"tox-blocker"})])})),r)}},pM=function(y){var t,n=y.inline,x=n?XB:HB,w=Ph(y)?KE:ZE,e=st.none(),o=Pe(),r=o.browser.isIE()?["tox-platform-ie"]:[],i=o.deviceType.isTouch()?["tox-platform-touch"]:[],u=Ih(y),a=Vp.isRtl()?{attributes:{dir:"rtl"}}:{},c={attributes:((t={})[Oc]=u?Hu.BottomToTop:Hu.TopToBottom,t)},S=function(){return e.bind(kB.getHeader)},k=Ji({dom:et({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(r).concat(i)},a),behaviours:za([Ds.config({useFixed:function(){return w.isDocked(S)}})])}),C=function(){return ot.value(k)},s=Ug({dom:{tag:"div",classes:["tox-anchorbar"]}}),O=function(){return e.bind(function(t){return kB.getThrobber(t)}).getOrDie("Could not find throbber element")},_=PO(k,y,function(){return e.bind(function(t){return s.getOpt(t)}).getOrDie("Could not find a anchor bar element")}),l=kB.parts().menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:_,onEscape:function(){y.focus()}}),T=Dh(y),f=kB.parts().toolbar(et({dom:{tag:"div",classes:["tox-toolbar"]},getSink:C,providers:_.shared.providers,onEscape:function(){y.focus()},type:T,lazyToolbar:function(){return e.bind(function(t){return kB.getToolbar(t)}).getOrDie("Could not find more toolbar element")},lazyHeader:function(){return S().getOrDie("Could not find header element")}},c)),d=kB.parts()["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:_.shared.providers,onEscape:function(){},type:T}),m=kB.parts().socket({dom:{tag:"div",classes:["tox-edit-area"]}}),g=kB.parts().sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),p=kB.parts().throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:_}),h=y.getParam("statusbar",!0,"boolean")&&!n?st.some(gM(y,_.shared.providers)):st.none(),v={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[m,g]},b=Th(y),E=Oh(y),B=Ch(y),D=kB.parts().header({dom:et({tag:"div",classes:["tox-editor-header"]},c),components:ut([B?[l]:[],b?[d]:E?[f]:[],Vh(y)?[]:[s.asSpec()]]),sticky:Ph(y),editor:y,sharedBackstage:_.shared}),M=ut([u?[]:[D],n?[]:[v],u?[D]:[]]),A=ut([[{dom:{tag:"div",classes:["tox-editor-container"]},components:M}],n?[]:h.toArray(),[p]]),F=Hh(y),I=et(et({role:"application"},Vp.isRtl()?{dir:"rtl"}:{}),F?{"aria-hidden":"true"}:{}),R=Ji(kB.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(n?["tox-tinymce-inline"]:[]).concat(u?["tox-tinymce--toolbar-bottom"]:[]).concat(i).concat(r),styles:et({visibility:"hidden"},F?{opacity:"0",border:"0"}:{}),attributes:I},components:A,behaviours:za([Rm.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a"})])}));e=st.some(R),y.shortcuts.add("alt+F9","focus menubar",function(){kB.focusMenubar(R)}),y.shortcuts.add("alt+F10","focus toolbar",function(){kB.focusToolbar(R)});var V,H,P,z,N,L,j,U,W,G,X,Y,q,K,J,$,Q=Wb(R),Z=Wb(k);V=y,H=Q,P=Z,z=function(n,e){it([H,P],function(t){t.broadcastEvent(n,e)})},N=function(n,e){it([H,P],function(t){t.broadcastOn([n],e)})},L=function(t){return N(Zs(),{target:t.target()})},j=yb(le.fromDom(nt.document),"touchstart",L),U=yb(le.fromDom(nt.document),"touchmove",function(t){return z(To(),t)}),W=yb(le.fromDom(nt.document),"touchend",function(t){return z(Eo(),t)}),G=yb(le.fromDom(nt.document),"mousedown",L),X=yb(le.fromDom(nt.document),"mouseup",function(t){0===t.raw().button&&N(nl(),{target:t.target()})}),Y=function(t){return N(Zs(),{target:le.fromDom(t.target)})},q=function(t){0===t.button&&N(nl(),{target:le.fromDom(t.target)})},K=function(t){return z(Bo(),wb(t))},J=function(t){N(tl(),{}),z(Do(),wb(t))},$=function(){return N(tl(),{})},V.on("PostRender",function(){V.on("click",Y),V.on("tap",Y),V.on("mouseup",q),V.on("ScrollWindow",K),V.on("ResizeWindow",J),V.on("ResizeEditor",$)}),V.on("remove",function(){V.off("click",Y),V.off("tap",Y),V.off("mouseup",q),V.off("ScrollWindow",K),V.off("ResizeWindow",J),V.off("ResizeEditor",$),G.unbind(),j.unbind(),U.unbind(),W.unbind(),X.unbind()}),V.on("detach",function(){js(H),js(P),H.destroy(),P.destroy()});var tt=function(){var t,n=NB(jB(y)),e=NB(UB(t=y).getOr(yh(t)));return y.inline||(Vi("div","width",e)&&Di(R.element(),"width",e),Vi("div","height",n)?Di(R.element(),"height",n):Di(R.element(),"height","200px")),n};return{mothership:Q,uiMothership:Z,backstage:_,renderUI:function(){var o,r,e,n,i,u,a,c;w.setup(y,_.shared,S),ZB(y,_),bD(y,C,_),r=(o=y).ui.registry.getAll().sidebars,it(lt(r),function(n){var t=r[n],e=function(){return st.from(o.queryCommandValue("ToggleSidebar")).is(n)};o.ui.registry.addToggleButton(n,{icon:t.icon,tooltip:t.tooltip,onAction:function(t){o.execCommand("ToggleSidebar",!1,n),t.setActive(e())},onSetup:function(t){var n=function(){return t.setActive(e())};return o.on("ToggleSidebar",n),function(){o.off("ToggleSidebar",n)}}})}),e=y,n=O,i=_.shared,u=ce(!1),a=ce(st.none()),c=function(t){t!==u.get()&&(pB(n(),t,i.providers),u.set(t))},e.on("ProgressState",function(t){if(a.get().each(Lg.clearTimeout),rt(t.time)){var n=Lg.setEditorTimeout(e,function(){return c(t.state)},t.time);a.set(st.some(n))}else c(t.state),a.set(st.none())}),dt(y.getParam("toolbar_groups",{},"object"),function(t,n){y.ui.registry.addGroupToolbarButton(n,t)});var t=y.ui.registry.getAll(),s=t.buttons,l=t.menuItems,f=t.contextToolbars,d=t.sidebars,m=_h(y),g={menuItems:l,menus:y.settings.menu?dt(y.settings.menu,function(t){return et(et({},t),{items:t.items})}):{},menubar:y.settings.menubar,toolbar:m.getOrThunk(function(){return y.getParam("toolbar",!0)}),allowToolbarGroups:T===Kg.floating,buttons:s,sidebar:d};pE(y,f,k,{backstage:_}),sM(y,k);var p=y.getElement(),h=tt(),v={mothership:Q,uiMothership:Z,outerContainer:R},b={targetNode:p,height:h};return x.render(y,v,g,_,b)},getUi:function(){return{channels:{broadcastAll:Z.broadcast,broadcastOn:Z.broadcastOn,register:function(){}}}}}},hM=at([zn("lazySink"),Yn("dragBlockClass"),ie("getBounds",ku),Zn("useTabstopAt",at(!0)),Zn("eventOrder",{}),yl("modalBehaviours",[Rm]),Qu("onExecute"),ta("onEscape")]),vM={sketch:ct},bM=at([Jl({name:"draghandle",overrides:function(t,n){return{behaviours:za([iM.config({mode:"mouse",getTarget:function(t){return Du(t,'[role="dialog"]').getOr(t)},blockerClass:t.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(n,null,2)).message),getBounds:t.getDragBounds})])}}}),ql({schema:[zn("dom")],name:"title"}),ql({factory:vM,schema:[zn("dom")],name:"close"}),ql({factory:vM,schema:[zn("dom")],name:"body"}),Jl({factory:vM,schema:[zn("dom")],name:"footer"}),Kl({factory:{sketch:function(t,n){return et(et({},t),{dom:n.dom,components:n.components})}},schema:[Zn("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),Zn("components",[])],name:"blocker"})]),yM=_f({name:"ModalDialog",configFields:hM(),partFields:bM(),factory:function(a,t,n,o){var r=Nr("alloy.dialog.busy"),c=Nr("alloy.dialog.idle"),s=za([Rm.config({mode:"special",onTab:function(){return st.some(!0)},onShiftTab:function(){return st.some(!0)}}),Xm.config({})]),e=Nr("modal-events"),i=et(et({},a.eventOrder),{"alloy.system.attached":[e].concat(a.eventOrder["alloy.system.attached"]||[])});return{uid:a.uid,dom:a.dom,components:t,apis:{show:function(i){var t=a.lazySink(i).getOrDie(),u=ce(st.none()),n=o.blocker(),e=t.getSystem().build(et(et({},n),{components:n.components.concat([$i(i)]),behaviours:za([Xm.config({}),Lm("dialog-blocker-events",[er(io(),function(){Rm.focusIn(i)}),Jo(c,function(t,n){Ar(i.element(),"aria-busy")&&(Fr(i.element(),"aria-busy"),u.get().each(function(t){return Nm.remove(i,t)}))}),Jo(r,function(t,n){Br(i.element(),"aria-busy","true");var e=n.event().getBusySpec();u.get().each(function(t){Nm.remove(i,t)});var o=e(i,s),r=t.getSystem().build(o);u.set(st.some(r)),Nm.append(i,$i(r)),r.hasConfigured(Rm)&&Rm.focusIn(r)})])])}));Rs(t,e),Rm.focusIn(i)},hide:function(n){lr(n.element()).each(function(t){n.getSystem().getByDom(t).each(function(t){Ps(t)})})},getBody:function(t){return lf(t,a,"body")},getFooter:function(t){return lf(t,a,"footer")},setIdle:function(t){Lo(t,c)},setBusy:function(t,n){jo(t,r,{getBusySpec:n})}},eventOrder:i,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:wl(a.modalBehaviours,[Nm.config({}),Rm.config({mode:"cyclic",onEnter:a.onExecute,onEscape:a.onEscape,useTabstopAt:a.useTabstopAt}),Lm(e,[or(function(t){var n,e,o,r,i,u;n=t.element(),e=lf(t,a,"title").element(),o=st.from(Dr(n,"id")).fold(function(){var t=Nr("dialog-label");return Br(e,"id",t),t},ct),Br(n,"aria-labelledby",o),r=t.element(),i=lf(t,a,"body").element(),u=st.from(Dr(r,"id")).fold(function(){var t=Nr("dialog-describe");return Br(i,"id",t),t},ct),Br(r,"aria-describedby",u)})])])}},apis:{show:function(t,n){t.show(n)},hide:function(t,n){t.hide(n)},getBody:function(t,n){return t.getBody(n)},getFooter:function(t,n){return t.getFooter(n)},setBusy:function(t,n,e){t.setBusy(n,e)},setIdle:function(t,n){t.setIdle(n)}}}),xM=[Ln("type"),Ln("text"),jn("level",["info","warn","error","success"]),Ln("icon"),Zn("url","")],wM=ln(xM),SM=[Ln("type"),Ln("text"),re("disabled",!1),re("primary",!1),hn("name","name",Gt(function(){return Nr("button-name")}),Fn),Jn("icon"),re("borderless",!1)],kM=ln(SM),CM=[Ln("type"),Ln("name"),Ln("label"),re("disabled",!1)],OM=ln(CM),_M=In,TM=[Ln("type"),Ln("name")],EM=TM.concat([Jn("label")]),BM=ln(EM),DM=Fn,MM=ln(EM),AM=Fn,FM=ln(EM),IM=fn(yn),RM=function(t){return[Ln("type"),Nn("columns",An),t]},VM=EM.concat([re("sandboxed",!0)]),HM=ln(VM),PM=Fn,zM=EM.concat([Jn("inputMode"),Jn("placeholder"),re("maximized",!1),re("disabled",!1)]),NM=ln(zM),LM=Fn,jM=EM.concat([Gn("items",[Ln("text"),Ln("value")]),ne("size",1),re("disabled",!1)]),UM=ln(jM),WM=Fn,GM=EM.concat([re("constrain",!0),re("disabled",!1)]),XM=ln(GM),YM=ln([Ln("width"),Ln("height")]),qM=EM.concat([Jn("placeholder"),re("maximized",!1),re("disabled",!1)]),KM=ln(qM),JM=Fn,$M=EM.concat([oe("filetype","file",["image","media","file"]),Zn("disabled",!1)]),QM=ln($M),ZM=ln([Ln("value"),Zn("meta",{})]),tA=TM.concat([ee("tag","textarea"),Ln("scriptId"),Ln("scriptUrl"),(lM=undefined,te("settings",lM,Hn))]),nA=TM.concat([ee("tag","textarea"),Un("init")]),eA=Sn(function(t){return Cn("customeditor.old",sn(nA),t).orThunk(function(){return Cn("customeditor.new",sn(tA),t)})}),oA=Fn,rA=[Ln("type"),Ln("html"),oe("presets","presentation",["presentation","document"])],iA=ln(rA),uA=EM.concat([Nn("currentState",ln([zn("blob"),Ln("url")]))]),aA=ln(uA),cA=EM.concat([Zn("columns","auto")]),sA=ln(cA),lA=xn([Ln("value"),Ln("text"),Ln("icon")]),fA=[Ln("type"),Xn("header",Fn),Xn("cells",fn(Fn))],dA=ln(fA),mA=function(n){return hn("items","items",Ut(),fn(Sn(function(t){return Cn("Checking item of "+n,gA,t).fold(function(t){return ot.error(Tn(t))},function(t){return ot.value(t)})})))},gA=wn(function(){return En("type",{alertbanner:wM,bar:ln((n=mA("bar"),[Ln("type"),n])),button:kM,checkbox:OM,colorinput:BM,colorpicker:MM,dropzone:FM,grid:ln(RM(mA("grid"))),iframe:HM,input:NM,selectbox:UM,sizeinput:XM,textarea:KM,urlinput:QM,customeditor:eA,htmlpanel:iA,imagetools:aA,collection:sA,label:ln((t=mA("label"),[Ln("type"),Ln("label"),t])),table:dA,panel:hA});var t,n}),pA=[Ln("type"),Zn("classes",[]),Xn("items",gA)],hA=ln(pA),vA=[hn("name","name",Gt(function(){return Nr("tab-name")}),Fn),Ln("title"),Xn("items",gA)],bA=[Ln("type"),Gn("tabs",vA)],yA=ln(bA),xA=ln([Ln("type"),Ln("name")].concat(yp)),wA=In,SA=[hn("name","name",Gt(function(){return Nr("button-name")}),Fn),Jn("icon"),oe("align","end",["start","end"]),re("primary",!1),re("disabled",!1)],kA=b(SA,[Ln("text")]),CA=b([jn("type",["submit","cancel","custom"])],kA),OA=b([jn("type",["menu"]),Jn("text"),Jn("tooltip"),Jn("icon"),Xn("items",xA)],SA),_A=kA,TA=Bn("type",{submit:CA,cancel:CA,custom:CA,menu:OA}),EA=ln([Ln("title"),Nn("body",En("type",{panel:hA,tabpanel:yA})),ee("size","normal"),Xn("buttons",TA),Zn("initialData",{}),ie("onAction",Z),ie("onChange",Z),ie("onSubmit",Z),ie("onClose",Z),ie("onCancel",Z),Zn("onTabChange",Z)]),BA=function(t){return k(t)?[t].concat(U(vt(t),BA)):C(t)?U(t,BA):[]},DA=function(t){return S(t.type)&&S(t.name)},MA={checkbox:_M,colorinput:DM,colorpicker:AM,dropzone:IM,input:LM,iframe:PM,sizeinput:YM,selectbox:WM,size:YM,textarea:JM,urlinput:ZM,customeditor:oA,collection:lA,togglemenuitem:wA},AA=function(t){var n=P(BA(t),DA),e=U(n,function(n){return t=n,st.from(MA[t.type]).fold(function(){return[]},function(t){return[Nn(n.name,t)]});var t});return ln(e)},FA=ln(b([jn("type",["cancel","custom"])],_A)),IA=ln([Ln("title"),Ln("url"),Kn("height"),Kn("width"),qn("buttons",fn(FA)),ie("onAction",Z),ie("onCancel",Z),ie("onClose",Z),ie("onMessage",Z)]),RA=function(t){return{internalDialog:On(Cn("dialog",EA,t)),dataValidator:AA(t),initialData:t.initialData}},VA={open:function(t,n){var e=RA(n);return t(e.internalDialog,e.initialData,e.dataValidator)},openUrl:function(t,n){return t(On(Cn("dialog",IA,n)))},redial:function(t){return RA(t)}},HA=function(t){var e=[],o={};return ft(t,function(t,n){t.fold(function(){e.push(n)},function(t){o[n]=t})}),0<e.length?ot.error(e):ot.value(o)},PA=Of({name:"TabButton",configFields:[Zn("uid",undefined),zn("value"),hn("dom","dom",Xt(function(){return{attributes:{role:"tab",id:Nr("aria"),"aria-selected":"false"}}}),Dn()),Yn("action"),Zn("domModification",{}),yl("tabButtonBehaviours",[Xm,Rm,bl]),zn("view")],factory:function(t,n){return{uid:t.uid,dom:t.dom,components:t.components,events:ug(t.action),behaviours:wl(t.tabButtonBehaviours,[Xm.config({}),Rm.config({mode:"execution",useSpace:!0,useEnter:!0}),bl.config({store:{mode:"memory",initialValue:t.value}})]),domModification:t.domModification}}}),zA=at([zn("tabs"),zn("dom"),Zn("clickToDismiss",!1),yl("tabbarBehaviours",[jf,Rm]),Ku(["tabClass","selectedClass"])]),NA=$l({factory:PA,name:"tabs",unit:"tab",overrides:function(o){var r=function(t,n){jf.dehighlight(t,n),jo(t,Po(),{tabbar:t,button:n})},i=function(t,n){jf.highlight(t,n),jo(t,Ho(),{tabbar:t,button:n})};return{action:function(t){var n=t.getSystem().getByUid(o.uid).getOrDie(),e=jf.isHighlighted(n,t);(e&&o.clickToDismiss?r:e?Z:i)(n,t)},domModification:{classes:[o.markers.tabClass]}}}}),LA=at([NA]),jA=_f({name:"Tabbar",configFields:zA(),partFields:LA(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:wl(t.tabbarBehaviours,[jf.config({highlightClass:t.markers.selectedClass,itemClass:t.markers.tabClass,onHighlight:function(t,n){Br(n.element(),"aria-selected","true")},onDehighlight:function(t,n){Br(n.element(),"aria-selected","false")}}),Rm.config({mode:"flow",getInitial:function(t){return jf.getHighlighted(t).map(function(t){return t.element()})},selector:"."+t.markers.tabClass,executeOnMove:!0})])}}}),UA=Of({name:"Tabview",configFields:[yl("tabviewBehaviours",[Nm])],factory:function(t,n){return{uid:t.uid,dom:t.dom,behaviours:wl(t.tabviewBehaviours,[Nm.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),WA=at([Zn("selectFirst",!0),$u("onChangeTab"),$u("onDismissTab"),Zn("tabs",[]),yl("tabSectionBehaviours",[])]),GA=ql({factory:jA,schema:[zn("dom"),Wn("markers",[zn("tabClass"),zn("selectedClass")])],name:"tabbar",defaults:function(t){return{tabs:t.tabs}}}),XA=ql({factory:UA,name:"tabview"}),YA=at([GA,XA]),qA=_f({name:"TabSection",configFields:WA(),partFields:YA(),factory:function(i,t,n,e){var o=function(t,n){sf(t,i,"tabbar").each(function(t){n(t).each(Uo)})};return{uid:i.uid,dom:i.dom,components:t,behaviours:xl(i.tabSectionBehaviours),events:Yo(ut([i.selectFirst?[or(function(t,n){o(t,jf.getFirst)})]:[],[Jo(Ho(),function(t,n){var o,r,e=n.event().button();o=e,r=bl.getValue(o),sf(o,i,"tabview").each(function(e){L(i.tabs,function(t){return t.value===r}).each(function(t){var n=t.view();Mr(o.element(),"id").each(function(t){Br(e.element(),"aria-labelledby",t)}),Nm.set(e,n),i.onChangeTab(e,o,n)})})}),Jo(Po(),function(t,n){var e=n.event().button();i.onDismissTab(t,e)})]])),apis:{getViewItems:function(t){return sf(t,i,"tabview").map(function(t){return Nm.contents(t)}).getOr([])},showTab:function(t,e){o(t,function(n){var t=jf.getCandidates(n);return L(t,function(t){return bl.getValue(t)===e}).filter(function(t){return!jf.isHighlighted(n,t)})})}}}},apis:{getViewItems:function(t,n){return t.getViewItems(n)},showTab:function(t,n,e){t.showTab(n,e)}}}),KA=function(t,n){Di(t,"height",n+"px"),Pe().browser.isIE()?Hi(t,"flex-basis"):Di(t,"flex-basis",n+"px")},JA=function(t,o,r){Du(t,'[role="dialog"]').each(function(e){Mu(e,'[role="tablist"]').each(function(n){r.get().map(function(t){return Di(o,"height","0"),Di(o,"flex-basis","0"),Math.min(t,function(t,n,e){var o,r=cr(t).dom(),i=Du(t,".tox-dialog-wrap").getOr(t);o="fixed"===Fi(i,"position")?Math.max(r.clientHeight,nt.window.innerHeight):Math.max(r.offsetHeight,r.scrollHeight);var u=tu(n),a=n.dom().offsetLeft>=e.dom().offsetLeft+cu(e)?Math.max(tu(e),u):u,c=parseInt(Fi(t,"margin-top"),10)||0,s=parseInt(Fi(t,"margin-bottom"),10)||0;return o-(tu(t)+c+s-a)}(e,o,n))}).each(function(t){KA(o,t)})})})},$A=function(t){return Mu(t,'[role="tabpanel"]')},QA=function(a){var c;return{smartTabHeight:(c=ce(st.none()),{extraEvents:[or(function(t){var e=t.element();$A(e).each(function(u){var n;Di(u,"visibility","hidden"),t.getSystem().getByDom(u).toOption().each(function(t){var o,r,i,n=(r=u,i=t,V(o=a,function(t,n){Nm.set(i,o[n].view());var e=r.dom().getBoundingClientRect();return Nm.set(i,[]),e.height})),e=K(q(n,function(t,n){return n<t?-1:t<n?1:0}));c.set(e)}),JA(e,u,c),Hi(u,"visibility"),n=t,K(a).each(function(t){return qA.showTab(n,t.value)}),Lg.requestAnimationFrame(function(){JA(e,u,c)})})}),Jo(Do(),function(t){var n=t.element();$A(n).each(function(t){JA(n,t,c)})}),Jo(Zb,function(t,n){var r=t.element();$A(r).each(function(n){var t=Ja();Di(n,"visibility","hidden");var e=Ri(n,"height").map(function(t){return parseInt(t,10)});Hi(n,"height"),Hi(n,"flex-basis");var o=n.dom().getBoundingClientRect().height;e.forall(function(t){return t<o})?(c.set(st.from(o)),JA(r,n,c)):e.each(function(t){KA(n,t)}),Hi(n,"visibility"),t.each(Ka)})})],selectFirst:!1}),naiveTabHeight:{extraEvents:[],selectFirst:!0}}},ZA="send-data-to-section",tF="send-data-to-view",nF=Nr("update-dialog"),eF=Nr("update-title"),oF=Nr("update-body"),rF=Nr("update-footer"),iF=Nr("body-send-message"),uF=function(t,n,d,e){return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:et(et({},n.map(function(t){return{id:t}}).getOr({})),e?{"aria-live":"polite"}:{})},components:[],behaviours:za([nS(0),cT.config({channel:oF,updateState:function(t,n){return st.some({isTabPanel:function(){return"tabpanel"===n.body.type}})},renderComponents:function(t){switch(t.body.type){case"tabpanel":return[(r=t.body,i=d,u=ce({}),a=function(t){var n=bl.getValue(t),e=HA(n).getOr({}),o=u.get(),r=Ct(o,e);u.set(r)},c=function(t){var n=u.get();bl.setValue(t,n)},s=ce(null),l=V(r.tabs,function(t){return{value:t.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:i.shared.providers.translate(t.title)},view:function(){return[Gw.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"]},components:V(t.items,function(t){return QC(n,t,i)}),formBehaviours:za([Rm.config({mode:"acyclic",useTabstopAt:x(yS)}),Lm("TabView.form.events",[or(c),rr(a)]),Ya.config({channels:Jt([{key:ZA,value:{onReceive:a}},{key:tF,value:{onReceive:c}}])})])}})]}}}),f=QA(l).smartTabHeight,qA.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(t,n,e){var o=bl.getValue(n);jo(t,Qb,{name:o,oldName:s.get()}),s.set(o)},tabs:l,components:[qA.parts().tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[jA.parts().tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:za([Dy.config({})])}),qA.parts().tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:f.selectFirst,tabSectionBehaviours:za([Lm("tabpanel",f.extraEvents),Rm.config({mode:"acyclic"}),Mf.config({find:function(t){return K(qA.getViewItems(t))}}),bl.config({store:{mode:"manual",getValue:function(t){return t.getSystem().broadcastOn([ZA],{}),u.get()},setValue:function(t,n){u.set(n),t.getSystem().broadcastOn([tF],{})}}})])}))];default:return[(e=t.body,o=d,{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[(n=Ug(Gw.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:V(e.items,function(t){return QC(n,t,o)})}}))).asSpec()]}],behaviours:za([Rm.config({mode:"acyclic",useTabstopAt:x(yS)}),tS(n),cS(n,{postprocess:function(t){return HA(t).fold(function(t){return nt.console.error(t),{}},function(t){return t})}})])})]}var e,o,n,r,i,u,a,c,s,l,f},initialData:t})])}},aF=Xp.deviceType.isTouch(),cF=function(t,n){return{dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[t,n]}},sF=function(t,n){return yM.parts().close(jg.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close")}},action:t,buttonBehaviours:za([Dy.config({})])}))},lF=function(){return yM.parts().title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})},fF=function(t,n){return yM.parts().body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:gB("<p>"+n.translate(t)+"</p>")}]}]})},dF=function(t){return yM.parts().footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:t})},mF=function(t,n){return[Ub.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:t}),Ub.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:n})]},gF=function(n){var t,e="tox-dialog",o=e+"-wrap",r=o+"__backdrop",i=e+"__disable-scroll";return yM.sketch({lazySink:n.lazySink,onEscape:function(t){return n.onEscape(t),st.some(!0)},useTabstopAt:function(t){return!yS(t)},dom:{tag:"div",classes:[e].concat(n.extraClasses),styles:et({position:"relative"},n.extraStyles)},components:b([n.header,n.body],n.footer.toArray()),parts:{blocker:{dom:gB('<div class="'+o+'"></div>'),components:[{dom:{tag:"div",classes:aF?[r,r+"--opaque"]:[r]}}]}},dragBlockClass:o,modalBehaviours:za(b([Xm.config({}),Lm("dialog-events",n.dialogEvents.concat([er(io(),function(t,n){Rm.focusIn(t)})])),Lm("scroll-lock",[or(function(){bi(_i(),i)}),rr(function(){xi(_i(),i)})])],n.extraBehaviours)),eventOrder:et(((t={})[xo()]=["dialog-events"],t[Mo()]=["scroll-lock","dialog-events","alloy.base.behaviour"],t[Ao()]=["alloy.base.behaviour","dialog-events","scroll-lock"],t),n.eventOrder)})},pF=function(t){return jg.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close"),title:t.translate("Close")}},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:'<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path d="M17.953 7.453L13.422 12l4.531 4.547-1.406 1.406L12 13.422l-4.547 4.531-1.406-1.406L10.578 12 6.047 7.453l1.406-1.406L12 10.578l4.547-4.531z" fill-rule="evenodd"></path></svg>'}}],action:function(t){Lo(t,Yb)}})},hF=function(t,n,e){var o=function(t){return[Yi(e.translate(t.title))]};return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:et({},n.map(function(t){return{id:t}}).getOr({}))},components:o(t),behaviours:za([cT.config({channel:eF,renderComponents:o})])}},vF=function(){return{dom:gB('<div class="tox-dialog__draghandle"></div>')}},bF=function(t,n){return e={title:n.shared.providers.translate(t),draggable:n.dialog.isDraggableModal()},o=n.shared.providers,r=yM.parts().title(hF(e,st.none(),o)),i=yM.parts().draghandle(vF()),u=yM.parts().close(pF(o)),a=[r].concat(e.draggable?[i]:[]).concat([u]),Ub.sketch({dom:gB('<div class="tox-dialog__header"></div>'),components:a});var e,o,r,i,u,a},yF=function(t,n){return{onClose:function(){return n.closeWindow()},onBlock:function(e){yM.setBusy(t(),function(t,n){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.message()},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:n,components:[{dom:gB('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}})},onUnblock:function(){yM.setIdle(t())}}},xF=function(t,n,e,o){var r;return Ji(gF(et(et({},t),{lazySink:o.shared.getSink,extraBehaviours:b([cT.config({channel:nF,updateState:function(t,n){return st.some(n)},initialData:n}),fS({})],t.extraBehaviours),onEscape:function(t){Lo(t,Yb)},dialogEvents:e,eventOrder:((r={})[yo()]=["reflecting","receiving"],r[Mo()]=["scroll-lock","reflecting","messages","dialog-events","alloy.base.behaviour"],r[Ao()]=["alloy.base.behaviour","dialog-events","messages","reflecting","scroll-lock"],r)})))},wF=function(t){return V(t,function(t){return"menu"===t.type?(e=V((n=t).items,function(t){var n=ce(!1);return et(et({},t),{storage:n})}),et(et({},n),{items:e})):t;var n,e})},SF=function(t){return N(t,function(t,n){return"menu"!==n.type?t:N(n.items,function(t,n){return t[n.name]=n.storage,t},t)},{})},kF=function(t,e){return[tr(io(),bS),t(Xb,function(t,n){e.onClose(),n.onClose()}),t(Yb,function(t,n,e,o){n.onCancel(t),Lo(o,Xb)}),Jo($b,function(t,n){return e.onUnblock()}),Jo(Jb,function(t,n){return e.onBlock(n.event())})]},CF=function(i,t){var n=function(t,r){return Jo(t,function(e,o){u(e,function(t,n){r(i(),t,o.event(),e)})})},u=function(n,e){cT.getState(n).get().each(function(t){e(t,n)})};return b(kF(n,t),[n(qb,function(t,n,e){n.onAction(t,{name:e.name()})})])},OF=function(i,t,a){var n=function(t,r){return Jo(t,function(e,o){u(e,function(t,n){r(i(),t,o.event(),e)})})},u=function(n,e){cT.getState(n).get().each(function(t){e(t.internalDialog,n)})};return b(kF(n,t),[n(Kb,function(t,n){return n.onSubmit(t)}),n(Gb,function(t,n,e){n.onChange(t,{name:e.name()})}),n(qb,function(t,n,e,o){var r=function(){return Rm.focusIn(o)},i=function(t){return Ar(t,"disabled")||Mr(t,"aria-disabled").exists(function(t){return"true"===t})},u=Ja();n.onAction(t,{name:e.name(),value:e.value()}),Ja().fold(r,function(n){i(n)||u.exists(function(t){return Ue(n,t)&&i(t)})?r():a().toOption().filter(function(t){return!Ue(t.element(),n)}).each(r)})}),n(Qb,function(t,n,e){n.onTabChange(t,{newTabName:e.name(),oldTabName:e.oldName()})}),rr(function(t){var n=i();bl.setValue(t,n.getData())})])},_F=function(t,n){var e=n.map(function(t){return t.footerButtons}).getOr([]),o=H(e,function(t){return"start"===t.align}),r=function(t,n){return Ub.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+t]},components:V(n,function(t){return t.memento.asSpec()})})};return[r("start",o.pass),r("end",o.fail)]},TF=function(t,i){return{dom:gB('<div class="tox-dialog__footer"></div>'),components:[],behaviours:za([cT.config({channel:rF,initialData:t,updateState:function(t,n){var r=V(n.buttons,function(t){var n,e,o=Ug((e=i,Nk(n=t,n.type,e)));return{name:t.name,align:t.align,memento:o}});return st.some({lookupByName:function(t,n){return e=t,o=n,L(r,function(t){return t.name===o}).bind(function(t){return t.memento.getOpt(e)});var e,o},footerButtons:r})},renderComponents:_F})])}},EF=function(t,n){return yM.parts().footer(TF(t,n))},BF=function(n,e){if(n.getRoot().getSystem().isConnected()){var o=Mf.getCurrent(n.getFormWrapper()).getOr(n.getFormWrapper());return Gw.getField(o,e).fold(function(){var t=n.getFooter();return cT.getState(t).get().bind(function(t){return t.lookupByName(o,e)})},function(t){return st.some(t)})}return st.none()},DF=function(c,o,s){var t=function(t){var n=c.getRoot();n.getSystem().isConnected()&&t(n)},l={getData:function(){var t=c.getRoot(),n=t.getSystem().isConnected()?c.getFormWrapper():t,e=bl.getValue(n),o=dt(s,function(t){return t.get()});return et(et({},e),o)},setData:function(a){t(function(t){var n,e,o=l.getData(),r=et(et({},o),a),i=(n=r,e=c.getRoot(),cT.getState(e).get().map(function(t){return On(Cn("data",t.dataValidator,n))}).getOr(n)),u=c.getFormWrapper();bl.setValue(u,i),ft(s,function(t,n){yt(r,n)&&t.set(r[n])})})},disable:function(t){BF(c,t).each(ph.disable)},enable:function(t){BF(c,t).each(ph.enable)},focus:function(t){BF(c,t).each(Xm.focus)},block:function(n){if(!S(n))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t(function(t){jo(t,Jb,{message:n})})},unblock:function(){t(function(t){Lo(t,$b)})},showTab:function(e){t(function(t){var n=c.getBody();cT.getState(n).get().exists(function(t){return t.isTabPanel()})&&Mf.getCurrent(n).each(function(t){qA.showTab(t,e)})})},redial:function(e){t(function(t){var n=o(e);t.getSystem().broadcastOn([nF],n),t.getSystem().broadcastOn([eF],n.internalDialog),t.getSystem().broadcastOn([oF],n.internalDialog),t.getSystem().broadcastOn([rF],n.internalDialog),l.setData(n.initialData)})},close:function(){t(function(t){Lo(t,Xb)})}};return l},MF=function(t,n,e){var o,r,i,u=bF(t.internalDialog.title,e),a=(o={body:t.internalDialog.body},r=e,i=uF(o,st.none(),r,!1),yM.parts().body(i)),c=wF(t.internalDialog.buttons),s=SF(c),l=EF({buttons:c},e),f=OF(function(){return p},yF(function(){return g},n),e.shared.getSink),d="normal"!==t.internalDialog.size?"large"===t.internalDialog.size?["tox-dialog--width-lg"]:["tox-dialog--width-md"]:[],m={header:u,body:a,footer:st.some(l),extraClasses:d,extraBehaviours:[],extraStyles:{}},g=xF(m,t,f,e),p=DF({getRoot:function(){return g},getBody:function(){return yM.getBody(g)},getFooter:function(){return yM.getFooter(g)},getFormWrapper:function(){var t=yM.getBody(g);return Mf.getCurrent(t).getOr(t)}},n.redial,s);return{dialog:g,instanceApi:p}},AF=function(t,n,e,o){var r,i,u,a,c,s,l,f,d,m=Nr("dialog-label"),g=Nr("dialog-content"),p=Ug((u={title:t.internalDialog.title,draggable:!0},a=m,c=e.shared.providers,Ub.sketch({dom:gB('<div class="tox-dialog__header"></div>'),components:[hF(u,st.some(a),c),vF(),pF(c)],containerBehaviours:za([iM.config({mode:"mouse",blockerClass:"blocker",getTarget:function(t){return Au(t,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))),h=Ug((s={body:t.internalDialog.body},l=g,f=e,d=o,uF(s,st.some(l),f,d))),v=wF(t.internalDialog.buttons),b=SF(v),y=Ug(TF({buttons:v},e)),x=OF(function(){return S},{onBlock:function(){},onUnblock:function(){},onClose:function(){return n.closeWindow()}},e.shared.getSink),w=Ji({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:((r={role:"dialog"})["aria-labelledby"]=m,r["aria-describedby"]=""+g,r)},eventOrder:((i={})[yo()]=[cT.name(),Ya.name()],i[xo()]=["execute-on-form"],i[Mo()]=["reflecting","execute-on-form"],i),behaviours:za([Rm.config({mode:"cyclic",onEscape:function(t){return Lo(t,Xb),st.some(!0)},useTabstopAt:function(t){return!yS(t)&&("button"!==Cr(t)||"disabled"!==Dr(t,"disabled"))}}),cT.config({channel:nF,updateState:function(t,n){return st.some(n)},initialData:t}),Xm.config({}),Lm("execute-on-form",x.concat([er(io(),function(t,n){Rm.focusIn(t)})])),fS({})]),components:[p.asSpec(),h.asSpec(),y.asSpec()]}),S=DF({getRoot:function(){return w},getFooter:function(){return y.get(w)},getBody:function(){return h.get(w)},getFormWrapper:function(){var t=h.get(w);return Mf.getCurrent(t).getOr(t)}},n.redial,b);return{dialog:w,instanceApi:S}},FF=tinymce.util.Tools.resolve("tinymce.util.URI"),IF=["insertContent","setContent","execCommand","close","block","unblock"],RF=function(t){return k(t)&&-1!==IF.indexOf(t.mceAction)},VF=function(o,t,r,n){var e,i,u,a,c=bF(o.title,n),s=(i={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[hS({dom:{tag:"iframe",attributes:{src:o.url}},behaviours:za([Dy.config({}),Xm.config({})])})]}],behaviours:za([Rm.config({mode:"acyclic",useTabstopAt:x(yS)})])},yM.parts().body(i)),l=o.buttons.bind(function(t){return 0===t.length?st.none():st.some(EF({buttons:t},n))}),f=CF(function(){return y},yF(function(){return b},t)),d=et(et({},o.height.fold(function(){return{}},function(t){return{height:t+"px","max-height":t+"px"}})),o.width.fold(function(){return{}},function(t){return{width:t+"px","max-width":t+"px"}})),m=o.width.isNone()&&o.height.isNone()?["tox-dialog--width-lg"]:[],g=new FF(o.url,{base_uri:new FF(nt.window.location.href)}),p=g.protocol+"://"+g.host+(g.port?":"+g.port:""),h=ce(st.none()),v=[Lm("messages",[or(function(){var t=yb(le.fromDom(nt.window),"message",function(t){if(g.isSameOrigin(new FF(t.raw().origin))){var n=t.raw().data;RF(n)?function(t,n,e){switch(e.mceAction){case"insertContent":t.insertContent(e.content);break;case"setContent":t.setContent(e.content);break;case"execCommand":var o=!!O(e.ui)&&e.ui;t.execCommand(e.cmd,o,e.value);break;case"close":n.close();break;case"block":n.block(e.message);break;case"unblock":n.unblock()}}(r,y,n):!RF(e=n)&&k(e)&&yt(e,"mceAction")&&o.onMessage(y,n)}var e});h.set(st.some(t))}),rr(function(){h.get().each(function(t){return t.unbind()})})]),Ya.config({channels:((e={})[iF]={onReceive:function(t,n){Mu(t.element(),"iframe").each(function(t){t.dom().contentWindow.postMessage(n,p)})}},e)})],b=xF({header:c,body:s,footer:l,extraClasses:m,extraBehaviours:v,extraStyles:d},o,f,n),y=(a=function(t){u.getSystem().isConnected()&&t(u)},{block:function(n){if(!S(n))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");a(function(t){jo(t,Jb,{message:n})})},unblock:function(){a(function(t){Lo(t,$b)})},close:function(){a(function(t){Lo(t,Xb)})},sendMessage:function(n){a(function(t){t.getSystem().broadcastOn([iF],n)})}});return{dialog:u=b,instanceApi:y}},HF=function(t){var c,s,l,f,p=t.backstage,h=t.editor,v=Ph(h),e=(s=(c=t).backstage.shared,{open:function(t,n){var e=function(){yM.hide(u),n()},o=Ug(Nk({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:st.none()},"cancel",c.backstage)),r=lF(),i=sF(e,s.providers),u=Ji(gF({lazySink:function(){return s.getSink()},header:cF(r,i),body:fF(t,s.providers),footer:st.some(dF(mF([],[o.asSpec()]))),onEscape:e,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Jo(Yb,e)],eventOrder:{}}));yM.show(u);var a=o.get(u);Xm.focus(a)}}),o=(f=(l=t).backstage.shared,{open:function(t,n){var e=function(t){yM.hide(a),n(t)},o=Ug(Nk({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:st.none()},"submit",l.backstage)),r=Nk({name:"no",text:"No",primary:!1,align:"end",disabled:!1,icon:st.none()},"cancel",l.backstage),i=lF(),u=sF(function(){return e(!1)},f.providers),a=Ji(gF({lazySink:function(){return f.getSink()},header:cF(i,u),body:fF(t,f.providers),footer:st.some(dF(mF([],[r,o.asSpec()]))),onEscape:function(){return e(!1)},extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Jo(Yb,function(){return e(!1)}),Jo(Kb,function(){return e(!0)})],eventOrder:{}}));yM.show(a);var c=o.get(a);Xm.focus(c)}}),r=function(t,e){return VA.openUrl(function(t){var n=VF(t,{closeWindow:function(){yM.hide(n.dialog),e(n.instanceApi)}},h,p);return yM.show(n.dialog),n.instanceApi},t)},i=function(t,i){return VA.open(function(t,n,e){var o=n,r=MF({dataValidator:e,initialData:o,internalDialog:t},{redial:VA.redial,closeWindow:function(){yM.hide(r.dialog),i(r.instanceApi)}},p);return yM.show(r.dialog),r.instanceApi.setData(o),r.instanceApi},t)},u=function(t,d,m,g){return VA.open(function(t,n,e){var o,r,i,u=On(Cn("data",e,n)),a=(o=ce(st.none()),{clear:function(){o.set(st.none())},set:function(t){o.set(st.some(t))},isSet:function(){return o.get().isSome()},on:function(t){o.get().each(t)}}),c=p.shared.header.isPositionedAtTop(),s=function(){return a.on(function(t){Bg.reposition(t),LE.refresh(t)})},l=AF({dataValidator:e,initialData:u,internalDialog:t},{redial:VA.redial,closeWindow:function(){a.on(Bg.hide),h.off("ResizeEditor",s),a.clear(),m(l.instanceApi)}},p,g),f=Ji(Bg.sketch(et(et({lazySink:p.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{}},c?{}:{fireRepositionEventInstead:{}}),{inlineBehaviours:za(b([Lm("window-manager-inline-events",[Jo(Fo(),function(t,n){Lo(l.dialog,Yb)})])],(r=h,i=c,v&&i?[]:[LE.config({contextual:{lazyContext:function(){return st.some(wu(le.fromDom(r.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})]))),isExtraPart:function(t,n){return Sb(e=n,".tox-alert-dialog")||Sb(e,".tox-confirm-dialog");var e}})));return a.set(f),Bg.showWithin(f,d,$i(l.dialog),st.some(_i())),v&&c||(LE.refresh(f),h.on("ResizeEditor",s)),l.instanceApi.setData(u),Rm.focusIn(l.dialog),l.instanceApi},t)};return{open:function(t,n,e){return n!==undefined&&"toolbar"===n.inline?u(t,p.shared.anchors.inlineDialog(),e,n.ariaAttrs):n!==undefined&&"cursor"===n.inline?u(t,p.shared.anchors.cursor(),e,n.ariaAttrs):i(t,e)},openUrl:function(t,n){return r(t,n)},alert:function(t,n){e.open(t,function(){n()})},close:function(t){t.close()},confirm:function(t,n){o.open(t,function(t){n(t)})}}};!function XF(){t.add("silver",function(t){var n=pM(t),e=n.uiMothership,o=n.backstage,r=n.renderUI,i=n.getUi;vb(t,o.shared);var u=HF({editor:t,backstage:o});return{renderUI:r,getWindowManagerImpl:at(u),getNotificationManagerImpl:function(){return qg(0,{backstage:o},e)},ui:i()}})}()}(window);