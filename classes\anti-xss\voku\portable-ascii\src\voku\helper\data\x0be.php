<?php

return [
    'byum',    // 0x00
    'byub',    // 0x01
    'byubs',    // 0x02
    'byus',    // 0x03
    'byuss',    // 0x04
    'byung',    // 0x05
    'byuj',    // 0x06
    'byuc',    // 0x07
    'byuk',    // 0x08
    'byut',    // 0x09
    'byup',    // 0x0a
    'byuh',    // 0x0b
    'beu',    // 0x0c
    'beug',    // 0x0d
    'beugg',    // 0x0e
    'beugs',    // 0x0f
    'beun',    // 0x10
    'beunj',    // 0x11
    'beunh',    // 0x12
    'beud',    // 0x13
    'beul',    // 0x14
    'beulg',    // 0x15
    'beulm',    // 0x16
    'beulb',    // 0x17
    'beuls',    // 0x18
    'beult',    // 0x19
    'beulp',    // 0x1a
    'beulh',    // 0x1b
    'beum',    // 0x1c
    'beub',    // 0x1d
    'beubs',    // 0x1e
    'beus',    // 0x1f
    'beuss',    // 0x20
    'beung',    // 0x21
    'beuj',    // 0x22
    'beuc',    // 0x23
    'beuk',    // 0x24
    'beut',    // 0x25
    'beup',    // 0x26
    'beuh',    // 0x27
    'byi',    // 0x28
    'byig',    // 0x29
    'byigg',    // 0x2a
    'byigs',    // 0x2b
    'byin',    // 0x2c
    'byinj',    // 0x2d
    'byinh',    // 0x2e
    'byid',    // 0x2f
    'byil',    // 0x30
    'byilg',    // 0x31
    'byilm',    // 0x32
    'byilb',    // 0x33
    'byils',    // 0x34
    'byilt',    // 0x35
    'byilp',    // 0x36
    'byilh',    // 0x37
    'byim',    // 0x38
    'byib',    // 0x39
    'byibs',    // 0x3a
    'byis',    // 0x3b
    'byiss',    // 0x3c
    'bying',    // 0x3d
    'byij',    // 0x3e
    'byic',    // 0x3f
    'byik',    // 0x40
    'byit',    // 0x41
    'byip',    // 0x42
    'byih',    // 0x43
    'bi',    // 0x44
    'big',    // 0x45
    'bigg',    // 0x46
    'bigs',    // 0x47
    'bin',    // 0x48
    'binj',    // 0x49
    'binh',    // 0x4a
    'bid',    // 0x4b
    'bil',    // 0x4c
    'bilg',    // 0x4d
    'bilm',    // 0x4e
    'bilb',    // 0x4f
    'bils',    // 0x50
    'bilt',    // 0x51
    'bilp',    // 0x52
    'bilh',    // 0x53
    'bim',    // 0x54
    'bib',    // 0x55
    'bibs',    // 0x56
    'bis',    // 0x57
    'biss',    // 0x58
    'bing',    // 0x59
    'bij',    // 0x5a
    'bic',    // 0x5b
    'bik',    // 0x5c
    'bit',    // 0x5d
    'bip',    // 0x5e
    'bih',    // 0x5f
    'bba',    // 0x60
    'bbag',    // 0x61
    'bbagg',    // 0x62
    'bbags',    // 0x63
    'bban',    // 0x64
    'bbanj',    // 0x65
    'bbanh',    // 0x66
    'bbad',    // 0x67
    'bbal',    // 0x68
    'bbalg',    // 0x69
    'bbalm',    // 0x6a
    'bbalb',    // 0x6b
    'bbals',    // 0x6c
    'bbalt',    // 0x6d
    'bbalp',    // 0x6e
    'bbalh',    // 0x6f
    'bbam',    // 0x70
    'bbab',    // 0x71
    'bbabs',    // 0x72
    'bbas',    // 0x73
    'bbass',    // 0x74
    'bbang',    // 0x75
    'bbaj',    // 0x76
    'bbac',    // 0x77
    'bbak',    // 0x78
    'bbat',    // 0x79
    'bbap',    // 0x7a
    'bbah',    // 0x7b
    'bbae',    // 0x7c
    'bbaeg',    // 0x7d
    'bbaegg',    // 0x7e
    'bbaegs',    // 0x7f
    'bbaen',    // 0x80
    'bbaenj',    // 0x81
    'bbaenh',    // 0x82
    'bbaed',    // 0x83
    'bbael',    // 0x84
    'bbaelg',    // 0x85
    'bbaelm',    // 0x86
    'bbaelb',    // 0x87
    'bbaels',    // 0x88
    'bbaelt',    // 0x89
    'bbaelp',    // 0x8a
    'bbaelh',    // 0x8b
    'bbaem',    // 0x8c
    'bbaeb',    // 0x8d
    'bbaebs',    // 0x8e
    'bbaes',    // 0x8f
    'bbaess',    // 0x90
    'bbaeng',    // 0x91
    'bbaej',    // 0x92
    'bbaec',    // 0x93
    'bbaek',    // 0x94
    'bbaet',    // 0x95
    'bbaep',    // 0x96
    'bbaeh',    // 0x97
    'bbya',    // 0x98
    'bbyag',    // 0x99
    'bbyagg',    // 0x9a
    'bbyags',    // 0x9b
    'bbyan',    // 0x9c
    'bbyanj',    // 0x9d
    'bbyanh',    // 0x9e
    'bbyad',    // 0x9f
    'bbyal',    // 0xa0
    'bbyalg',    // 0xa1
    'bbyalm',    // 0xa2
    'bbyalb',    // 0xa3
    'bbyals',    // 0xa4
    'bbyalt',    // 0xa5
    'bbyalp',    // 0xa6
    'bbyalh',    // 0xa7
    'bbyam',    // 0xa8
    'bbyab',    // 0xa9
    'bbyabs',    // 0xaa
    'bbyas',    // 0xab
    'bbyass',    // 0xac
    'bbyang',    // 0xad
    'bbyaj',    // 0xae
    'bbyac',    // 0xaf
    'bbyak',    // 0xb0
    'bbyat',    // 0xb1
    'bbyap',    // 0xb2
    'bbyah',    // 0xb3
    'bbyae',    // 0xb4
    'bbyaeg',    // 0xb5
    'bbyaegg',    // 0xb6
    'bbyaegs',    // 0xb7
    'bbyaen',    // 0xb8
    'bbyaenj',    // 0xb9
    'bbyaenh',    // 0xba
    'bbyaed',    // 0xbb
    'bbyael',    // 0xbc
    'bbyaelg',    // 0xbd
    'bbyaelm',    // 0xbe
    'bbyaelb',    // 0xbf
    'bbyaels',    // 0xc0
    'bbyaelt',    // 0xc1
    'bbyaelp',    // 0xc2
    'bbyaelh',    // 0xc3
    'bbyaem',    // 0xc4
    'bbyaeb',    // 0xc5
    'bbyaebs',    // 0xc6
    'bbyaes',    // 0xc7
    'bbyaess',    // 0xc8
    'bbyaeng',    // 0xc9
    'bbyaej',    // 0xca
    'bbyaec',    // 0xcb
    'bbyaek',    // 0xcc
    'bbyaet',    // 0xcd
    'bbyaep',    // 0xce
    'bbyaeh',    // 0xcf
    'bbeo',    // 0xd0
    'bbeog',    // 0xd1
    'bbeogg',    // 0xd2
    'bbeogs',    // 0xd3
    'bbeon',    // 0xd4
    'bbeonj',    // 0xd5
    'bbeonh',    // 0xd6
    'bbeod',    // 0xd7
    'bbeol',    // 0xd8
    'bbeolg',    // 0xd9
    'bbeolm',    // 0xda
    'bbeolb',    // 0xdb
    'bbeols',    // 0xdc
    'bbeolt',    // 0xdd
    'bbeolp',    // 0xde
    'bbeolh',    // 0xdf
    'bbeom',    // 0xe0
    'bbeob',    // 0xe1
    'bbeobs',    // 0xe2
    'bbeos',    // 0xe3
    'bbeoss',    // 0xe4
    'bbeong',    // 0xe5
    'bbeoj',    // 0xe6
    'bbeoc',    // 0xe7
    'bbeok',    // 0xe8
    'bbeot',    // 0xe9
    'bbeop',    // 0xea
    'bbeoh',    // 0xeb
    'bbe',    // 0xec
    'bbeg',    // 0xed
    'bbegg',    // 0xee
    'bbegs',    // 0xef
    'bben',    // 0xf0
    'bbenj',    // 0xf1
    'bbenh',    // 0xf2
    'bbed',    // 0xf3
    'bbel',    // 0xf4
    'bbelg',    // 0xf5
    'bbelm',    // 0xf6
    'bbelb',    // 0xf7
    'bbels',    // 0xf8
    'bbelt',    // 0xf9
    'bbelp',    // 0xfa
    'bbelh',    // 0xfb
    'bbem',    // 0xfc
    'bbeb',    // 0xfd
    'bbebs',    // 0xfe
    'bbes',    // 0xff
];
