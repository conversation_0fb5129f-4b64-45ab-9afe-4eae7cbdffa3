@font-face {
	font-family: 'Metrize-Icons';
	src:url('fonts/Metrize-Icons.eot');
	src:url('fonts/Metrize-Icons.eot?#iefix') format('embedded-opentype'),
		url('fonts/Metrize-Icons.woff') format('woff'),
		url('fonts/Metrize-Icons.ttf') format('truetype'),
		url('fonts/Metrize-Icons.svg#Metrize-Icons') format('svg');
	font-weight: normal;
	font-style: normal;
}

/* Use the following CSS code if you want to use data attributes for inserting your icons */
[data-icon]:before {
	font-family: 'Metrize-Icons';
	content: attr(data-icon);
	speak: none;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
}

/* Use the following CSS code if you want to have a class per icon */
/*
Instead of a list of all class selectors,
you can use the generic selector below, but it's slower:
[class*="icon-"] {
*/
.icon-yen, .icon-world, .icon-wireframe-globe, .icon-wind, .icon-wifi, .icon-waves, .icon-viewport, .icon-viewport-video, .icon-user, .icon-user-remove, .icon-user-ban, .icon-user-add, .icon-upload, .icon-upload-selection, .icon-upload-selection-circle, .icon-underline, .icon-triple-points, .icon-top-bottom, .icon-three-points, .icon-three-points-top, .icon-three-points-bottom, .icon-text-width, .icon-text-size-upper, .icon-text-size-reduce, .icon-text-paragraph, .icon-text-normal, .icon-text-justify-right, .icon-text-justify-left, .icon-text-justify-center, .icon-text-height, .icon-text-center, .icon-text-bold, .icon-text-align-right, .icon-text-align-left, .icon-telephone, .icon-sunshine, .icon-sun, .icon-stop, .icon-star, .icon-speed, .icon-sound-on, .icon-sound-off, .icon-sos, .icon-social-zerply, .icon-social-youtube, .icon-social-yelp, .icon-social-yahoo, .icon-social-wordpress, .icon-social-virb, .icon-social-vimeo, .icon-social-viddler, .icon-social-twitter, .icon-social-tumblr, .icon-social-stumbleupon, .icon-social-soundcloud, .icon-social-skype, .icon-social-sharethis, .icon-social-quora, .icon-social-pinterest, .icon-social-photobucket, .icon-social-paypal, .icon-social-myspace, .icon-social-linkedin, .icon-social-last-fm, .icon-social-grooveshark, .icon-social-google-plus, .icon-social-github, .icon-social-forrst, .icon-social-flickr, .icon-social-facebook, .icon-social-evernote, .icon-social-envato, .icon-social-email, .icon-social-dribbble, .icon-social-digg, .icon-social-deviantart, .icon-social-blogger, .icon-social-behance, .icon-social-bebo, .icon-social-addthis, .icon-social-500px, .icon-snow, .icon-sliders, .icon-sliders-vertical, .icon-sign-male, .icon-sign-female, .icon-shield, .icon-settings, .icon-setting, .icon-select-square, .icon-select-circle, .icon-search, .icon-scale, .icon-rules, .icon-rss, .icon-retweet, .icon-report-comment, .icon-refresh, .icon-rec, .icon-random, .icon-quote, .icon-question, .icon-previous-fast-step, .icon-prev-step, .icon-pounds, .icon-podcast, .icon-plus, .icon-play, .icon-pin, .icon-pin-map, .icon-pig-money, .icon-pause, .icon-paperclip, .icon-paperclip-oblique, .icon-options-settings, .icon-officine, .icon-officine-2, .icon-off, .icon-number-zero, .icon-number-two, .icon-number-three, .icon-number-six, .icon-number-seven, .icon-number-one, .icon-number-nine, .icon-number-four, .icon-number-five, .icon-number-eight, .icon-next-step, .icon-next-fast-step, .icon-music, .icon-multi-borders, .icon-minus, .icon-marker, .icon-marker-points, .icon-marker-minus, .icon-marker-add, .icon-map, .icon-male-symbol, .icon-mailbox, .icon-mail, .icon-magnet, .icon-magic-wand, .icon-login-lock-refresh, .icon-locked, .icon-location, .icon-location-maps, .icon-list-square, .icon-list-circle, .icon-link-url, .icon-line-through, .icon-limit-directions, .icon-like-upload, .icon-like-remove, .icon-like-download, .icon-like-close, .icon-like-ban, .icon-like-add, .icon-left-right, .icon-leaf, .icon-layers, .icon-landscape, .icon-key, .icon-italic, .icon-info, .icon-idea, .icon-home-wifi, .icon-heart, .icon-hdd, .icon-hdd-raid, .icon-hdd-net, .icon-grids, .icon-grid-big, .icon-graphs, .icon-forward, .icon-fire, .icon-female-symbol, .icon-eye, .icon-eye-disabled, .icon-expand, .icon-expand-vertical, .icon-expand-horizontal, .icon-expand-directions, .icon-exclamation, .icon-euro, .icon-email-upload, .icon-email-spam, .icon-email-remove, .icon-email-luminosity, .icon-email-download, .icon-email-close, .icon-email-add, .icon-eject, .icon-drops, .icon-drop, .icon-download, .icon-download-selection, .icon-download-selection-circle, .icon-double-diamonds, .icon-dot-square, .icon-dot-line, .icon-dot-circle, .icon-dollar, .icon-documents, .icon-document, .icon-document-fill, .icon-directions, .icon-cross, .icon-credit-card, .icon-copy-paste-document, .icon-copy-document, .icon-contract-vertical, .icon-contract-horizontal, .icon-contract-directions, .icon-compass, .icon-compass-2, .icon-comments, .icon-comment, .icon-coins, .icon-cloud, .icon-cloud-upload, .icon-cloud-remove, .icon-cloud-download, .icon-cloud-add, .icon-clock, .icon-circles, .icon-check, .icon-chat, .icon-chart-down, .icon-cd-dvd-rom, .icon-camera, .icon-button-question, .icon-button-minus, .icon-button-exclamation, .icon-button-email, .icon-button-close, .icon-button-check, .icon-button-add, .icon-brush, .icon-browser-sizes, .icon-box-remove, .icon-box-close, .icon-box-blank, .icon-box-add, .icon-bolt, .icon-block-menu, .icon-blank, .icon-bezier, .icon-bars, .icon-ban-circle, .icon-bag, .icon-backward, .icon-axis-rules, .icon-atom, .icon-arrow-up, .icon-arrow-up-thin, .icon-arrow-up-light, .icon-arrow-up-bold, .icon-arrow-up-bold-round, .icon-arrow-up-big, .icon-arrow-right, .icon-arrow-right-thin, .icon-arrow-right-light, .icon-arrow-right-bold, .icon-arrow-right-bold-round, .icon-arrow-right-big, .icon-arrow-oblique-expand, .icon-arrow-oblique-expand-directions, .icon-arrow-oblique-contract, .icon-arrow-oblique-contract-directions, .icon-arrow-multi-line-up, .icon-arrow-multi-line-right, .icon-arrow-multi-line-left, .icon-arrow-multi-line-down, .icon-arrow-left, .icon-arrow-left-thin, .icon-arrow-left-light, .icon-arrow-left-bold, .icon-arrow-left-bold-round, .icon-arrow-left-big, .icon-arrow-fill-up, .icon-arrow-fill-right, .icon-arrow-fill-left, .icon-arrow-fill-down, .icon-arrow-down, .icon-arrow-down-thin, .icon-arrow-down-light, .icon-arrow-down-bold, .icon-arrow-down-bold-round, .icon-arrow-down-big, .icon-arrow-cycling, .icon-arrow-cycle, .icon-arrow-curve-right, .icon-arrow-curve-recycle, .icon-arrow-curve-left, .icon-animal-footprint, .icon-alarm-clock, .icon-air-plane, .icon-adjust, .icon-cube {
	font-family: 'Metrize-Icons';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
}
.icon-yen:before {
	content: "\e000";
}
.icon-world:before {
	content: "\e001";
}
.icon-wireframe-globe:before {
	content: "\e002";
}
.icon-wind:before {
	content: "\e003";
}
.icon-wifi:before {
	content: "\e004";
}
.icon-waves:before {
	content: "\e005";
}
.icon-viewport:before {
	content: "\e006";
}
.icon-viewport-video:before {
	content: "\e007";
}
.icon-user:before {
	content: "\e008";
}
.icon-user-remove:before {
	content: "\e009";
}
.icon-user-ban:before {
	content: "\e00a";
}
.icon-user-add:before {
	content: "\e00b";
}
.icon-upload:before {
	content: "\e00c";
}
.icon-upload-selection:before {
	content: "\e00d";
}
.icon-upload-selection-circle:before {
	content: "\e00e";
}
.icon-underline:before {
	content: "\e00f";
}
.icon-triple-points:before {
	content: "\e010";
}
.icon-top-bottom:before {
	content: "\e011";
}
.icon-three-points:before {
	content: "\e012";
}
.icon-three-points-top:before {
	content: "\e013";
}
.icon-three-points-bottom:before {
	content: "\e014";
}
.icon-text-width:before {
	content: "\e015";
}
.icon-text-size-upper:before {
	content: "\e016";
}
.icon-text-size-reduce:before {
	content: "\e017";
}
.icon-text-paragraph:before {
	content: "\e018";
}
.icon-text-normal:before {
	content: "\e019";
}
.icon-text-justify-right:before {
	content: "\e01a";
}
.icon-text-justify-left:before {
	content: "\e01b";
}
.icon-text-justify-center:before {
	content: "\e01c";
}
.icon-text-height:before {
	content: "\e01d";
}
.icon-text-center:before {
	content: "\e01e";
}
.icon-text-bold:before {
	content: "\e01f";
}
.icon-text-align-right:before {
	content: "\e020";
}
.icon-text-align-left:before {
	content: "\e021";
}
.icon-telephone:before {
	content: "\e022";
}
.icon-sunshine:before {
	content: "\e023";
}
.icon-sun:before {
	content: "\e024";
}
.icon-stop:before {
	content: "\e025";
}
.icon-star:before {
	content: "\e026";
}
.icon-speed:before {
	content: "\e027";
}
.icon-sound-on:before {
	content: "\e028";
}
.icon-sound-off:before {
	content: "\e029";
}
.icon-sos:before {
	content: "\e02a";
}
.icon-social-zerply:before {
	content: "\e02b";
}
.icon-social-youtube:before {
	content: "\e02c";
}
.icon-social-yelp:before {
	content: "\e02d";
}
.icon-social-yahoo:before {
	content: "\e02e";
}
.icon-social-wordpress:before {
	content: "\e02f";
}
.icon-social-virb:before {
	content: "\e030";
}
.icon-social-vimeo:before {
	content: "\e031";
}
.icon-social-viddler:before {
	content: "\e032";
}
.icon-social-twitter:before {
	content: "\e033";
}
.icon-social-tumblr:before {
	content: "\e034";
}
.icon-social-stumbleupon:before {
	content: "\e035";
}
.icon-social-soundcloud:before {
	content: "\e036";
}
.icon-social-skype:before {
	content: "\e037";
}
.icon-social-sharethis:before {
	content: "\e038";
}
.icon-social-quora:before {
	content: "\e039";
}
.icon-social-pinterest:before {
	content: "\e03a";
}
.icon-social-photobucket:before {
	content: "\e03b";
}
.icon-social-paypal:before {
	content: "\e03c";
}
.icon-social-myspace:before {
	content: "\e03d";
}
.icon-social-linkedin:before {
	content: "\e03e";
}
.icon-social-last-fm:before {
	content: "\e03f";
}
.icon-social-grooveshark:before {
	content: "\e040";
}
.icon-social-google-plus:before {
	content: "\e041";
}
.icon-social-github:before {
	content: "\e042";
}
.icon-social-forrst:before {
	content: "\e043";
}
.icon-social-flickr:before {
	content: "\e044";
}
.icon-social-facebook:before {
	content: "\e045";
}
.icon-social-evernote:before {
	content: "\e046";
}
.icon-social-envato:before {
	content: "\e047";
}
.icon-social-email:before {
	content: "\e048";
}
.icon-social-dribbble:before {
	content: "\e049";
}
.icon-social-digg:before {
	content: "\e04a";
}
.icon-social-deviantart:before {
	content: "\e04b";
}
.icon-social-blogger:before {
	content: "\e04c";
}
.icon-social-behance:before {
	content: "\e04d";
}
.icon-social-bebo:before {
	content: "\e04e";
}
.icon-social-addthis:before {
	content: "\e04f";
}
.icon-social-500px:before {
	content: "\e050";
}
.icon-snow:before {
	content: "\e051";
}
.icon-sliders:before {
	content: "\e052";
}
.icon-sliders-vertical:before {
	content: "\e053";
}
.icon-sign-male:before {
	content: "\e054";
}
.icon-sign-female:before {
	content: "\e055";
}
.icon-shield:before {
	content: "\e056";
}
.icon-settings:before {
	content: "\e057";
}
.icon-setting:before {
	content: "\e058";
}
.icon-select-square:before {
	content: "\e059";
}
.icon-select-circle:before {
	content: "\e05a";
}
.icon-search:before {
	content: "\e05b";
}
.icon-scale:before {
	content: "\e05c";
}
.icon-rules:before {
	content: "\e05d";
}
.icon-rss:before {
	content: "\e05e";
}
.icon-retweet:before {
	content: "\e05f";
}
.icon-report-comment:before {
	content: "\e060";
}
.icon-refresh:before {
	content: "\e061";
}
.icon-rec:before {
	content: "\e062";
}
.icon-random:before {
	content: "\e063";
}
.icon-quote:before {
	content: "\e064";
}
.icon-question:before {
	content: "\e065";
}
.icon-previous-fast-step:before {
	content: "\e066";
}
.icon-prev-step:before {
	content: "\e067";
}
.icon-pounds:before {
	content: "\e068";
}
.icon-podcast:before {
	content: "\e069";
}
.icon-plus:before {
	content: "\e06a";
}
.icon-play:before {
	content: "\e06b";
}
.icon-pin:before {
	content: "\e06c";
}
.icon-pin-map:before {
	content: "\e06d";
}
.icon-pig-money:before {
	content: "\e06e";
}
.icon-pause:before {
	content: "\e06f";
}
.icon-paperclip:before {
	content: "\e070";
}
.icon-paperclip-oblique:before {
	content: "\e071";
}
.icon-options-settings:before {
	content: "\e072";
}
.icon-officine:before {
	content: "\e073";
}
.icon-officine-2:before {
	content: "\e074";
}
.icon-off:before {
	content: "\e075";
}
.icon-number-zero:before {
	content: "\e076";
}
.icon-number-two:before {
	content: "\e077";
}
.icon-number-three:before {
	content: "\e078";
}
.icon-number-six:before {
	content: "\e079";
}
.icon-number-seven:before {
	content: "\e07a";
}
.icon-number-one:before {
	content: "\e07b";
}
.icon-number-nine:before {
	content: "\e07c";
}
.icon-number-four:before {
	content: "\e07d";
}
.icon-number-five:before {
	content: "\e07e";
}
.icon-number-eight:before {
	content: "\e07f";
}
.icon-next-step:before {
	content: "\e080";
}
.icon-next-fast-step:before {
	content: "\e081";
}
.icon-music:before {
	content: "\e082";
}
.icon-multi-borders:before {
	content: "\e083";
}
.icon-minus:before {
	content: "\e084";
}
.icon-marker:before {
	content: "\e085";
}
.icon-marker-points:before {
	content: "\e086";
}
.icon-marker-minus:before {
	content: "\e087";
}
.icon-marker-add:before {
	content: "\e088";
}
.icon-map:before {
	content: "\e089";
}
.icon-male-symbol:before {
	content: "\e08a";
}
.icon-mailbox:before {
	content: "\e08b";
}
.icon-mail:before {
	content: "\e08c";
}
.icon-magnet:before {
	content: "\e08d";
}
.icon-magic-wand:before {
	content: "\e08e";
}
.icon-login-lock-refresh:before {
	content: "\e08f";
}
.icon-locked:before {
	content: "\e090";
}
.icon-location:before {
	content: "\e091";
}
.icon-location-maps:before {
	content: "\e092";
}
.icon-list-square:before {
	content: "\e093";
}
.icon-list-circle:before {
	content: "\e094";
}
.icon-link-url:before {
	content: "\e095";
}
.icon-line-through:before {
	content: "\e096";
}
.icon-limit-directions:before {
	content: "\e097";
}
.icon-like-upload:before {
	content: "\e098";
}
.icon-like-remove:before {
	content: "\e099";
}
.icon-like-download:before {
	content: "\e09a";
}
.icon-like-close:before {
	content: "\e09b";
}
.icon-like-ban:before {
	content: "\e09c";
}
.icon-like-add:before {
	content: "\e09d";
}
.icon-left-right:before {
	content: "\e09e";
}
.icon-leaf:before {
	content: "\e09f";
}
.icon-layers:before {
	content: "\e0a0";
}
.icon-landscape:before {
	content: "\e0a1";
}
.icon-key:before {
	content: "\e0a2";
}
.icon-italic:before {
	content: "\e0a3";
}
.icon-info:before {
	content: "\e0a4";
}
.icon-idea:before {
	content: "\e0a5";
}
.icon-home-wifi:before {
	content: "\e0a6";
}
.icon-heart:before {
	content: "\e0a7";
}
.icon-hdd:before {
	content: "\e0a8";
}
.icon-hdd-raid:before {
	content: "\e0a9";
}
.icon-hdd-net:before {
	content: "\e0aa";
}
.icon-grids:before {
	content: "\e0ab";
}
.icon-grid-big:before {
	content: "\e0ac";
}
.icon-graphs:before {
	content: "\e0ad";
}
.icon-forward:before {
	content: "\e0ae";
}
.icon-fire:before {
	content: "\e0af";
}
.icon-female-symbol:before {
	content: "\e0b0";
}
.icon-eye:before {
	content: "\e0b1";
}
.icon-eye-disabled:before {
	content: "\e0b2";
}
.icon-expand:before {
	content: "\e0b3";
}
.icon-expand-vertical:before {
	content: "\e0b4";
}
.icon-expand-horizontal:before {
	content: "\e0b5";
}
.icon-expand-directions:before {
	content: "\e0b6";
}
.icon-exclamation:before {
	content: "\e0b7";
}
.icon-euro:before {
	content: "\e0b8";
}
.icon-email-upload:before {
	content: "\e0b9";
}
.icon-email-spam:before {
	content: "\e0ba";
}
.icon-email-remove:before {
	content: "\e0bb";
}
.icon-email-luminosity:before {
	content: "\e0bc";
}
.icon-email-download:before {
	content: "\e0bd";
}
.icon-email-close:before {
	content: "\e0be";
}
.icon-email-add:before {
	content: "\e0bf";
}
.icon-eject:before {
	content: "\e0c0";
}
.icon-drops:before {
	content: "\e0c1";
}
.icon-drop:before {
	content: "\e0c2";
}
.icon-download:before {
	content: "\e0c3";
}
.icon-download-selection:before {
	content: "\e0c4";
}
.icon-download-selection-circle:before {
	content: "\e0c5";
}
.icon-double-diamonds:before {
	content: "\e0c6";
}
.icon-dot-square:before {
	content: "\e0c7";
}
.icon-dot-line:before {
	content: "\e0c8";
}
.icon-dot-circle:before {
	content: "\e0c9";
}
.icon-dollar:before {
	content: "\e0ca";
}
.icon-documents:before {
	content: "\e0cb";
}
.icon-document:before {
	content: "\e0cc";
}
.icon-document-fill:before {
	content: "\e0cd";
}
.icon-directions:before {
	content: "\e0ce";
}
.icon-cross:before {
	content: "\e0cf";
}
.icon-credit-card:before {
	content: "\e0d0";
}
.icon-copy-paste-document:before {
	content: "\e0d1";
}
.icon-copy-document:before {
	content: "\e0d2";
}
.icon-contract-vertical:before {
	content: "\e0d3";
}
.icon-contract-horizontal:before {
	content: "\e0d4";
}
.icon-contract-directions:before {
	content: "\e0d5";
}
.icon-compass:before {
	content: "\e0d6";
}
.icon-compass-2:before {
	content: "\e0d7";
}
.icon-comments:before {
	content: "\e0d8";
}
.icon-comment:before {
	content: "\e0d9";
}
.icon-coins:before {
	content: "\e0da";
}
.icon-cloud:before {
	content: "\e0db";
}
.icon-cloud-upload:before {
	content: "\e0dc";
}
.icon-cloud-remove:before {
	content: "\e0dd";
}
.icon-cloud-download:before {
	content: "\e0de";
}
.icon-cloud-add:before {
	content: "\e0df";
}
.icon-clock:before {
	content: "\e0e0";
}
.icon-circles:before {
	content: "\e0e1";
}
.icon-check:before {
	content: "\e0e2";
}
.icon-chat:before {
	content: "\e0e3";
}
.icon-chart-down:before {
	content: "\e0e4";
}
.icon-cd-dvd-rom:before {
	content: "\e0e5";
}
.icon-camera:before {
	content: "\e0e6";
}
.icon-button-question:before {
	content: "\e0e7";
}
.icon-button-minus:before {
	content: "\e0e8";
}
.icon-button-exclamation:before {
	content: "\e0e9";
}
.icon-button-email:before {
	content: "\e0ea";
}
.icon-button-close:before {
	content: "\e0eb";
}
.icon-button-check:before {
	content: "\e0ec";
}
.icon-button-add:before {
	content: "\e0ed";
}
.icon-brush:before {
	content: "\e0ee";
}
.icon-browser-sizes:before {
	content: "\e0ef";
}
.icon-box-remove:before {
	content: "\e0f0";
}
.icon-box-close:before {
	content: "\e0f1";
}
.icon-box-blank:before {
	content: "\e0f2";
}
.icon-box-add:before {
	content: "\e0f3";
}
.icon-bolt:before {
	content: "\e0f4";
}
.icon-block-menu:before {
	content: "\e0f5";
}
.icon-blank:before {
	content: "\e0f6";
}
.icon-bezier:before {
	content: "\e0f7";
}
.icon-bars:before {
	content: "\e0f8";
}
.icon-ban-circle:before {
	content: "\e0f9";
}
.icon-bag:before {
	content: "\e0fa";
}
.icon-backward:before {
	content: "\e0fb";
}
.icon-axis-rules:before {
	content: "\e0fc";
}
.icon-atom:before {
	content: "\e0fd";
}
.icon-arrow-up:before {
	content: "\e0fe";
}
.icon-arrow-up-thin:before {
	content: "\e0ff";
}
.icon-arrow-up-light:before {
	content: "\e100";
}
.icon-arrow-up-bold:before {
	content: "\e101";
}
.icon-arrow-up-bold-round:before {
	content: "\e102";
}
.icon-arrow-up-big:before {
	content: "\e103";
}
.icon-arrow-right:before {
	content: "\e104";
}
.icon-arrow-right-thin:before {
	content: "\e105";
}
.icon-arrow-right-light:before {
	content: "\e106";
}
.icon-arrow-right-bold:before {
	content: "\e107";
}
.icon-arrow-right-bold-round:before {
	content: "\e108";
}
.icon-arrow-right-big:before {
	content: "\e109";
}
.icon-arrow-oblique-expand:before {
	content: "\e10a";
}
.icon-arrow-oblique-expand-directions:before {
	content: "\e10b";
}
.icon-arrow-oblique-contract:before {
	content: "\e10c";
}
.icon-arrow-oblique-contract-directions:before {
	content: "\e10d";
}
.icon-arrow-multi-line-up:before {
	content: "\e10e";
}
.icon-arrow-multi-line-right:before {
	content: "\e10f";
}
.icon-arrow-multi-line-left:before {
	content: "\e110";
}
.icon-arrow-multi-line-down:before {
	content: "\e111";
}
.icon-arrow-left:before {
	content: "\e112";
}
.icon-arrow-left-thin:before {
	content: "\e113";
}
.icon-arrow-left-light:before {
	content: "\e114";
}
.icon-arrow-left-bold:before {
	content: "\e115";
}
.icon-arrow-left-bold-round:before {
	content: "\e116";
}
.icon-arrow-left-big:before {
	content: "\e117";
}
.icon-arrow-fill-up:before {
	content: "\e118";
}
.icon-arrow-fill-right:before {
	content: "\e119";
}
.icon-arrow-fill-left:before {
	content: "\e11a";
}
.icon-arrow-fill-down:before {
	content: "\e11b";
}
.icon-arrow-down:before {
	content: "\e11c";
}
.icon-arrow-down-thin:before {
	content: "\e11d";
}
.icon-arrow-down-light:before {
	content: "\e11e";
}
.icon-arrow-down-bold:before {
	content: "\e11f";
}
.icon-arrow-down-bold-round:before {
	content: "\e120";
}
.icon-arrow-down-big:before {
	content: "\e121";
}
.icon-arrow-cycling:before {
	content: "\e122";
}
.icon-arrow-cycle:before {
	content: "\e123";
}
.icon-arrow-curve-right:before {
	content: "\e124";
}
.icon-arrow-curve-recycle:before {
	content: "\e125";
}
.icon-arrow-curve-left:before {
	content: "\e126";
}
.icon-animal-footprint:before {
	content: "\e127";
}
.icon-alarm-clock:before {
	content: "\e128";
}
.icon-air-plane:before {
	content: "\e129";
}
.icon-adjust:before {
	content: "\e12a";
}
.icon-cube:before {
	content: "\e12b";
}
