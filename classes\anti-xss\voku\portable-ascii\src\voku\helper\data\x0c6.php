<?php

return [
    'yeoss',    // 0x00
    'yeong',    // 0x01
    'yeoj',    // 0x02
    'yeoc',    // 0x03
    'yeok',    // 0x04
    'yeot',    // 0x05
    'yeop',    // 0x06
    'yeoh',    // 0x07
    'ye',    // 0x08
    'yeg',    // 0x09
    'yegg',    // 0x0a
    'yegs',    // 0x0b
    'yen',    // 0x0c
    'yenj',    // 0x0d
    'yenh',    // 0x0e
    'yed',    // 0x0f
    'yel',    // 0x10
    'yelg',    // 0x11
    'yelm',    // 0x12
    'yelb',    // 0x13
    'yels',    // 0x14
    'yelt',    // 0x15
    'yelp',    // 0x16
    'yelh',    // 0x17
    'yem',    // 0x18
    'yeb',    // 0x19
    'yebs',    // 0x1a
    'yes',    // 0x1b
    'yess',    // 0x1c
    'yeng',    // 0x1d
    'yej',    // 0x1e
    'yec',    // 0x1f
    'yek',    // 0x20
    'yet',    // 0x21
    'yep',    // 0x22
    'yeh',    // 0x23
    'o',    // 0x24
    'og',    // 0x25
    'ogg',    // 0x26
    'ogs',    // 0x27
    'on',    // 0x28
    'onj',    // 0x29
    'onh',    // 0x2a
    'od',    // 0x2b
    'ol',    // 0x2c
    'olg',    // 0x2d
    'olm',    // 0x2e
    'olb',    // 0x2f
    'ols',    // 0x30
    'olt',    // 0x31
    'olp',    // 0x32
    'olh',    // 0x33
    'om',    // 0x34
    'ob',    // 0x35
    'obs',    // 0x36
    'os',    // 0x37
    'oss',    // 0x38
    'ong',    // 0x39
    'oj',    // 0x3a
    'oc',    // 0x3b
    'ok',    // 0x3c
    'ot',    // 0x3d
    'op',    // 0x3e
    'oh',    // 0x3f
    'wa',    // 0x40
    'wag',    // 0x41
    'wagg',    // 0x42
    'wags',    // 0x43
    'wan',    // 0x44
    'wanj',    // 0x45
    'wanh',    // 0x46
    'wad',    // 0x47
    'wal',    // 0x48
    'walg',    // 0x49
    'walm',    // 0x4a
    'walb',    // 0x4b
    'wals',    // 0x4c
    'walt',    // 0x4d
    'walp',    // 0x4e
    'walh',    // 0x4f
    'wam',    // 0x50
    'wab',    // 0x51
    'wabs',    // 0x52
    'was',    // 0x53
    'wass',    // 0x54
    'wang',    // 0x55
    'waj',    // 0x56
    'wac',    // 0x57
    'wak',    // 0x58
    'wat',    // 0x59
    'wap',    // 0x5a
    'wah',    // 0x5b
    'wae',    // 0x5c
    'waeg',    // 0x5d
    'waegg',    // 0x5e
    'waegs',    // 0x5f
    'waen',    // 0x60
    'waenj',    // 0x61
    'waenh',    // 0x62
    'waed',    // 0x63
    'wael',    // 0x64
    'waelg',    // 0x65
    'waelm',    // 0x66
    'waelb',    // 0x67
    'waels',    // 0x68
    'waelt',    // 0x69
    'waelp',    // 0x6a
    'waelh',    // 0x6b
    'waem',    // 0x6c
    'waeb',    // 0x6d
    'waebs',    // 0x6e
    'waes',    // 0x6f
    'waess',    // 0x70
    'waeng',    // 0x71
    'waej',    // 0x72
    'waec',    // 0x73
    'waek',    // 0x74
    'waet',    // 0x75
    'waep',    // 0x76
    'waeh',    // 0x77
    'oe',    // 0x78
    'oeg',    // 0x79
    'oegg',    // 0x7a
    'oegs',    // 0x7b
    'oen',    // 0x7c
    'oenj',    // 0x7d
    'oenh',    // 0x7e
    'oed',    // 0x7f
    'oel',    // 0x80
    'oelg',    // 0x81
    'oelm',    // 0x82
    'oelb',    // 0x83
    'oels',    // 0x84
    'oelt',    // 0x85
    'oelp',    // 0x86
    'oelh',    // 0x87
    'oem',    // 0x88
    'oeb',    // 0x89
    'oebs',    // 0x8a
    'oes',    // 0x8b
    'oess',    // 0x8c
    'oeng',    // 0x8d
    'oej',    // 0x8e
    'oec',    // 0x8f
    'oek',    // 0x90
    'oet',    // 0x91
    'oep',    // 0x92
    'oeh',    // 0x93
    'yo',    // 0x94
    'yog',    // 0x95
    'yogg',    // 0x96
    'yogs',    // 0x97
    'yon',    // 0x98
    'yonj',    // 0x99
    'yonh',    // 0x9a
    'yod',    // 0x9b
    'yol',    // 0x9c
    'yolg',    // 0x9d
    'yolm',    // 0x9e
    'yolb',    // 0x9f
    'yols',    // 0xa0
    'yolt',    // 0xa1
    'yolp',    // 0xa2
    'yolh',    // 0xa3
    'yom',    // 0xa4
    'yob',    // 0xa5
    'yobs',    // 0xa6
    'yos',    // 0xa7
    'yoss',    // 0xa8
    'yong',    // 0xa9
    'yoj',    // 0xaa
    'yoc',    // 0xab
    'yok',    // 0xac
    'yot',    // 0xad
    'yop',    // 0xae
    'yoh',    // 0xaf
    'u',    // 0xb0
    'ug',    // 0xb1
    'ugg',    // 0xb2
    'ugs',    // 0xb3
    'un',    // 0xb4
    'unj',    // 0xb5
    'unh',    // 0xb6
    'ud',    // 0xb7
    'ul',    // 0xb8
    'ulg',    // 0xb9
    'ulm',    // 0xba
    'ulb',    // 0xbb
    'uls',    // 0xbc
    'ult',    // 0xbd
    'ulp',    // 0xbe
    'ulh',    // 0xbf
    'um',    // 0xc0
    'ub',    // 0xc1
    'ubs',    // 0xc2
    'us',    // 0xc3
    'uss',    // 0xc4
    'ung',    // 0xc5
    'uj',    // 0xc6
    'uc',    // 0xc7
    'uk',    // 0xc8
    'ut',    // 0xc9
    'up',    // 0xca
    'uh',    // 0xcb
    'weo',    // 0xcc
    'weog',    // 0xcd
    'weogg',    // 0xce
    'weogs',    // 0xcf
    'weon',    // 0xd0
    'weonj',    // 0xd1
    'weonh',    // 0xd2
    'weod',    // 0xd3
    'weol',    // 0xd4
    'weolg',    // 0xd5
    'weolm',    // 0xd6
    'weolb',    // 0xd7
    'weols',    // 0xd8
    'weolt',    // 0xd9
    'weolp',    // 0xda
    'weolh',    // 0xdb
    'weom',    // 0xdc
    'weob',    // 0xdd
    'weobs',    // 0xde
    'weos',    // 0xdf
    'weoss',    // 0xe0
    'weong',    // 0xe1
    'weoj',    // 0xe2
    'weoc',    // 0xe3
    'weok',    // 0xe4
    'weot',    // 0xe5
    'weop',    // 0xe6
    'weoh',    // 0xe7
    'we',    // 0xe8
    'weg',    // 0xe9
    'wegg',    // 0xea
    'wegs',    // 0xeb
    'wen',    // 0xec
    'wenj',    // 0xed
    'wenh',    // 0xee
    'wed',    // 0xef
    'wel',    // 0xf0
    'welg',    // 0xf1
    'welm',    // 0xf2
    'welb',    // 0xf3
    'wels',    // 0xf4
    'welt',    // 0xf5
    'welp',    // 0xf6
    'welh',    // 0xf7
    'wem',    // 0xf8
    'web',    // 0xf9
    'webs',    // 0xfa
    'wes',    // 0xfb
    'wess',    // 0xfc
    'weng',    // 0xfd
    'wej',    // 0xfe
    'wec',    // 0xff
];
