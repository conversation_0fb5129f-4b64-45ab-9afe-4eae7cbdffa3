<?php

return [
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    ' ',
    '!',
    '"',
    '#',
    '$',
    '%',
    '&',
    '\'',
    '(',
    ')',
    '*',
    '+',
    ',',
    '-',
    '.',
    '/',
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    ':',
    ';',
    '<',
    '=',
    '>',
    '?',
    '@',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
    '[',
    '\\',
    ']',
    '^',
    '_',
    '`',
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z',
    '{',
    '|',
    '}',
    '~',
    '',
    'EUR',  // "\xc2\x80" => "\xe2\x82\xac" => EURO SIGN
    '',
    ',',    // "\xc2\x82" => "\xe2\x80\x9a" =>  SINGLE LOW-9 QUOTATION MARK
    'f',    // "\xc2\x83" => "\xc6\x92" => LATIN SMALL LETTER F WITH HOOK
    ',,',   // "\xc2\x84" => "\xe2\x80\x9e" => DOUBLE LOW-9 QUOTATION MARK
    '...',  // "\xc2\x85" => "\xe2\x80\xa6" =>  HORIZONTAL ELLIPSIS
    '+',    // "\xc2\x86" => "\xe2\x80\xa0" => DAGGER
    '++',   // "\xc2\x87" => "\xe2\x80\xa1" => DOUBLE DAGGER
    '^',    // "\xc2\x88" => "\xcb\x86" => MODIFIER LETTER CIRCUMFLEX ACCENT
    '%0',   // "\xc2\x89" => "\xe2\x80\xb0" => PER MILLE SIGN
    'S',    // "\xc2\x8a" => "\xc5\xa0" => LATIN CAPITAL LETTER S WITH CARON
    '<',    // "\xc2\x8b" => "\xe2\x80\xb9" => SINGLE LEFT-POINTING ANGLE QUOTE
    'OE',   // "\xc2\x8c" => "\xc5\x92" => LATIN CAPITAL LIGATURE OE
    '',
    'Z',    // "\xc2\x8e" => "\xc5\xbd" => LATIN CAPITAL LETTER Z WITH CARON
    '',
    '',
    '\'',   // "\xc2\x91" => "\xe2\x80\x98" => LEFT SINGLE QUOTATION MARK
    '\'',   // "\xc2\x92" => "\xe2\x80\x99" => RIGHT SINGLE QUOTATION MARK
    '"',    // "\xc2\x93" => "\xe2\x80\x9c" => LEFT DOUBLE QUOTATION MARK
    '"',    // "\xc2\x94" => "\xe2\x80\x9d" => RIGHT DOUBLE QUOTATION MARK
    '*',    // "\xc2\x95" => "\xe2\x80\xa2" => BULLET
    '-',    // "\xc2\x96" => "\xe2\x80\x93" => EN DASH
    '--',   // "\xc2\x97" => "\xe2\x80\x94" => EM DASH
    '~',    // "\xc2\x98" => "\xcb\x9c" => SMALL TILDE
    'tm',
    's',
    '>',
    'oe',
    '',
    'z',
    'Y',
    ' ',
    '!',
    'C/',
    'PS',
    '$?',
    'Y=',
    '|',
    'SS',
    '"',
    '(c)',
    'a',
    '<<',
    '!',
    '',
    '(r)',
    '-',
    'deg',
    '+-',
    '2',
    '3',
    '\'',
    'u',
    'P',
    '*',
    ',',
    '1',
    'o',
    '>>',
    '1/4',
    '1/2',
    '3/4',
    '?',    // 0xbf
    'A',    // 0xc0
    'A',    // 0xc1
    'A',    // 0xc2
    'A',    // 0xc3

    // Not "AE" - used in languages other than German
    'A',    // 0xc4

    'A',    // 0xc5
    'AE',    // 0xc6
    'C',    // 0xc7
    'E',    // 0xc8
    'E',    // 0xc9
    'E',    // 0xca
    'E',    // 0xcb
    'I',    // 0xcc
    'I',    // 0xcd
    'I',    // 0xce
    'I',    // 0xcf
    'D',    // 0xd0
    'N',    // 0xd1
    'O',    // 0xd2
    'O',    // 0xd3
    'O',    // 0xd4
    'O',    // 0xd5

    // Not "OE" - used in languages other than German
    'O',    // 0xd6

    'x',    // 0xd7
    'O',    // 0xd8
    'U',    // 0xd9
    'U',    // 0xda
    'U',    // 0xdb

    // Not "UE" - used in languages other than German
    'U',    // 0xdc

    'Y',    // 0xdd
    'Th',    // 0xde
    'ss',    // 0xdf
    'a',    // 0xe0
    'a',    // 0xe1
    'a',    // 0xe2
    'a',    // 0xe3

    // Not "ae" - used in languages other than German
    'a',    // 0xe4

    'a',    // 0xe5
    'ae',    // 0xe6
    'c',    // 0xe7
    'e',    // 0xe8
    'e',    // 0xe9
    'e',    // 0xea
    'e',    // 0xeb
    'i',    // 0xec
    'i',    // 0xed
    'i',    // 0xee
    'i',    // 0xef
    'd',    // 0xf0
    'n',    // 0xf1
    'o',    // 0xf2
    'o',    // 0xf3
    'o',    // 0xf4
    'o',    // 0xf5

    // Not "oe" - used in languages other than German
    'o',    // 0xf6

    '/',    // 0xf7
    'o',    // 0xf8
    'u',    // 0xf9
    'u',    // 0xfa
    'u',    // 0xfb

    // Not "ue" - used in languages other than German
    'u',    // 0xfc

    'y',    // 0xfd
    'th',    // 0xfe
    'y',    // 0xff
];
