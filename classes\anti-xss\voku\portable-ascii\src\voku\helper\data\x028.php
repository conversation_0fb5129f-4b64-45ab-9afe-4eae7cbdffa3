<?php

return [
    ' ',    // 0x00
    'a',    // 0x01
    '1',    // 0x02
    'b',    // 0x03
    '\'',    // 0x04
    'k',    // 0x05
    '2',    // 0x06
    'l',    // 0x07
    '@',    // 0x08
    'c',    // 0x09
    'i',    // 0x0a
    'f',    // 0x0b
    '/',    // 0x0c
    'm',    // 0x0d
    's',    // 0x0e
    'p',    // 0x0f
    '"',    // 0x10
    'e',    // 0x11
    '3',    // 0x12
    'h',    // 0x13
    '9',    // 0x14
    'o',    // 0x15
    '6',    // 0x16
    'r',    // 0x17
    '^',    // 0x18
    'd',    // 0x19
    'j',    // 0x1a
    'g',    // 0x1b
    '>',    // 0x1c
    'n',    // 0x1d
    't',    // 0x1e
    'q',    // 0x1f
    ',',    // 0x20
    '*',    // 0x21
    '5',    // 0x22
    '<',    // 0x23
    '-',    // 0x24
    'u',    // 0x25
    '8',    // 0x26
    'v',    // 0x27
    '.',    // 0x28
    '%',    // 0x29
    '[',    // 0x2a
    '$',    // 0x2b
    '+',    // 0x2c
    'x',    // 0x2d
    '!',    // 0x2e
    '&',    // 0x2f
    ';',    // 0x30
    ':',    // 0x31
    '4',    // 0x32
    '\\',    // 0x33
    '0',    // 0x34
    'z',    // 0x35
    '7',    // 0x36
    '(',    // 0x37
    '_',    // 0x38
    '?',    // 0x39
    'w',    // 0x3a
    ']',    // 0x3b
    '#',    // 0x3c
    'y',    // 0x3d
    ')',    // 0x3e
    '=',    // 0x3f
    '[d7]',    // 0x40
    '[d17]',    // 0x41
    '[d27]',    // 0x42
    '[d127]',    // 0x43
    '[d37]',    // 0x44
    '[d137]',    // 0x45
    '[d237]',    // 0x46
    '[d1237]',    // 0x47
    '[d47]',    // 0x48
    '[d147]',    // 0x49
    '[d247]',    // 0x4a
    '[d1247]',    // 0x4b
    '[d347]',    // 0x4c
    '[d1347]',    // 0x4d
    '[d2347]',    // 0x4e
    '[d12347]',    // 0x4f
    '[d57]',    // 0x50
    '[d157]',    // 0x51
    '[d257]',    // 0x52
    '[d1257]',    // 0x53
    '[d357]',    // 0x54
    '[d1357]',    // 0x55
    '[d2357]',    // 0x56
    '[d12357]',    // 0x57
    '[d457]',    // 0x58
    '[d1457]',    // 0x59
    '[d2457]',    // 0x5a
    '[d12457]',    // 0x5b
    '[d3457]',    // 0x5c
    '[d13457]',    // 0x5d
    '[d23457]',    // 0x5e
    '[d123457]',    // 0x5f
    '[d67]',    // 0x60
    '[d167]',    // 0x61
    '[d267]',    // 0x62
    '[d1267]',    // 0x63
    '[d367]',    // 0x64
    '[d1367]',    // 0x65
    '[d2367]',    // 0x66
    '[d12367]',    // 0x67
    '[d467]',    // 0x68
    '[d1467]',    // 0x69
    '[d2467]',    // 0x6a
    '[d12467]',    // 0x6b
    '[d3467]',    // 0x6c
    '[d13467]',    // 0x6d
    '[d23467]',    // 0x6e
    '[d123467]',    // 0x6f
    '[d567]',    // 0x70
    '[d1567]',    // 0x71
    '[d2567]',    // 0x72
    '[d12567]',    // 0x73
    '[d3567]',    // 0x74
    '[d13567]',    // 0x75
    '[d23567]',    // 0x76
    '[d123567]',    // 0x77
    '[d4567]',    // 0x78
    '[d14567]',    // 0x79
    '[d24567]',    // 0x7a
    '[d124567]',    // 0x7b
    '[d34567]',    // 0x7c
    '[d134567]',    // 0x7d
    '[d234567]',    // 0x7e
    '[d1234567]',    // 0x7f
    '[d8]',    // 0x80
    '[d18]',    // 0x81
    '[d28]',    // 0x82
    '[d128]',    // 0x83
    '[d38]',    // 0x84
    '[d138]',    // 0x85
    '[d238]',    // 0x86
    '[d1238]',    // 0x87
    '[d48]',    // 0x88
    '[d148]',    // 0x89
    '[d248]',    // 0x8a
    '[d1248]',    // 0x8b
    '[d348]',    // 0x8c
    '[d1348]',    // 0x8d
    '[d2348]',    // 0x8e
    '[d12348]',    // 0x8f
    '[d58]',    // 0x90
    '[d158]',    // 0x91
    '[d258]',    // 0x92
    '[d1258]',    // 0x93
    '[d358]',    // 0x94
    '[d1358]',    // 0x95
    '[d2358]',    // 0x96
    '[d12358]',    // 0x97
    '[d458]',    // 0x98
    '[d1458]',    // 0x99
    '[d2458]',    // 0x9a
    '[d12458]',    // 0x9b
    '[d3458]',    // 0x9c
    '[d13458]',    // 0x9d
    '[d23458]',    // 0x9e
    '[d123458]',    // 0x9f
    '[d68]',    // 0xa0
    '[d168]',    // 0xa1
    '[d268]',    // 0xa2
    '[d1268]',    // 0xa3
    '[d368]',    // 0xa4
    '[d1368]',    // 0xa5
    '[d2368]',    // 0xa6
    '[d12368]',    // 0xa7
    '[d468]',    // 0xa8
    '[d1468]',    // 0xa9
    '[d2468]',    // 0xaa
    '[d12468]',    // 0xab
    '[d3468]',    // 0xac
    '[d13468]',    // 0xad
    '[d23468]',    // 0xae
    '[d123468]',    // 0xaf
    '[d568]',    // 0xb0
    '[d1568]',    // 0xb1
    '[d2568]',    // 0xb2
    '[d12568]',    // 0xb3
    '[d3568]',    // 0xb4
    '[d13568]',    // 0xb5
    '[d23568]',    // 0xb6
    '[d123568]',    // 0xb7
    '[d4568]',    // 0xb8
    '[d14568]',    // 0xb9
    '[d24568]',    // 0xba
    '[d124568]',    // 0xbb
    '[d34568]',    // 0xbc
    '[d134568]',    // 0xbd
    '[d234568]',    // 0xbe
    '[d1234568]',    // 0xbf
    '[d78]',    // 0xc0
    '[d178]',    // 0xc1
    '[d278]',    // 0xc2
    '[d1278]',    // 0xc3
    '[d378]',    // 0xc4
    '[d1378]',    // 0xc5
    '[d2378]',    // 0xc6
    '[d12378]',    // 0xc7
    '[d478]',    // 0xc8
    '[d1478]',    // 0xc9
    '[d2478]',    // 0xca
    '[d12478]',    // 0xcb
    '[d3478]',    // 0xcc
    '[d13478]',    // 0xcd
    '[d23478]',    // 0xce
    '[d123478]',    // 0xcf
    '[d578]',    // 0xd0
    '[d1578]',    // 0xd1
    '[d2578]',    // 0xd2
    '[d12578]',    // 0xd3
    '[d3578]',    // 0xd4
    '[d13578]',    // 0xd5
    '[d23578]',    // 0xd6
    '[d123578]',    // 0xd7
    '[d4578]',    // 0xd8
    '[d14578]',    // 0xd9
    '[d24578]',    // 0xda
    '[d124578]',    // 0xdb
    '[d34578]',    // 0xdc
    '[d134578]',    // 0xdd
    '[d234578]',    // 0xde
    '[d1234578]',    // 0xdf
    '[d678]',    // 0xe0
    '[d1678]',    // 0xe1
    '[d2678]',    // 0xe2
    '[d12678]',    // 0xe3
    '[d3678]',    // 0xe4
    '[d13678]',    // 0xe5
    '[d23678]',    // 0xe6
    '[d123678]',    // 0xe7
    '[d4678]',    // 0xe8
    '[d14678]',    // 0xe9
    '[d24678]',    // 0xea
    '[d124678]',    // 0xeb
    '[d34678]',    // 0xec
    '[d134678]',    // 0xed
    '[d234678]',    // 0xee
    '[d1234678]',    // 0xef
    '[d5678]',    // 0xf0
    '[d15678]',    // 0xf1
    '[d25678]',    // 0xf2
    '[d125678]',    // 0xf3
    '[d35678]',    // 0xf4
    '[d135678]',    // 0xf5
    '[d235678]',    // 0xf6
    '[d1235678]',    // 0xf7
    '[d45678]',    // 0xf8
    '[d145678]',    // 0xf9
    '[d245678]',    // 0xfa
    '[d1245678]',    // 0xfb
    '[d345678]',    // 0xfc
    '[d1345678]',    // 0xfd
    '[d2345678]',    // 0xfe
    '[d12345678]',    // 0xff
];
