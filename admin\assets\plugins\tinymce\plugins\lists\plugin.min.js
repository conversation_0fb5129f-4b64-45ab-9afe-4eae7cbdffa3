/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.1 (2020-05-27)
 */
!function(a){"use strict";var e,n,t,r,o,i,u,f=tinymce.util.Tools.resolve("tinymce.PluginManager"),s=function(){},y=function(e){return function(){return e}},c=function(n){return function(e){return!n(e)}},d=y(!1),l=y(!0),m=function(){return p},p=(e=function(e){return e.isNone()},{fold:function(e,n){return e()},is:d,isSome:d,isNone:l,getOr:t=function(e){return e},getOrThunk:n=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOr<PERSON><PERSON> called on none.")},getOrNull:y(null),getOrUndefined:y(undefined),or:t,orThunk:n,map:m,each:s,bind:m,exists:d,forall:l,filter:m,equals:e,equals_:e,toArray:function(){return[]},toString:y("none()")}),g=function(t){var e=y(t),n=function(){return o},r=function(e){return e(t)},o={fold:function(e,n){return n(t)},is:function(e){return t===e},isSome:l,isNone:d,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:n,orThunk:n,map:function(e){return g(e(t))},each:function(e){e(t)},bind:r,exists:r,forall:r,filter:function(e){return e(t)?o:p},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(e){return e.is(t)},equals_:function(e,n){return e.fold(d,function(e){return n(t,e)})}};return o},v={some:g,none:m,from:function(e){return null===e||e===undefined?p:g(e)}},h=function(r){return function(e){return t=typeof(n=e),(null===n?"null":"object"==t&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":t)===r;var n,t}},S=function(n){return function(e){return typeof e===n}},b=h("string"),C=h("array"),O=S("boolean"),N=S("function"),L=S("number"),T=Array.prototype.slice,w=Array.prototype.push,D=function(e,n){for(var t=e.length,r=new Array(t),o=0;o<t;o++){var i=e[o];r[o]=n(i,o)}return r},k=function(e,n){for(var t=0,r=e.length;t<r;t++){n(e[t],t)}},x=function(e,n){for(var t=[],r=0,o=e.length;r<o;r++){var i=e[r];n(i,r)&&t.push(i)}return t},A=function(e,n,t){return k(e,function(e){t=n(t,e)}),t},E=function(e,n,t){for(var r=0,o=e.length;r<o;r++){var i=e[r];if(n(i,r))return v.some(i);if(t(i,r))break}return v.none()},B=function(e,n){return E(e,n,d)},I=function(e,n){return function(e){for(var n=[],t=0,r=e.length;t<r;++t){if(!C(e[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+e);w.apply(n,e[t])}return n}(D(e,n))},R=function(e){var n=T.call(e,0);return n.reverse(),n},P=function(e){return 0===e.length?v.none():v.some(e[0])},M=function(e){return 0===e.length?v.none():v.some(e[e.length-1])},U=function(){return(U=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e}).apply(this,arguments)},_=function(e,n){var t=function(e,n){for(var t=0;t<e.length;t++){var r=e[t];if(r.test(n))return r}return undefined}(e,n);if(!t)return{major:0,minor:0};var r=function(e){return Number(n.replace(t,"$"+e))};return $(r(1),r(2))},F=function(){return $(0,0)},$=function(e,n){return{major:e,minor:n}},H={nu:$,detect:function(e,n){var t=String(n).toLowerCase();return 0===e.length?F():_(e,t)},unknown:F},j="Edge",q="Chrome",K="Opera",V="Firefox",W="Safari",Q=function(e){var n=e.current,t=e.version,r=function(e){return function(){return n===e}};return{current:n,version:t,isEdge:r(j),isChrome:r(q),isIE:r("IE"),isOpera:r(K),isFirefox:r(V),isSafari:r(W)}},X={unknown:function(){return Q({current:undefined,version:H.unknown()})},nu:Q,edge:y(j),chrome:y(q),ie:y("IE"),opera:y(K),firefox:y(V),safari:y(W)},z="Windows",Y="Android",G="Solaris",J="FreeBSD",Z="ChromeOS",ee=function(e){var n=e.current,t=e.version,r=function(e){return function(){return n===e}};return{current:n,version:t,isWindows:r(z),isiOS:r("iOS"),isAndroid:r(Y),isOSX:r("OSX"),isLinux:r("Linux"),isSolaris:r(G),isFreeBSD:r(J),isChromeOS:r(Z)}},ne={unknown:function(){return ee({current:undefined,version:H.unknown()})},nu:ee,windows:y(z),ios:y("iOS"),android:y(Y),linux:y("Linux"),osx:y("OSX"),solaris:y(G),freebsd:y(J),chromeos:y(Z)},te=function(e,n){var t=String(n).toLowerCase();return B(e,function(e){return e.search(t)})},re=function(e,t){return te(e,t).map(function(e){var n=H.detect(e.versionRegexes,t);return{current:e.name,version:n}})},oe=function(e,t){return te(e,t).map(function(e){var n=H.detect(e.versionRegexes,t);return{current:e.name,version:n}})},ie=function(e,n){return-1!==e.indexOf(n)},ue=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ae=function(n){return function(e){return ie(e,n)}},se=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return ie(e,"edge/")&&ie(e,"chrome")&&ie(e,"safari")&&ie(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,ue],search:function(e){return ie(e,"chrome")&&!ie(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return ie(e,"msie")||ie(e,"trident")}},{name:"Opera",versionRegexes:[ue,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ae("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ae("firefox")},{name:"Safari",versionRegexes:[ue,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(ie(e,"safari")||ie(e,"mobile/"))&&ie(e,"applewebkit")}}],ce=[{name:"Windows",search:ae("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return ie(e,"iphone")||ie(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ae("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:ae("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ae("linux"),versionRegexes:[]},{name:"Solaris",search:ae("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ae("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:ae("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],fe={browsers:y(se),oses:y(ce)},de=function(e,n){var t,r,o,i,u,a,s,c,f,d,l,m,p=fe.browsers(),g=fe.oses(),v=re(p,e).fold(X.unknown,X.nu),h=oe(g,e).fold(ne.unknown,ne.nu);return{browser:v,os:h,deviceType:(r=v,o=e,i=n,u=(t=h).isiOS()&&!0===/ipad/i.test(o),a=t.isiOS()&&!u,s=t.isiOS()||t.isAndroid(),c=s||i("(pointer:coarse)"),f=u||!a&&s&&i("(min-device-width:768px)"),d=a||s&&!f,l=r.isSafari()&&t.isiOS()&&!1===/safari/i.test(o),m=!d&&!f&&!l,{isiPad:y(u),isiPhone:y(a),isTablet:y(f),isPhone:y(d),isTouch:y(c),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:y(l),isDesktop:y(m)})}},le=function(e){return a.window.matchMedia(e).matches},me=(i=!(r=function(){return de(a.navigator.userAgent,le)}),function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return i||(i=!0,o=r.apply(null,e)),o}),pe=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:y(e)}},ge={fromHtml:function(e,n){var t=(n||a.document).createElement("div");if(t.innerHTML=e,!t.hasChildNodes()||1<t.childNodes.length)throw a.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return pe(t.childNodes[0])},fromTag:function(e,n){var t=(n||a.document).createElement(e);return pe(t)},fromText:function(e,n){var t=(n||a.document).createTextNode(e);return pe(t)},fromDom:pe,fromPoint:function(e,n,t){var r=e.dom();return v.from(r.elementFromPoint(n,t)).map(pe)}},ve=function(e,n){return e.dom()===n.dom()},he=function(e,n){return t=e.dom(),r=n.dom(),o=t,i=r,u=a.Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(o.compareDocumentPosition(i)&u);var t,r,o,i,u},ye=function(e,n){return me().browser.isIE()?he(e,n):(t=n,r=e.dom(),o=t.dom(),r!==o&&r.contains(o));var t,r,o},Se=function(e,n){var t=e.dom();if(1!==t.nodeType)return!1;var r=t;if(r.matches!==undefined)return r.matches(n);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(n);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(n);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")},be=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),Ce=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),Oe=tinymce.util.Tools.resolve("tinymce.util.VK"),Ne=function(e,n,t){return e.isSome()&&n.isSome()?v.some(t(e.getOrDie(),n.getOrDie())):v.none()},Le=function(e){return v.from(e.dom().parentNode).map(ge.fromDom)},Te=function(e){return D(e.dom().childNodes,ge.fromDom)},we=function(e,n){var t=e.dom().childNodes;return v.from(t[n]).map(ge.fromDom)},De=function(e){return we(e,0)},ke=function(e){return we(e,e.dom().childNodes.length-1)},xe=function(n,t){Le(n).each(function(e){e.dom().insertBefore(t.dom(),n.dom())})},Ae=function(e,n){e.dom().appendChild(n.dom())},Ee=function(n,e){k(e,function(e){Ae(n,e)})},Be=function(e){var n=e.dom();null!==n.parentNode&&n.parentNode.removeChild(n)},Ie=function(e,n,t){return e.fire("ListMutation",{action:n,element:t})},Re=("undefined"!=typeof a.window?a.window:Function("return this;")(),tinymce.util.Tools.resolve("tinymce.dom.DomQuery")),Pe=tinymce.util.Tools.resolve("tinymce.util.Tools"),Me=function(n){return function(e){return e&&e.nodeName.toLowerCase()===n}},Ue=function(n){return function(e){return e&&n.test(e.nodeName)}},_e=function(e){return e&&3===e.nodeType},Fe=Ue(/^(OL|UL|DL)$/),$e=Ue(/^(OL|UL)$/),He=Me("ol"),je=Ue(/^(LI|DT|DD)$/),qe=Ue(/^(DT|DD)$/),Ke=Ue(/^(TH|TD)$/),Ve=Me("br"),We=function(e,n){return n&&!!e.schema.getTextBlockElements()[n.nodeName]},Qe=function(e,n){return e&&e.nodeName in n},Xe=function(e,n,t){var r=e.isEmpty(n);return!(t&&0<e.select("span[data-mce-type=bookmark]",n).length)&&r},ze=function(e,n){return e.isChildOf(n,e.getRoot())},Ye=function(e,n){var t=n||e.selection.getStart(!0);return e.dom.getParent(t,"OL,UL,DL",Ze(e,t))},Ge=function(e){var n,t,r,o=Ye(e),i=e.selection.getSelectedBlocks();return r=i,(t=o)&&1===r.length&&r[0]===t?(n=o,Pe.grep(n.querySelectorAll("ol,ul,dl"),function(e){return Fe(e)})):Pe.grep(i,function(e){return Fe(e)&&o!==e})},Je=function(e){var t,n,r,o=e.selection.getSelectedBlocks();return Pe.grep((t=e,n=o,r=Pe.map(n,function(e){var n=t.dom.getParent(e,"li,dd,dt",Ze(t,e));return n||e}),Re.unique(r)),function(e){return je(e)})},Ze=function(e,n){var t=e.dom.getParents(n,"TD,TH");return 0<t.length?t[0]:e.getBody()},en=function(e,n){var t=e.dom.getParents(n,"ol,ul",Ze(e,n));return M(t)},nn=function(e){var n,t,r,o=(t=en(n=e,n.selection.getStart()),r=x(n.selection.getSelectedBlocks(),$e),t.toArray().concat(r));return tn(e,o)},tn=function(n,e){var t=D(e,function(e){return en(n,e).getOr(e)});return Re.unique(t)},rn=function(e,n){var t,r,o,i,u=e.dom,a=e.schema.getBlockElements(),s=u.createFragment(),c=!1===(t=e.getParam("forced_root_block","p"))?"":!0===t?"p":t;if(c&&((o=u.create(c)).tagName===c.toUpperCase()&&u.setAttribs(o,e.getParam("forced_root_block_attrs",{})),Qe(n.firstChild,a)||s.appendChild(o)),n)for(;r=n.firstChild;){var f=r.nodeName;i||"SPAN"===f&&"bookmark"===r.getAttribute("data-mce-type")||(i=!0),Qe(r,a)?(s.appendChild(r),o=null):c?(o||(o=u.create(c),s.appendChild(o)),o.appendChild(r)):s.appendChild(r)}return c?i||o.appendChild(u.create("br",{"data-mce-bogus":"1"})):s.appendChild(u.create("br")),s},on=Object.keys,un=function(e,n){for(var t=on(e),r=0,o=t.length;r<o;r++){var i=t[r];n(e[i],i)}},an=function(e,n){var t,r,o,i,u={};return t=n,i=u,r=function(e,n){i[n]=e},o=s,un(e,function(e,n){(t(e,n)?r:o)(e,n)}),u},sn=function(e){return e.dom().nodeName.toLowerCase()},cn=(u=1,function(e){return e.dom().nodeType===u}),fn=function(e,n){var t=e.dom();un(n,function(e,n){!function(e,n,t){if(!(b(t)||O(t)||L(t)))throw a.console.error("Invalid call to Attr.set. Key ",n,":: Value ",t,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(n,t+"")}(t,n,e)})},dn=function(e){return A(e.dom().attributes,function(e,n){return e[n.name]=n.value,e},{})},ln=function(e,n,t){if(!b(t))throw a.console.error("Invalid call to CSS.set. Property ",n,":: Value ",t,":: Element ",e),new Error("CSS value must be a string: "+t);var r;(r=e).style!==undefined&&N(r.style.getPropertyValue)&&e.style.setProperty(n,t)},mn=function(e){return n=e,t=!0,ge.fromDom(n.dom().cloneNode(t));var n,t},pn=function(e,n){var t,r,o,i,u=(t=e,r=n,o=ge.fromTag(r),i=dn(t),fn(o,i),o);xe(e,u);var a=Te(e);return Ee(u,a),Be(e),u},gn=function(e,n){Ae(e.item,n.list)},vn=function(f,e,d){var n=e.slice(0,d.depth);return M(n).each(function(e){var n,t,r,o,i,u,a,s,c=(n=f,t=d.itemAttributes,r=d.content,o=ge.fromTag("li",n),fn(o,t),Ee(o,r),o);u=c,Ae((i=e).list,u),i.item=u,s=d,sn((a=e).list)!==s.listType&&(a.list=pn(a.list,s.listType)),fn(a.list,s.listAttributes)}),n},hn=function(e,n,t){var r,o=function(e,n,t){for(var r,o,i,u=[],a=0;a<t;a++)u.push((r=e,o=n.listType,i={list:ge.fromTag(o,r),item:ge.fromTag("li",r)},Ae(i.list,i.item),i));return u}(e,t,t.depth-n.length);return function(e){for(var n=1;n<e.length;n++)gn(e[n-1],e[n])}(o),function(e,n){for(var t=0;t<e.length-1;t++)r=e[t].item,o="list-style-type",i="none",u=r.dom(),ln(u,o,i);var r,o,i,u;M(e).each(function(e){fn(e.list,n.listAttributes),fn(e.item,n.itemAttributes),Ee(e.item,n.content)})}(o,t),r=o,Ne(M(n),P(r),gn),n.concat(o)},yn=function(e){return Se(e,"OL,UL")},Sn=function(e){return De(e).map(yn).getOr(!1)},bn=function(e){return 0<e.depth},Cn=function(e){return e.isSelected},On=function(e){var n=Te(e),t=ke(e).map(yn).getOr(!1)?n.slice(0,-1):n;return D(t,mn)},Nn=function(a){return k(a,function(r,e){var n,t,o,i,u;o=(n=a)[t=e].depth,i=function(e){return e.depth===o&&!e.dirty},u=function(e){return e.depth<o},E(R(n.slice(0,t)),i,u).orThunk(function(){return E(n.slice(t+1),i,u)}).fold(function(){var e;r.dirty&&((e=r).listAttributes=an(e.listAttributes,function(e,n){return"start"!==n}))},function(e){return t=e,(n=r).listType=t.listType,void(n.listAttributes=U({},t.listAttributes));var n,t})}),a},Ln=function(i,u,a,s){return De(s).filter(yn).fold(function(){u.each(function(e){ve(e.start,s)&&a.set(!0)});var n,t,r,e=(n=s,t=i,r=a.get(),Le(n).filter(cn).map(function(e){return{depth:t,dirty:!1,isSelected:r,content:On(n),itemAttributes:dn(n),listAttributes:dn(e),listType:sn(e)}}));u.each(function(e){ve(e.end,s)&&a.set(!1)});var o=ke(s).filter(yn).map(function(e){return Tn(i,u,a,e)}).getOr([]);return e.toArray().concat(o)},function(e){return Tn(i,u,a,e)})},Tn=function(n,t,r,e){return I(Te(e),function(e){return(yn(e)?Tn:Ln)(n+1,t,r,e)})},wn=function(e,n){var t,r=(t=!1,{get:function(){return t},set:function(e){t=e}});return D(e,function(e){return{sourceList:e,entries:Tn(0,n,r,e)}})},Dn=function(i,e){var n=Nn(e);return D(n,function(e){var n,t,r,o=(n=e.content,r=(t||a.document).createDocumentFragment(),k(n,function(e){r.appendChild(e.dom())}),ge.fromDom(r));return ge.fromDom(rn(i,o.dom()))})},kn=function(e,n){var t,r,o=Nn(n);return t=e.contentDocument,r=A(o,function(e,n){return(n.depth>e.length?hn:vn)(t,e,n)},[]),P(r).map(function(e){return e.list}).toArray()},xn=function(a,e,s){var n,t=wn(e,(n=D(Je(a),ge.fromDom),Ne(B(n,c(Sn)),B(R(n),c(Sn)),function(e,n){return{start:e,end:n}})));k(t,function(e){var n,t;n=e.entries,t=s,k(x(n,Cn),function(e){return function(e,n){switch(e){case"Indent":n.depth++;break;case"Outdent":n.depth--;break;case"Flatten":n.depth=0}n.dirty=!0}(t,e)});var r,o,i,u=(r=a,o=e.entries,I(function(e,n){if(0===e.length)return[];for(var t=n(e[0]),r=[],o=[],i=0,u=e.length;i<u;i++){var a=e[i],s=n(a);s!==t&&(r.push(o),o=[]),t=s,o.push(a)}return 0!==o.length&&r.push(o),r}(o,bn),function(e){return(P(e).map(bn).getOr(!1)?kn:Dn)(r,e)}));k(u,function(e){Ie(a,"Indent"===s?"IndentList":"OutdentList",e.dom())}),i=e.sourceList,k(u,function(e){xe(i,e)}),Be(e.sourceList)})},An=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),En=An.DOM,Bn=function(n,t){Se(t,"dd")?pn(t,"dt"):Se(t,"dt")&&Le(t).each(function(e){return function(e,n,t){var r,o,i,u,a,s;for(i=En.select('span[data-mce-type="bookmark"]',n),a=rn(e,t),(r=En.createRng()).setStartAfter(t),r.setEndAfter(n),u=(o=r.extractContents()).firstChild;u;u=u.firstChild)if("LI"===u.nodeName&&e.dom.isEmpty(u)){En.remove(u);break}e.dom.isEmpty(o)||En.insertAfter(o,n),En.insertAfter(a,n),Xe(e.dom,t.parentNode)&&(s=t.parentNode,Pe.each(i,function(e){s.parentNode.insertBefore(e,t.parentNode)}),En.remove(s)),En.remove(t),Xe(e.dom,n)&&En.remove(n)}(n,e.dom(),t.dom())})},In=function(e){Se(e,"dt")&&pn(e,"dd")},Rn=function(e,n){if(_e(e))return{container:e,offset:n};var t=be.getNode(e,n);return _e(t)?{container:t,offset:n>=e.childNodes.length?t.data.length:0}:t.previousSibling&&_e(t.previousSibling)?{container:t.previousSibling,offset:t.previousSibling.data.length}:t.nextSibling&&_e(t.nextSibling)?{container:t.nextSibling,offset:0}:{container:e,offset:n}},Pn=function(e){var n=e.cloneRange(),t=Rn(e.startContainer,e.startOffset);n.setStart(t.container,t.offset);var r=Rn(e.endContainer,e.endOffset);return n.setEnd(r.container,r.offset),n},Mn=function(e,n){var t,r=D(nn(e),ge.fromDom),o=D(x(Je(e),qe),ge.fromDom),i=!1;if(r.length||o.length){var u=e.selection.getBookmark();xn(e,r,n),t=e,k(o,"Indent"===n?In:function(e){return Bn(t,e)}),e.selection.moveToBookmark(u),e.selection.setRng(Pn(e.selection.getRng())),e.nodeChanged(),i=!0}return i},Un=function(e){return Mn(e,"Indent")},_n=function(e){return Mn(e,"Outdent")},Fn=function(e){return Mn(e,"Flatten")},$n=tinymce.util.Tools.resolve("tinymce.dom.BookmarkManager"),Hn=An.DOM,jn=function(o){var i={},e=function(e){var n,t,r;t=o[e?"startContainer":"endContainer"],r=o[e?"startOffset":"endOffset"],1===t.nodeType&&(n=Hn.create("span",{"data-mce-type":"bookmark"}),t.hasChildNodes()?(r=Math.min(r,t.childNodes.length-1),e?t.insertBefore(n,t.childNodes[r]):Hn.insertAfter(n,t.childNodes[r])):t.appendChild(n),t=n,r=0),i[e?"startContainer":"endContainer"]=t,i[e?"startOffset":"endOffset"]=r};return e(!0),o.collapsed||e(),i},qn=function(o){function e(e){var n,t,r;n=r=o[e?"startContainer":"endContainer"],t=o[e?"startOffset":"endOffset"],n&&(1===n.nodeType&&(t=function(e){for(var n=e.parentNode.firstChild,t=0;n;){if(n===e)return t;1===n.nodeType&&"bookmark"===n.getAttribute("data-mce-type")||t++,n=n.nextSibling}return-1}(n),n=n.parentNode,Hn.remove(r),!n.hasChildNodes()&&Hn.isBlock(n)&&n.appendChild(Hn.create("br"))),o[e?"startContainer":"endContainer"]=n,o[e?"startOffset":"endOffset"]=t)}e(!0),e();var n=Hn.createRng();return n.setStart(o.startContainer,o.startOffset),o.endContainer&&n.setEnd(o.endContainer,o.endOffset),Pn(n)},Kn=function(e){return/\btox\-/.test(e.className)},Vn=function(n,t,r){return function(){var e=function(e){var n=E(e.parents,Fe,Ke).filter(function(e){return e.nodeName===t&&!Kn(e)}).isSome();r(n)};return n.on("NodeChange",e),function(){return n.off("NodeChange",e)}}},Wn=function(e){switch(e){case"UL":return"ToggleUlList";case"OL":return"ToggleOlList";case"DL":return"ToggleDLList"}},Qn=function(t,e){Pe.each(e,function(e,n){t.setAttribute(n,e)})},Xn=function(e,n,t){var r,o,i,u,a,s,c;r=e,o=n,u=(i=t)["list-style-type"]?i["list-style-type"]:null,r.setStyle(o,"list-style-type",u),a=e,Qn(s=n,(c=t)["list-attributes"]),Pe.each(a.select("li",s),function(e){Qn(e,c["list-item-attributes"])})},zn=function(e,n,t,r){var o,i;for(o=n[t?"startContainer":"endContainer"],i=n[t?"startOffset":"endOffset"],1===o.nodeType&&(o=o.childNodes[Math.min(i,o.childNodes.length-1)]||o),!t&&Ve(o.nextSibling)&&(o=o.nextSibling);o.parentNode!==r;){if(We(e,o))return o;if(/^(TD|TH)$/.test(o.parentNode.nodeName))return o;o=o.parentNode}return o},Yn=function(f,d,l){void 0===l&&(l={});var e,n=f.selection.getRng(!0),m="LI",t=Ze(f,f.selection.getStart(!0)),p=f.dom;"false"!==p.getContentEditable(f.selection.getNode())&&("DL"===(d=d.toUpperCase())&&(m="DT"),e=jn(n),Pe.each(function(t,e,r){for(var o,i=[],u=t.dom,n=zn(t,e,!0,r),a=zn(t,e,!1,r),s=[],c=n;c&&(s.push(c),c!==a);c=c.nextSibling);return Pe.each(s,function(e){if(We(t,e))return i.push(e),void(o=null);if(u.isBlock(e)||Ve(e))return Ve(e)&&u.remove(e),void(o=null);var n=e.nextSibling;$n.isBookmarkNode(e)&&(We(t,n)||!n&&e.parentNode===r)?o=null:(o||(o=u.create("p"),e.parentNode.insertBefore(o,e),i.push(o)),o.appendChild(e))}),i}(f,n,t),function(e){var n,t,r,o,i,u,a,s,c;(t=e.previousSibling)&&Fe(t)&&t.nodeName===d&&(r=t,o=l,i=p.getStyle(r,"list-style-type"),u=o?o["list-style-type"]:"",i===(u=null===u?"":u))?(n=t,e=p.rename(e,m),t.appendChild(e)):(n=p.create(d),e.parentNode.insertBefore(n,e),n.appendChild(e),e=p.rename(e,m)),a=p,s=e,c=["margin","margin-right","margin-bottom","margin-left","margin-top","padding","padding-right","padding-bottom","padding-left","padding-top"],Pe.each(c,function(e){var n;return a.setStyle(s,((n={})[e]="",n))}),Xn(p,n,l),Jn(f.dom,n)}),f.selection.setRng(qn(e)))},Gn=function(e,n,t){return s=t,(a=n)&&s&&Fe(a)&&a.nodeName===s.nodeName&&(i=n,u=t,(o=e).getStyle(i,"list-style-type",!0)===o.getStyle(u,"list-style-type",!0))&&(r=t,n.className===r.className);var r,o,i,u,a,s},Jn=function(e,n){var t,r;if(t=n.nextSibling,Gn(e,n,t)){for(;r=t.firstChild;)n.appendChild(r);e.remove(t)}if(t=n.previousSibling,Gn(e,n,t)){for(;r=t.lastChild;)n.insertBefore(r,n.firstChild);e.remove(t)}},Zn=function(n,e,t,r,o){if(e.nodeName!==r||et(o)){var i=jn(n.selection.getRng(!0));Pe.each([e].concat(t),function(e){!function(e,n,t,r){if(n.nodeName!==t){var o=e.dom.rename(n,t);Xn(e.dom,o,r),Ie(e,Wn(t),o)}else Xn(e.dom,n,r),Ie(e,Wn(t),n)}(n,e,r,o)}),n.selection.setRng(qn(i))}else Fn(n)},et=function(e){return"list-style-type"in e},nt=function(e,n,t){var r=Ye(e),o=Ge(e);t=t||{},r&&0<o.length?Zn(e,r,o,n,t):function(e,n,t,r){if(n!==e.getBody())if(n)if(n.nodeName!==t||et(r)||Kn(n)){var o=jn(e.selection.getRng(!0));Xn(e.dom,n,r);var i=e.dom.rename(n,t);Jn(e.dom,i),e.selection.setRng(qn(o)),Ie(e,Wn(t),i)}else Fn(e);else Yn(e,t,r),Ie(e,Wn(t),n)}(e,r,n,t)},tt=An.DOM,rt=function(i,e){Pe.each(Pe.grep(i.select("ol,ul",e)),function(e){var n,t,r,o;n=i,"LI"===(o=(t=e).parentNode).nodeName&&o.firstChild===t&&((r=o.previousSibling)&&"LI"===r.nodeName?(r.appendChild(t),Xe(n,o)&&tt.remove(o)):tt.setStyle(o,"listStyleType","none")),Fe(o)&&(r=o.previousSibling)&&"LI"===r.nodeName&&r.appendChild(t)})},ot=function(e,n,t,r){var o=n.startContainer,i=n.startOffset;if(_e(o)&&(t?i<o.data.length:0<i))return o;var u=e.schema.getNonEmptyElements();1===o.nodeType&&(o=be.getNode(o,i));var a,s,c=new Ce(o,r);for(t&&(a=e.dom,Ve(s=o)&&a.isBlock(s.nextSibling)&&!Ve(s.previousSibling)&&c.next());o=c[t?"next":"prev2"]();){if("LI"===o.nodeName&&!o.hasChildNodes())return o;if(u[o.nodeName])return o;if(_e(o)&&0<o.data.length)return o}},it=function(e,n){var t=n.childNodes;return 1===t.length&&!Fe(t[0])&&e.isBlock(t[0])},ut=function(e,n,t){var r,o,i,u;if(o=it(e,t)?t.firstChild:t,it(i=e,u=n)&&i.remove(u.firstChild,!0),!Xe(e,n,!0))for(;r=n.firstChild;)o.appendChild(r)},at=function(n,e,t){var r,o,i=e.parentNode;if(ze(n,e)&&ze(n,t)){Fe(t.lastChild)&&(o=t.lastChild),i===t.lastChild&&Ve(i.previousSibling)&&n.remove(i.previousSibling),(r=t.lastChild)&&Ve(r)&&e.hasChildNodes()&&n.remove(r),Xe(n,t,!0)&&n.$(t).empty(),ut(n,e,t),o&&t.appendChild(o);var u=ye(ge.fromDom(t),ge.fromDom(e))?n.getParents(e,Fe,t):[];n.remove(e),k(u,function(e){Xe(n,e)&&e!==n.getRoot()&&n.remove(e)})}},st=function(e,n,t,r){var o,i,u,a=e.dom;if(a.isEmpty(r))i=t,u=r,(o=e).dom.$(u).empty(),at(o.dom,i,u),o.selection.setCursorLocation(u);else{var s=jn(n);at(a,t,r),e.selection.setRng(qn(s))}},ct=function(n,t){var e=n.dom,r=n.selection,o=r.getStart(),i=Ze(n,o),u=e.getParent(r.getStart(),"LI",i);if(u){var a=u.parentNode;if(a===n.getBody()&&Xe(e,a))return!0;var s=Pn(r.getRng()),c=e.getParent(ot(n,s,t,i),"LI",i);if(c&&c!==u)return n.undoManager.transact(function(){var e;t?st(n,s,c,u):(e=u).parentNode.firstChild===e?_n(n):function(e,n,t,r){var o=jn(n);at(e.dom,t,r);var i=qn(o);e.selection.setRng(i)}(n,s,u,c)}),!0;if(!c&&!t&&0===s.startOffset&&0===s.endOffset)return n.undoManager.transact(function(){Fn(n)}),!0}return!1},ft=function(o,i){var u=o.dom,e=o.selection.getStart(),a=Ze(o,e),s=u.getParent(e,u.isBlock,a);if(s&&u.isEmpty(s)){var n=Pn(o.selection.getRng()),c=u.getParent(ot(o,n,i,a),"LI",a);if(c)return o.undoManager.transact(function(){var e,n,t,r;n=s,t=a,r=(e=u).getParent(n.parentNode,e.isBlock,t),e.remove(n),r&&e.isEmpty(r)&&e.remove(r),Jn(u,c.parentNode),o.selection.select(c,!0),o.selection.collapse(i)}),!0}return!1},dt=function(e,n){return e.selection.isCollapsed()?ct(i=e,u=n)||ft(i,u):(r=(t=e).selection.getStart(),o=Ze(t,r),!!(t.dom.getParent(r,"LI,DT,DD",o)||0<Je(t).length)&&(t.undoManager.transact(function(){t.execCommand("Delete"),rt(t.dom,t.getBody())}),!0));var t,r,o,i,u},lt=function(t){var r=t.dom,e=Ye(t);He(e)&&t.windowManager.open({title:"List Properties",body:{type:"panel",items:[{type:"input",name:"start",label:"Start list at number",inputMode:"numeric"}]},initialData:{start:r.getAttrib(e,"start")||"1"},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],onSubmit:function(e){var n=e.getData();t.undoManager.transact(function(){r.setAttrib(Ye(t),"start","1"===n.start?"":n.start)}),e.close()}})},mt=function(n,t){return function(){var e=n.dom.getParent(n.selection.getStart(),"UL,OL,DL");return e&&e.nodeName===t}},pt=function(e){var n,t;e.getParam("lists_indent_on_tab",!0)&&(n=e).on("keydown",function(e){e.keyCode!==Oe.TAB||Oe.metaKeyPressed(e)||n.undoManager.transact(function(){(e.shiftKey?_n:Un)(n)&&e.preventDefault()})}),(t=e).on("keydown",function(e){e.keyCode===Oe.BACKSPACE?dt(t,!1)&&e.preventDefault():e.keyCode===Oe.DELETE&&dt(t,!0)&&e.preventDefault()})};!function gt(){f.add("lists",function(e){var t,n,r,o,i,u,a,s,c;return!1==!(!/(^|[ ,])rtc([, ]|$)/.test(e.settings.plugins)||!f.get("rtc"))&&(pt(e),(t=e).on("BeforeExecCommand",function(e){var n=e.command.toLowerCase();"indent"===n?Un(t):"outdent"===n&&_n(t)}),t.addCommand("InsertUnorderedList",function(e,n){nt(t,"UL",n)}),t.addCommand("InsertOrderedList",function(e,n){nt(t,"OL",n)}),t.addCommand("InsertDefinitionList",function(e,n){nt(t,"DL",n)}),t.addCommand("RemoveList",function(){Fn(t)}),t.addCommand("mceListProps",function(){lt(t)}),t.addQueryStateHandler("InsertUnorderedList",mt(t,"UL")),t.addQueryStateHandler("InsertOrderedList",mt(t,"OL")),t.addQueryStateHandler("InsertDefinitionList",mt(t,"DL"))),u=function(e){return function(){return n.execCommand(e)}},o="advlist",i=(r=n=e).settings.plugins?r.settings.plugins:"",-1===Pe.inArray(i.split(/[ ,]/),o)&&(n.ui.registry.addToggleButton("numlist",{icon:"ordered-list",active:!1,tooltip:"Numbered list",onAction:u("InsertOrderedList"),onSetup:function(e){return Vn(n,"OL",e.setActive)}}),n.ui.registry.addToggleButton("bullist",{icon:"unordered-list",active:!1,tooltip:"Bullet list",onAction:u("InsertUnorderedList"),onSetup:function(e){return Vn(n,"UL",e.setActive)}})),s={text:"List properties...",icon:"ordered-list",onAction:function(){return lt(a)},onSetup:function(n){return Vn(a,"OL",function(e){return n.setDisabled(!e)})}},(a=e).ui.registry.addMenuItem("listprops",s),a.ui.registry.addContextMenu("lists",{update:function(e){var n=Ye(a,e);return He(n)?["listprops"]:[]}}),c=e,{backspaceDelete:function(e){dt(c,e)}}})}()}(window);