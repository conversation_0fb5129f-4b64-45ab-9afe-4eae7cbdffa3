<?php

return [
    '[?]',    // 0x00
    '[?]',    // 0x01
    '[?]',    // 0x02
    '[?]',    // 0x03
    '[?]',    // 0x04
    '[?]',    // 0x05
    '[?]',    // 0x06
    '[?]',    // 0x07
    '[?]',    // 0x08
    '[?]',    // 0x09
    '[?]',    // 0x0a
    '[?]',    // 0x0b
    '[?]',    // 0x0c
    '[?]',    // 0x0d
    '[?]',    // 0x0e
    '[?]',    // 0x0f
    '[?]',    // 0x10
    '[?]',    // 0x11
    '[?]',    // 0x12
    '[?]',    // 0x13
    '[?]',    // 0x14
    '[?]',    // 0x15
    '[?]',    // 0x16
    '[?]',    // 0x17
    '[?]',    // 0x18
    '[?]',    // 0x19
    '[?]',    // 0x1a
    '[?]',    // 0x1b
    '[?]',    // 0x1c
    '[?]',    // 0x1d
    '[?]',    // 0x1e
    '[?]',    // 0x1f
    '[?]',    // 0x20
    '[?]',    // 0x21
    '[?]',    // 0x22
    '[?]',    // 0x23
    '[?]',    // 0x24
    '[?]',    // 0x25
    '[?]',    // 0x26
    '[?]',    // 0x27
    '[?]',    // 0x28
    '[?]',    // 0x29
    '[?]',    // 0x2a
    '[?]',    // 0x2b
    '[?]',    // 0x2c
    '[?]',    // 0x2d
    '[?]',    // 0x2e
    '[?]',    // 0x2f
    '[?]',    // 0x30
    'A',    // 0x31
    'B',    // 0x32
    'G',    // 0x33
    'D',    // 0x34
    'E',    // 0x35
    'Z',    // 0x36
    'E',    // 0x37
    'E',    // 0x38
    'T`',    // 0x39
    'Zh',    // 0x3a
    'I',    // 0x3b
    'L',    // 0x3c
    'Kh',    // 0x3d
    'Ts',    // 0x3e
    'K',    // 0x3f
    'H',    // 0x40
    'Dz',    // 0x41
    'Gh',    // 0x42
    'Ch',    // 0x43
    'M',    // 0x44
    'Y',    // 0x45
    'N',    // 0x46
    'Sh',    // 0x47
    'O',    // 0x48
    'Ch`',    // 0x49
    'P',    // 0x4a
    'J',    // 0x4b
    'Rh',    // 0x4c
    'S',    // 0x4d
    'V',    // 0x4e
    'T',    // 0x4f
    'R',    // 0x50
    'Ts`',    // 0x51
    'W',    // 0x52
    'P`',    // 0x53
    'K`',    // 0x54
    'O',    // 0x55
    'F',    // 0x56
    '[?]',    // 0x57
    '[?]',    // 0x58
    '<',    // 0x59
    '\'',    // 0x5a
    '/',    // 0x5b
    '!',    // 0x5c
    ',',    // 0x5d
    '?',    // 0x5e
    '.',    // 0x5f
    '[?]',    // 0x60
    'a',    // 0x61
    'b',    // 0x62
    'g',    // 0x63
    'd',    // 0x64
    'e',    // 0x65
    'z',    // 0x66
    'e',    // 0x67
    'e',    // 0x68
    't`',    // 0x69
    'zh',    // 0x6a
    'i',    // 0x6b
    'l',    // 0x6c
    'kh',    // 0x6d
    'ts',    // 0x6e
    'k',    // 0x6f
    'h',    // 0x70
    'dz',    // 0x71
    'gh',    // 0x72
    'ch',    // 0x73
    'm',    // 0x74
    'y',    // 0x75
    'n',    // 0x76
    'sh',    // 0x77
    'o',    // 0x78
    'ch`',    // 0x79
    'p',    // 0x7a
    'j',    // 0x7b
    'rh',    // 0x7c
    's',    // 0x7d
    'v',    // 0x7e
    't',    // 0x7f
    'r',    // 0x80
    'ts`',    // 0x81
    'w',    // 0x82
    'p`',    // 0x83
    'k`',    // 0x84
    'o',    // 0x85
    'f',    // 0x86
    'ew',    // 0x87
    '[?]',    // 0x88
    ':',    // 0x89
    '-',    // 0x8a
    '[?]',    // 0x8b
    '[?]',    // 0x8c
    '[?]',    // 0x8d
    '[?]',    // 0x8e
    '[?]',    // 0x8f
    '[?]',    // 0x90
    '',    // 0x91
    '',    // 0x92
    '',    // 0x93
    '',    // 0x94
    '',    // 0x95
    '',    // 0x96
    '',    // 0x97
    '',    // 0x98
    '',    // 0x99
    '',    // 0x9a
    '',    // 0x9b
    '',    // 0x9c
    '',    // 0x9d
    '',    // 0x9e
    '',    // 0x9f
    '',    // 0xa0
    '',    // 0xa1
    '',    // 0xa2
    '',    // 0xa3
    '',    // 0xa4
    '',    // 0xa5
    '',    // 0xa6
    '',    // 0xa7
    '',    // 0xa8
    '',    // 0xa9
    '',    // 0xaa
    '',    // 0xab
    '',    // 0xac
    '',    // 0xad
    '',    // 0xae
    '',    // 0xaf
    '@',    // 0xb0
    'e',    // 0xb1
    'a',    // 0xb2
    'o',    // 0xb3
    'i',    // 0xb4
    'e',    // 0xb5
    'e',    // 0xb6
    'a',    // 0xb7
    'a',    // 0xb8
    'o',    // 0xb9
    'o',    // 0xba
    'u',    // 0xbb
    '\'',    // 0xbc
    '',    // 0xbd
    '-',    // 0xbe
    '-',    // 0xbf
    '|',    // 0xc0
    '',    // 0xc1
    '',    // 0xc2
    ':',    // 0xc3
    '',    // 0xc4
    '',    // 0xc5
    'n',    // 0xc6
    'o',    // 0xc7
    '[?]',    // 0xc8
    '[?]',    // 0xc9
    '[?]',    // 0xca
    '[?]',    // 0xcb
    '[?]',    // 0xcc
    '[?]',    // 0xcd
    '[?]',    // 0xce
    '[?]',    // 0xcf
    'A',    // 0xd0
    'b',    // 0xd1
    'g',    // 0xd2
    'd',    // 0xd3
    'h',    // 0xd4
    'v',    // 0xd5
    'z',    // 0xd6
    'KH',    // 0xd7
    't',    // 0xd8
    'y',    // 0xd9
    'k',    // 0xda
    'k',    // 0xdb
    'l',    // 0xdc
    'm',    // 0xdd
    'm',    // 0xde
    'n',    // 0xdf
    'n',    // 0xe0
    's',    // 0xe1
    '`',    // 0xe2
    'p',    // 0xe3
    'p',    // 0xe4
    'TS',    // 0xe5
    'TS',    // 0xe6
    'q',    // 0xe7
    'r',    // 0xe8
    'SH',    // 0xe9
    't',    // 0xea
    '[?]',    // 0xeb
    '[?]',    // 0xec
    '[?]',    // 0xed
    '[?]',    // 0xee
    '[?]',    // 0xef
    'V',    // 0xf0
    'OY',    // 0xf1
    'i',    // 0xf2
    '\'',    // 0xf3
    '"',    // 0xf4
    'v',    // 0xf5
    'n',    // 0xf6
    'q',    // 0xf7
    '[?]',    // 0xf8
    '[?]',    // 0xf9
    '[?]',    // 0xfa
    '[?]',    // 0xfb
    '[?]',    // 0xfc
    '[?]',    // 0xfd
    '[?]',    // 0xfe
];
