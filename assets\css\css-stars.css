.br-theme-css-stars .br-widget {
    height: 28px;
    white-space: nowrap;
  }
  .br-theme-css-stars .br-widget a {
    text-decoration: none;
    height: 26px;
    width: 21px;
    font-size: 2.1rem;
    margin-right: 12px;
    display: inline-block;
    display: inline-block;
    font-family: "Ionicons";
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    text-rendering: auto;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .br-theme-css-stars .br-widget a:after {
    float: left;
    content: "\f4b3";
    color: #d2d2d2;
  }
  .br-theme-css-stars .br-widget a.br-active:after {
    color: #ffc120;
  }
  .br-theme-css-stars .br-widget a.br-selected:after {
    color: #ffc120;
  }
  .br-theme-css-stars .br-widget .br-current-rating {
    display: none;
  }
  .br-theme-css-stars .br-readonly a {
    cursor: default;
  }
  @media print {
    .br-theme-css-stars .br-widget a:after {
      content: "\f4b2";
      color: black;
    }
    .br-theme-css-stars .br-widget a.br-active:after,
    .br-theme-css-stars .br-widget a.br-selected:after {
      content: "\f4b3";
      color: black;
    }
  }
  