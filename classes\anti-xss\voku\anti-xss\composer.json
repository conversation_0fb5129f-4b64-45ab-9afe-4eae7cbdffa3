{"name": "voku/anti-xss", "description": "anti xss-library", "type": "library", "keywords": ["anti-xss", "clean", "security", "xss"], "homepage": "https://github.com/voku/anti-xss", "license": "MIT", "authors": [{"name": "EllisLab Dev Team", "homepage": "http://ellislab.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.moelleken.org/"}], "require": {"php": ">=7.0.0", "voku/portable-utf8": "~5.4.27"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0"}, "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}}