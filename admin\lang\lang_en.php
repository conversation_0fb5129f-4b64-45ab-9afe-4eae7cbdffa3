<?php

// Sections
define("_TYPES", "Types");
define("_STATUS", "Status");
define("_COUPONS", "Coupons");
define("_CATEGORIES", "Categories");
define("_SUBCATEGORIES", "Sub Categories");
define("_STORES", "Stores");
define("_PAGES", "Pages");
define("_USERS", "Users");
define("_LANGUAGES", "Languages");
define("_MENUS", "Menus");
define("_ADS", "Ads");
define("_THEME", "Theme");
define("_SETTINGS", "Settings");
define("_PROFILE", "Profile");
define("_SUBSCRIBERS", "Subscribers");
define("_COMMENTS", "Comments");
define("_SLIDERS", "Sliders");

//Table Tags
define("_TABLEFIELDID", "Id");
define("_TABLEFIELDTITLE", "Title");
define("_TABLEFIELDNAME", "Name");
define("_TABLEFIELDLOCATION", "Location");
define("_TABLEFIELDLANG", "Language");
define("_TABLEFIELDSTATUS", "Status");
define("_TABLEFIELDHEADER", "Header");
define("_TABLEFIELDFOOTER", "Footer");
define("_TABLEFIELDSIDEBAR", "Sidebar");
define("_TABLEFIELDMODAL", "Modal");
define("_TABLEFIELDIMAGE", "Image");
define("_TABLEFIELDSTORE", "Store");
define("_TABLEFIELDLOCATIONS", "Location");
define("_TABLEFIELDCONTENT", "Content");
define("_TABLEFIELDCATEGORY", "Category");
define("_TABLEFIELDFEATURED", "Featured");
define("_TABLEFIELDVISIBILITY", "Privacy");
define("_TABLEFIELDUSEREMAIL", "Email");
define("_TABLEFIELDUSERROLE", "Role");
define("_TABLEFIELDUSERNAME", "Name");
define("_TABLEFIELDPASSWORD", "Password");
define("_TABLEFIELDVERIFIED", "Verified");
define("_TABLEFIELDDATEREGISTER", "Date of registration:");
define("_TABLEFIELDDESCRIPTION", "Description");
define("_TABLEFIELDITEMSTATUS", "Status");
define("_TABLEFIELDACTIONS", "Actions");
define("_TABLEFIELDURLLINK", "Url/Link");
define("_TABLEFIELDPRICE", "Price");
define("_TABLEFIELDOLDPRICE", "Old Price");
define("_TABLEFIELDDISCOUNT", "Discount");
define("_TABLEFIELDSTART", "Start Date");
define("_TABLEFIELDEXPIRE", "Expire Date");
define("_TABLEFIELDSLUG", "Slug");
define("_TABLEFIELDAUTHOR", "Author");
define("_TABLEFIELDEXCLUSIVE", "Exclusive");
define("_TABLEFIELDVERIFY", "Verify");
define("_TABLEFIELDCODE", "Code");
define("_TABLEFIELDCREATED", "Created");
define("_TABLEFIELDTYPE", "Type");
define("_TABLEFIELDTAGLINE", "Tag Line");
define("_TABLEFIELDSUBCATEGORY", "Sub Category");
define("_TABLEFIELDICON", "Icon");
define("_TABLEFIELDPARENT", "Parent Category");
define("_TABLEFIELDSHOWMENU", "Show in menu");
define("_TABLEFIELDVIDEO", "Video (Youtube ID)");
define("_TABLEFIELDGALLERY", "Gallery (Max. 8)");

// General
define("_WELCOME", "Welcome,");
define("_VIEWSITE", "View Site");
define("_SECTIONES", "Sections");
define("_SUMMARY", "Summary");
define("_NAVIGATION", "Navigation");
define("_HREFTARGET", "Target");
define("_HREFLABEL", "Label");
define("_HREFURL", "Url");
define("_LANGDIRRTL", "RTL (Right-to-Left)");
define("_LANGDIRLTR", "LTR (Left-to-Right)");
define("_TRANSLATIONNOTCREATED", "The language translation file is not created.");
define("_TRANSLATIONCREATE", "Create Now");
define("_RECOMMENDEDSIZE", "Recommended Size:");
define("_CHOOSEFILE", "Choose File");
define("_DISABLEADBLOCK", "In case you don't see the ads please disable the Ad-Block.");

//Actions
define("_VIEWALL", "View All");
define("_EDITITEM", "Edit");
define("_REMOVEITEM", "Remove");
define("_DELETEITEM", "Delete");
define("_UPDATEITEM", "Update");
define("_ADDITEM", "Add New");
define("_SAVECHANGES", "Save Changes");
define("_CHANGESSAVED", "Changes Saved");
define("_NOITEMSFOUND", "No items found");
define("_ADDPAGE", "Add Page");
define("_ADDCUSTOMLINK", "Add Custom Link");
define("_ADDTRANSLATION", "Add translation");
define("_EDITTRANSLATION", "Edit translation");
define("_ENTERVALUE", "Enter Value");

//Status
define("_ITEMSTATUS", "Status");
define("_HIDDEN", "Hidden");
define("_VISIBLE", "Visible");
define("_ENABLED", "Enabled");
define("_DISABLED", "Disabled");
define("_PENDING", "Pending");
define("_ACTIVE", "Active");
define("_INACTIVE", "Inactive");
define("_PROCESSING", "Processing...");
define("_AREYOUSURE", "Are you sure?");
define("_YOUWILLNOT", "You will not be able to recover this item!");
define("_YESDELETEIT", "Yes, delete it!");
define("_CANCELBUTTONALERT", "Cancel");
define("_DUPLICATETITLE", "Loading...");
define("_DUPLICATETEXT", "Please Wait");
define("_DUPLICATEDONE", "Done");
define("_DUPLICATECOMPLETED", "Completed Successfully");
define("_YESTEXT", "Yes");
define("_NOTEXT", "No");
define("_PAGEPRIVATE", "Private");
define("_PAGEPUBLIC", "Public");
define("_PAGEHIDDEN", "Hidden");
define("_PAGEISDEFAULT", "Default Page");
define("_PAGETITLE", "Title");
define("_PAGETEMPLATE", "Template");
define("_PAGEVISIBILTY", "Visibility");
define("_PAGEFOOTER", "Show Footer");
define("_PAGEHEADERAD", "Show Header Ad");
define("_PAGEFOOTERAD", "Show Footer Ad");
define("_PAGESIDEBARAD", "Show Sidebar Ad");
define("_PAGEMODALAD", "Show Modal Ad");
define("_PAGECONTENT", "Content");
define("_PAGEBLANK", "Blank");
define("_PAGECONTACT", "Contact");
define("_PAGECOUPONS", "Coupons");
define("_PAGEABOUTUS", "About Us");
define("_PAGERECIPES", "Properties");
define("_PAGESEARCH", "Search");
define("_PAGELOCATIONS", "Locations");
define("_PAGESTORES", "Stores");
define("_PAGECATEGORIES", "Categories");
define("_PAGETERMSCONDITIONS", "Terms & Conditions");
define("_PAGEPRIVACYPOLICY", "Privacy Policy");
define("_SEO", "Seo");
define("_SEOTITLE", "Title");
define("_SEODESCRIPTION", "Description");
define("_PERMALINK", "Permalink:");
define("_CHANGESETTINGBTN", "Change Settings");
define("_CANTCHANGEPAGE", "You cannot change the type because this page is selected as the default");
define("_TRANSLATIONSITEM", "Translations");
define("_NOTRANSLATIONSFOUNDITEM", "No Translations");
define("_DUPLICATETRANSLATIONITEM", "Duplicate");
define("_EDITTRANSLATIONITEM", "Edit");
define("_DELETETRANSLATIONITEM", "Delete");
define("_DOWNLOAD", "Download");
define("_EXPORT", "Export");
define("_FINDCOORDINATES", "Find Coordinates");
define("_FOREXAMPLEMONTHLY", "For Example: Monthly");
define("_FILEUPLOADERCHOOSE", "Select");
define("_FILEUPLOADERSELECT", "Select files to upload");
define("_FILEUPLOADERYOUHAVE", "You have selected");
define("_FILEUPLOADERFILE", "file");
define("_FILEUPLOADERFILES", "files");
define("_FILEUPLOADERDELETEALERT", "Are you sure you want to delete this file?");
define("_AUTHORBY", "By:");
define("_PUBLISHED", "Published:");
define("_UPDATED", "Updated:");
define("_ACCESSDENIEDTITLE", "Access Denied");
define("_ACCESSDENIEDSUBTITLE", "You don't have permission to view this page");
define("_ERROR404", "Error 404");
define("_ERROR404TITLE", "Connection failed");
define("_ERROR404SUBTITLE", "please check your database settings");
define("_ERROR404BUTTON", "Try Again");
define("_TOTALITEMSBYUSER", "Total:");
define("_HOMEPAGE", "Home Page");

//Settings
define("_ADMINSETTINGS", "Admin Settings");
define("_SITESETTINGS", "Site Settings");
define("_MAINTENANCEMODE", "Maintenance Mode");
define("_DECIMALSEPARATOR", "Decimal Separator");
define("_DECIMALNUMBER", "Decimal Number");
define("_CURRENCYSYMBOL", "Currency Symbol");
define("_CURRENCYPOSITION", "Currency Position");
define("_DATEFORMAT", "Date Format");
define("_TIMEZONE", "Time Zone");
define("_LANGDIR", "Language Direction");
define("_DEFAULTPAGES", "Default Pages");
define("_COMPANYINFO", "Company Info");
define("_EMAIL", "Email");
define("_PHONE", "Phone");
define("_OFFICEADDRESS", "Office Address");
define("_SMTPEMAILS", "Smtp Configuration");
define("_RECIPIENTEMAIL", "Recipient Email");
define("_MESSAGERECIPIENTEMAIL", "The email in which you will receive the emails of the contact form");
define("_SMTPHOST", "Host");
define("_SMTPUSER", "Email");
define("_SMTPPASSWORD", "Password");
define("_SMTPENCRYPT", "Encrypt");
define("_SMTPPORT", "Port");
define("_LAYOUTSETTINGS", "Layout Settings");
define("_GENERALSETTINGS", "General");
define("_ANALYTICSTRACKINGCODE", "Analytics / Tracking Code");
define("_GOOGLERECAPTCHAKEY", "Google Recaptcha Key");
define("_GOOGLERECAPTCHASECRETKEY", "Google Recaptcha Secret Key");

//Translations
define("_GENERALTRANSLATIONS", "General");
define("_SEOMETATITLE", "Meta Title");
define("_SEOMETAKEYWORDS", "Meta Keywords");
define("_SEOMETADESCRIPTION", "Meta Description");
define("_MAINTENANCEMODEPAGETITLE", "Page Title");
define("_MAINTENANCEMODETITLE", "Title");
define("_MAINTENANCEMODESUBTITLE", "Subtitle");
define("_TRPROFILEPAGE", "Profile Page");
define("_TRSIGNINPAGE", "Sign In Page");
define("_TRSIGNUPPAGE", "Sign Up Page");
define("_TRRESETPAGE", "Reset Password Page");
define("_TRFORGOTPAGE", "Forgot Password Page");
define("_TRAPPCONTENT", "App");
define("_TRAPPTERMSANDCONDS", "Privacy Policy & Terms & Conditions");
define("_TRAPPABOUTUS", "About Us");
define("_TRERRORPAGE", "Error Page");
define("_TRERRORPAGETITLE", "Title");
define("_TRERRORPAGESUBTITLE", "Sub Title");
define("_TRERRORPAGETAGLINE", "Tag Line");
define("_TRERRORPAGEBUTTON", "Button");
define("_TOTALITEMS", "Total");

//Email Templates
define("_EMAILTEMPLATES", "Email Templates");
define("_EMAILTYPE", "Email Type");
define("_EMAILFROMNAME", "From Name");
define("_SENDASPLAINTEXT", "Send as plain text");
define("_EMAILDISABLE", "Disable this email from being sent");
define("_EMAILSUBJECT", "Subject");
define("_EMAILMESSAGE", "Message");
define("_EMAILFIELDS", "Available Fields");
define("_EMAILSENDTEST", "Send a Test Email");
define("_EMAILYOUMUSTSAVE", "You must save before sending a test email");
define("_EMAILSENDBUTTON", "Send Email");
define("_EMAILSENTSUCCESS", "Email Sent Successfully");
define("_EMAILFIELDLOGO", "Logo");
define("_EMAILFIELDSITEDOMAIN", "Site Domain");
define("_EMAILFIELDSITENAME", "Site Name");
define("_EMAILFIELDUSERNAME", "User Name");
define("_EMAILFIELDUSEREMAIL", "User Email");
define("_EMAILFIELDUSERPHONE", "User Phone");
define("_EMAILFIELDUSERMESSAGE", "User Message");
define("_EMAILFIELDSIGNINURL", "Sign In URL");
define("_EMAILFIELDTERMSURL", "Terms & Conditions URL");
define("_EMAILFIELDPRIVACYURL", "Privacy Policy URL");
define("_EMAILFIELDRECIPEURL", "Recipe URL");
define("_EMAILFIELDRECIPETITLE", "Recipe Title");
define("_EMAILFIELDRECIPEIMAGE", "Recipe Image");
define("_EMAILFIELDRESETURL", "Reset URL");
define("_EMAILFIELDRESETPASSREF", "Reset Password Reference");
define("_EMAILFIELDSENDERNAME", "Sender Name");
define("_EMAILFIELDSENDEREMAIL", "Sender Email");
define("_EMAILFIELDFRIENDEMAIL", "Friend Email");

// Login
define("_AUTHORCOPYRIGHT", "Wicombit");
define("_LOGINUSERNAME", "Username");
define("_LOGINPASSWORD", "Password");
define("_LOGINPCAPTCHA", "Enter Captcha");
define("_LOGINPCAPTCHACODE", "Captcha Code");
define("_LOGINBUTTON", "Log In");
define("_COPYRIGHTFOOTER", "All right reserved");
define("_LOGINREQUIREDFORM", "This field is required");
define("_LOGINACCESSDENIED", "Incorrect login data or access denied");
define("_LOGININVALIDTOKEN", "Invalid Token");
define("_LOGININVALIDCAPTCHA", "Invalid Captcha");

//Theme
define("_THCOLORS", "Colors");
define("_THCOLORPRIMARY", "Primary");
define("_THCOLORSSECONDARY", "Secondary");
define("_THLAYOUT", "Layout");
define("_THLAYOUTSTYLE", "Style");
define("_THHEADER", "Header");
define("_THHEADERMOBILE", "Header Mobile");
define("_THHOME", "Home");
define("_THLOGOS", "Logos");
define("_THLOGO", "Logo");
define("_THTRANSPARENTLOGO", "Transparent Logo");
define("_THFAVICON", "Favicon");
define("_THIMAGES", "Images");
define("_THHOMEBACKGROUND", "Home Background Image");
define("_THHOMEIMAGE", "Our Mision Image");

?>