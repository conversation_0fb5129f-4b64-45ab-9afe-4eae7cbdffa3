[{"name": "symfony/polyfill-iconv", "version": "v1.17.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "c4de7601eefbf25f9d47190abe07f79fe0a27424"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/c4de7601eefbf25f9d47190abe07f79fe0a27424", "reference": "c4de7601eefbf25f9d47190abe07f79fe0a27424", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-iconv": "For best performance"}, "time": "2020-05-12T16:47:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"]}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.17.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "e094b0770f7833fdf257e6ba4775be4e258230b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/e094b0770f7833fdf257e6ba4775be4e258230b2", "reference": "e094b0770f7833fdf257e6ba4775be4e258230b2", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-intl": "For best performance"}, "time": "2020-05-12T16:47:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"]}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.17.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "1357b1d168eb7f68ad6a134838e46b0b159444a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/1357b1d168eb7f68ad6a134838e46b0b159444a9", "reference": "1357b1d168eb7f68ad6a134838e46b0b159444a9", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-intl": "For best performance"}, "time": "2020-05-12T16:14:59+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"]}, {"name": "symfony/polyfill-mbstring", "version": "v1.17.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fa79b11539418b02fc5e1897267673ba2c19419c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fa79b11539418b02fc5e1897267673ba2c19419c", "reference": "fa79b11539418b02fc5e1897267673ba2c19419c", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2020-05-12T16:47:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"]}, {"name": "symfony/polyfill-php72", "version": "v1.17.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "f048e612a3905f34931127360bdd2def19a5e582"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/f048e612a3905f34931127360bdd2def19a5e582", "reference": "f048e612a3905f34931127360bdd2def19a5e582", "shasum": ""}, "require": {"php": ">=5.3.3"}, "time": "2020-05-12T16:47:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"]}, {"name": "voku/anti-xss", "version": "4.1.24", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/voku/anti-xss.git", "reference": "4c032aa1aedbf4934418520c61c00b8fad6ca8d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/anti-xss/zipball/4c032aa1aedbf4934418520c61c00b8fad6ca8d5", "reference": "4c032aa1aedbf4934418520c61c00b8fad6ca8d5", "shasum": ""}, "require": {"php": ">=7.0.0", "voku/portable-utf8": "~5.4.27"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0"}, "time": "2020-03-08T00:12:04+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "EllisLab Dev Team", "homepage": "http://ellislab.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.moelleken.org/"}], "description": "anti xss-library", "homepage": "https://github.com/voku/anti-xss", "keywords": ["anti-xss", "clean", "security", "xss"]}, {"name": "voku/portable-ascii", "version": "1.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "e7f9bd5deff09a57318f9b900ab33a05acfcf4d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/e7f9bd5deff09a57318f9b900ab33a05acfcf4d3", "reference": "e7f9bd5deff09a57318f9b900ab33a05acfcf4d3", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "time": "2020-05-26T06:40:44+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"]}, {"name": "voku/portable-utf8", "version": "5.4.45", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/voku/portable-utf8.git", "reference": "5445d44086ea7b53dcd6dd1b182ae5bdcb1fbb07"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-utf8/zipball/5445d44086ea7b53dcd6dd1b182ae5bdcb1fbb07", "reference": "5445d44086ea7b53dcd6dd1b182ae5bdcb1fbb07", "shasum": ""}, "require": {"php": ">=7.0.0", "symfony/polyfill-iconv": "~1.0", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.0", "voku/portable-ascii": "~1.5"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0"}, "suggest": {"ext-ctype": "Use Ctype for e.g. hexadecimal digit detection", "ext-fileinfo": "Use Fileinfo for better binary file detection", "ext-iconv": "Use iconv for best performance", "ext-intl": "Use Intl for best performance", "ext-json": "Use JSON for string detection", "ext-mbstring": "Use Mbstring for best performance"}, "time": "2020-05-26T07:35:55+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"voku\\": "src/voku/"}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["(Apache-2.0 or GPL-2.0)"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "http://pageconfig.com/"}, {"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable UTF-8 library - performance optimized (unicode) string functions for php.", "homepage": "https://github.com/voku/portable-utf8", "keywords": ["UTF", "clean", "php", "unicode", "utf-8", "utf8"]}]