<?php

return [
    'sseum',    // 0x00
    'sseub',    // 0x01
    'sseubs',    // 0x02
    'sseus',    // 0x03
    'sseuss',    // 0x04
    'sseung',    // 0x05
    'sseuj',    // 0x06
    'sseuc',    // 0x07
    'sseuk',    // 0x08
    'sseut',    // 0x09
    'sseup',    // 0x0a
    'sseuh',    // 0x0b
    'ssyi',    // 0x0c
    'ssyig',    // 0x0d
    'ssyigg',    // 0x0e
    'ssyigs',    // 0x0f
    'ssyin',    // 0x10
    'ssyinj',    // 0x11
    'ssyinh',    // 0x12
    'ssyid',    // 0x13
    'ssyil',    // 0x14
    'ssyilg',    // 0x15
    'ssyilm',    // 0x16
    'ssyilb',    // 0x17
    'ssyils',    // 0x18
    'ssyilt',    // 0x19
    'ssyilp',    // 0x1a
    'ssyilh',    // 0x1b
    'ssyim',    // 0x1c
    'ssyib',    // 0x1d
    'ssyibs',    // 0x1e
    'ssyis',    // 0x1f
    'ssyiss',    // 0x20
    'ssying',    // 0x21
    'ssyij',    // 0x22
    'ssyic',    // 0x23
    'ssyik',    // 0x24
    'ssyit',    // 0x25
    'ssyip',    // 0x26
    'ssyih',    // 0x27
    'ssi',    // 0x28
    'ssig',    // 0x29
    'ssigg',    // 0x2a
    'ssigs',    // 0x2b
    'ssin',    // 0x2c
    'ssinj',    // 0x2d
    'ssinh',    // 0x2e
    'ssid',    // 0x2f
    'ssil',    // 0x30
    'ssilg',    // 0x31
    'ssilm',    // 0x32
    'ssilb',    // 0x33
    'ssils',    // 0x34
    'ssilt',    // 0x35
    'ssilp',    // 0x36
    'ssilh',    // 0x37
    'ssim',    // 0x38
    'ssib',    // 0x39
    'ssibs',    // 0x3a
    'ssis',    // 0x3b
    'ssiss',    // 0x3c
    'ssing',    // 0x3d
    'ssij',    // 0x3e
    'ssic',    // 0x3f
    'ssik',    // 0x40
    'ssit',    // 0x41
    'ssip',    // 0x42
    'ssih',    // 0x43
    'a',    // 0x44
    'ag',    // 0x45
    'agg',    // 0x46
    'ags',    // 0x47
    'an',    // 0x48
    'anj',    // 0x49
    'anh',    // 0x4a
    'ad',    // 0x4b
    'al',    // 0x4c
    'alg',    // 0x4d
    'alm',    // 0x4e
    'alb',    // 0x4f
    'als',    // 0x50
    'alt',    // 0x51
    'alp',    // 0x52
    'alh',    // 0x53
    'am',    // 0x54
    'ab',    // 0x55
    'abs',    // 0x56
    'as',    // 0x57
    'ass',    // 0x58
    'ang',    // 0x59
    'aj',    // 0x5a
    'ac',    // 0x5b
    'ak',    // 0x5c
    'at',    // 0x5d
    'ap',    // 0x5e
    'ah',    // 0x5f
    'ae',    // 0x60
    'aeg',    // 0x61
    'aegg',    // 0x62
    'aegs',    // 0x63
    'aen',    // 0x64
    'aenj',    // 0x65
    'aenh',    // 0x66
    'aed',    // 0x67
    'ael',    // 0x68
    'aelg',    // 0x69
    'aelm',    // 0x6a
    'aelb',    // 0x6b
    'aels',    // 0x6c
    'aelt',    // 0x6d
    'aelp',    // 0x6e
    'aelh',    // 0x6f
    'aem',    // 0x70
    'aeb',    // 0x71
    'aebs',    // 0x72
    'aes',    // 0x73
    'aess',    // 0x74
    'aeng',    // 0x75
    'aej',    // 0x76
    'aec',    // 0x77
    'aek',    // 0x78
    'aet',    // 0x79
    'aep',    // 0x7a
    'aeh',    // 0x7b
    'ya',    // 0x7c
    'yag',    // 0x7d
    'yagg',    // 0x7e
    'yags',    // 0x7f
    'yan',    // 0x80
    'yanj',    // 0x81
    'yanh',    // 0x82
    'yad',    // 0x83
    'yal',    // 0x84
    'yalg',    // 0x85
    'yalm',    // 0x86
    'yalb',    // 0x87
    'yals',    // 0x88
    'yalt',    // 0x89
    'yalp',    // 0x8a
    'yalh',    // 0x8b
    'yam',    // 0x8c
    'yab',    // 0x8d
    'yabs',    // 0x8e
    'yas',    // 0x8f
    'yass',    // 0x90
    'yang',    // 0x91
    'yaj',    // 0x92
    'yac',    // 0x93
    'yak',    // 0x94
    'yat',    // 0x95
    'yap',    // 0x96
    'yah',    // 0x97
    'yae',    // 0x98
    'yaeg',    // 0x99
    'yaegg',    // 0x9a
    'yaegs',    // 0x9b
    'yaen',    // 0x9c
    'yaenj',    // 0x9d
    'yaenh',    // 0x9e
    'yaed',    // 0x9f
    'yael',    // 0xa0
    'yaelg',    // 0xa1
    'yaelm',    // 0xa2
    'yaelb',    // 0xa3
    'yaels',    // 0xa4
    'yaelt',    // 0xa5
    'yaelp',    // 0xa6
    'yaelh',    // 0xa7
    'yaem',    // 0xa8
    'yaeb',    // 0xa9
    'yaebs',    // 0xaa
    'yaes',    // 0xab
    'yaess',    // 0xac
    'yaeng',    // 0xad
    'yaej',    // 0xae
    'yaec',    // 0xaf
    'yaek',    // 0xb0
    'yaet',    // 0xb1
    'yaep',    // 0xb2
    'yaeh',    // 0xb3
    'eo',    // 0xb4
    'eog',    // 0xb5
    'eogg',    // 0xb6
    'eogs',    // 0xb7
    'eon',    // 0xb8
    'eonj',    // 0xb9
    'eonh',    // 0xba
    'eod',    // 0xbb
    'eol',    // 0xbc
    'eolg',    // 0xbd
    'eolm',    // 0xbe
    'eolb',    // 0xbf
    'eols',    // 0xc0
    'eolt',    // 0xc1
    'eolp',    // 0xc2
    'eolh',    // 0xc3
    'eom',    // 0xc4
    'eob',    // 0xc5
    'eobs',    // 0xc6
    'eos',    // 0xc7
    'eoss',    // 0xc8
    'eong',    // 0xc9
    'eoj',    // 0xca
    'eoc',    // 0xcb
    'eok',    // 0xcc
    'eot',    // 0xcd
    'eop',    // 0xce
    'eoh',    // 0xcf
    'e',    // 0xd0
    'eg',    // 0xd1
    'egg',    // 0xd2
    'egs',    // 0xd3
    'en',    // 0xd4
    'enj',    // 0xd5
    'enh',    // 0xd6
    'ed',    // 0xd7
    'el',    // 0xd8
    'elg',    // 0xd9
    'elm',    // 0xda
    'elb',    // 0xdb
    'els',    // 0xdc
    'elt',    // 0xdd
    'elp',    // 0xde
    'elh',    // 0xdf
    'em',    // 0xe0
    'eb',    // 0xe1
    'ebs',    // 0xe2
    'es',    // 0xe3
    'ess',    // 0xe4
    'eng',    // 0xe5
    'ej',    // 0xe6
    'ec',    // 0xe7
    'ek',    // 0xe8
    'et',    // 0xe9
    'ep',    // 0xea
    'eh',    // 0xeb
    'yeo',    // 0xec
    'yeog',    // 0xed
    'yeogg',    // 0xee
    'yeogs',    // 0xef
    'yeon',    // 0xf0
    'yeonj',    // 0xf1
    'yeonh',    // 0xf2
    'yeod',    // 0xf3
    'yeol',    // 0xf4
    'yeolg',    // 0xf5
    'yeolm',    // 0xf6
    'yeolb',    // 0xf7
    'yeols',    // 0xf8
    'yeolt',    // 0xf9
    'yeolp',    // 0xfa
    'yeolh',    // 0xfb
    'yeom',    // 0xfc
    'yeob',    // 0xfd
    'yeobs',    // 0xfe
    'yeos',    // 0xff
];
